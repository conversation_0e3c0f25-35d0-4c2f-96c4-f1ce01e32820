import 'package:json_annotation/json_annotation.dart';

part 'working_holiday_request.g.dart';

@JsonSerializable(explicitToJson: true)
class WorkingOnHolidayRequest{
  String employeeCode;
  int extraWorkPeriodId;
  int extraWorkRegisterId;
  int extraWorkId;
  String extraDate;
  int extraType;
  String workItems;
  String description;
  String contentWork;
  String extraAddress;
  String location;
  int page;
  int pageSize;
  int registerType;
  WorkingOnHolidayRequest({
    this.page,
    this.employeeCode,
    this.pageSize,
    this.extraDate,
    this.extraType,
    this.workItems,
    this.extraAddress,
    this.description,
    this.extraWorkPeriodId,
    this.extraWorkRegisterId,
    this.extraWorkId,
    this.contentWork,
    this.location,
    this.registerType,
});

  factory WorkingOnHolidayRequest.fromJson(Map<String, dynamic> json) =>
      _$WorkingOnHolidayRequestFromJson(json);

  Map<String, dynamic> toJson() => _$WorkingOnHolidayRequestToJson(this);
}