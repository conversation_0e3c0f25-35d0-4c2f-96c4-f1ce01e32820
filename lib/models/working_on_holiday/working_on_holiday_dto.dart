import 'package:json_annotation/json_annotation.dart';

part 'working_on_holiday_dto.g.dart';

@JsonSerializable(explicitToJson: true)
class WorkingOnHolidayDto {
  int extraWorkRegisterId;
  int extraWorkId;
  String employeeCode;
  String extraDate;
  String extraWorkDay;
  int extraType;
  String workItems;
  String description;
  String extraAddress;
  int registerStatus;
  String appName;
  String timeTypeApp;
  int status;
  int registerLock;
  int approveLock;
  int approveResultLock;

  /// result work
  String createdAt;
  int periodId;
  String periodName;
  String contentWork;
  int registerType;
  String rejectReason;
  String location;
  int approveBy;
  String approveName;
  String approveCode;
  String approvePhoneNumber;
  int woStatus;

  WorkingOnHolidayDto({
    this.employeeCode,
    this.description,
    this.extraAddress,
    this.extraDate,
    this.extraWorkDay,
    this.extraType,
    this.extraWorkRegisterId,
    this.workItems,
    this.registerStatus,
    this.appName,
    this.timeTypeApp,
    this.status,
    this.extraWorkId,
    this.createdAt,
    this.periodName,
    this.contentWork,
    this.periodId,
    this.registerType,
    this.rejectReason,
    this.location,
    this.approveBy,
    this.approveCode,
    this.approveName,
    this.approvePhoneNumber,
    this.approveLock,
    this.approveResultLock,
    this.registerLock,
    this.woStatus,
  });

  String getStatusWorkingHoliday() {
    switch (this.registerStatus) {
      case 1:
        return "Chờ TCLĐ đơn vị phê duyệt";
        break;
      case 2:
        return "Chờ NLĐ cập nhật công việc";
        break;
      case 3:
        return "TCLĐ đơn vị từ chối phê duyệt";
        break;
      case 4:
        return "TCLĐ yêu cầu bổ sung thông tin";
        break;
      case 5:
        return "Chờ trình ký chỉ huy đơn vị";
        break;
      case 6:
        return "Đang trình ký chỉ huy đơn vị";
        break;
      case 7:
        return "Chỉ huy đơn vị đã ký";
        break;
      case 8:
        return "Chỉ huy đơn vị từ chối ký";
        break;
    }
    return "";
  }

  String getStatusResultWorkingHoliday() {
    switch (this.status) {
      case 0:
        return "Không tham gia trực";
        break;
      case 1:
        return "Chờ NLĐ cập nhật công việc";
        break;
      case 2:
        return "Chờ TCLĐ phê duyệt";
      case 3:
        return "TCLĐ đơn vị từ chối phê duyệt";
        break;
      case 4:
        return "TCLĐ yêu cầu bổ sung thông tin";
        break;
      case 5:
        return "Chờ trình ký chỉ huy đơn vị";
      case 6:
        return "Đang trình ký chỉ huy đơn vị";
        break;
      case 7:
        return "Chỉ huy đơn vị đã ký";
        break;
      case 8:
        return "Chỉ huy đơn vị từ chối ký";
        break;
    }
    return "";
  }

  factory WorkingOnHolidayDto.fromJson(Map<String, dynamic> json) =>
      _$WorkingOnHolidayDtoFromJson(json);

  Map<String, dynamic> toJson() => _$WorkingOnHolidayDtoToJson(this);
}
