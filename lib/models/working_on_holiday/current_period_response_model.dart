import 'package:json_annotation/json_annotation.dart';

part 'current_period_response_model.g.dart';

@JsonSerializable()
class CurrentPeriodResponseModel {
  String message;
  int status;
  CurrentPeriodModel result;

  CurrentPeriodResponseModel({
    this.result,
    this.message,
    this.status,
  });

  factory CurrentPeriodResponseModel.fromJson(Map<String, dynamic> json) =>
      _$CurrentPeriodResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$CurrentPeriodResponseModelToJson(this);
}

@JsonSerializable()
class CurrentPeriodModel {
  @Json<PERSON><PERSON>(name: "extraWorkPeriodId")
  int extraWorkPeriodId;
  @Json<PERSON><PERSON>(name: "delFlag")
  bool delFlag;
  @JsonKey(name: "createdAt")
  String createdAt;
  @JsonKey(name: "createdBy")
  int createdBy;
  @Json<PERSON><PERSON>(name: "createdName")
  String createdName;
  @Json<PERSON>ey(name: "updatedAt")
  String updatedAt;
  @Json<PERSON><PERSON>(name: "updatedName")
  String updatedName;
  @Json<PERSON><PERSON>(name: "updatedBy")
  int updatedBy;
  @Json<PERSON>ey(name: "periodName")
  String periodName;
  @JsonKey(name: "startDate")
  String startDate;
  @JsonKey(name: "endDate")
  String endDate;
  @JsonKey(name: "approveLock")
  int approveLock;
  @JsonKey(name: "registerAmount")
  dynamic registerAmount;
  @JsonKey(name: "approveAmount")
  dynamic approveAmount;
  @JsonKey(name: "registerLock")
  int registerLock;
  @JsonKey(name: "approveResultLock")
  int approveResultLock;


  CurrentPeriodModel({
    this.extraWorkPeriodId,
    this.delFlag,
    this.createdAt,
    this.createdBy,
    this.createdName,
    this.updatedAt,
    this.updatedName,
    this.updatedBy,
    this.periodName,
    this.startDate,
    this.endDate,
    this.approveLock,
    this.registerAmount,
    this.approveAmount,
    this.registerLock,
    this.approveResultLock,
  });

  factory CurrentPeriodModel.fromJson(Map<String, dynamic> json) =>
      _$CurrentPeriodModelFromJson(json);

  Map<String, dynamic> toJson() => _$CurrentPeriodModelToJson(this);
}
