import 'dart:typed_data';

class FileData {
  String filePath;
  String encodePath;
  String fileName;
  String type;
  int size;
  String data;
  String downloadFileExtension;

  FileData({
    this.filePath,
    this.encodePath,
    this.fileName,
    this.type,
    this.size,
    this.data,
    this.downloadFileExtension,
  });

  FileData.fromJson(Map<String, dynamic> json) {
    filePath = json['filePath'];
    encodePath = json['encodePath'];
    fileName = json['fileName'];
    type = json['type'];
    size = json['size'];
    data = json['data'];
    downloadFileExtension = json['downloadFileExtension'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['filePath'] = this.filePath;
    data['encodePath'] = this.encodePath;
    data['fileName'] = this.fileName;
    data['type'] = this.type;
    data['size'] = this.size;
    data['data'] = this.data;
    data['downloadFileExtension'] = this.downloadFileExtension;
    return data;
  }
}

class FileUploadData {
  String title;
  String url;
  String absoluteUrl;
  String encodeUrl;
  int size;
  String type;
  String oldFileName;

  FileUploadData(
      {this.title,
      this.url,
      this.absoluteUrl,
      this.encodeUrl,
      this.size,
      this.type,
      this.oldFileName});

  FileUploadData.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    url = json['url'];
    absoluteUrl = json['absoluteUrl'];
    encodeUrl = json['encodeUrl'];
    size = json['size'];
    type = json['type'];
    oldFileName = json['oldFileName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['url'] = this.url;
    data['absoluteUrl'] = this.absoluteUrl;
    data['encodeUrl'] = this.encodeUrl;
    data['size'] = this.size;
    data['type'] = this.type;
    data['oldFileName'] = this.oldFileName;
    return data;
  }
}

class FileUploadRequest {
  Uint8List fileUnit;

  String fileName;

  FileUploadRequest({this.fileUnit, this.fileName});

  FileUploadRequest.fromJson(Map<String, dynamic> json) {
    fileUnit = json['fileUnit'];
    fileName = json['fileName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['fileUnit'] = this.fileUnit;
    data['fileName'] = this.fileName;
    return data;
  }
}
