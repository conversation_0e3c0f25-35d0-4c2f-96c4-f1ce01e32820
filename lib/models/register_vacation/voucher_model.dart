import 'package:json_annotation/json_annotation.dart';

part 'voucher_model.g.dart';

@JsonSerializable()
class VoucherModel {
  @Json<PERSON><PERSON>(name: "id")
  int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: "travelRegisterId")
  int travelRegisterId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "codeVoucher")
  String codeVoucher;
  @<PERSON><PERSON><PERSON><PERSON>(name: "number<PERSON>erson")
  int numberPerson;
  @<PERSON>son<PERSON><PERSON>(name: "numberChildren")
  int numberChildren;
  @<PERSON><PERSON><PERSON><PERSON>(name: "statusPerson")
  dynamic statusPerson;
  @<PERSON><PERSON><PERSON><PERSON>(name: "statusChildren")
  dynamic statusChildren;
  @<PERSON><PERSON><PERSON><PERSON>(name: "filePathFont")
  String filePathFont;
  @<PERSON><PERSON><PERSON><PERSON>(name: "filePathBack")
  String filePathBack;
  String filePathFontName;
  String filePathBackName;
  @<PERSON><PERSON><PERSON><PERSON>(name: "listDetail")
  List<ListDetail> listDetail;
  bool verifyNumberPerson;
  int bedType;
  String base64Font;
  String base64Back;
  String checkInName;
  String checkInPhone;

  VoucherModel({
    this.id,
    this.travelRegisterId,
    this.codeVoucher,
    this.numberPerson,
    this.numberChildren,
    this.statusPerson,
    this.statusChildren,
    this.filePathBack,
    this.filePathFont,
    this.listDetail,
    this.verifyNumberPerson,
    this.filePathBackName,
    this.filePathFontName,
    this.bedType,
    this.base64Back,
    this.base64Font,
    this.checkInName,
    this.checkInPhone,
  });

  factory VoucherModel.fromJson(Map<String, dynamic> json) =>
      _$VoucherModelFromJson(json);

  Map<String, dynamic> toJson() => _$VoucherModelToJson(this);
}

@JsonSerializable()
class ListDetail {
  @JsonKey(name: "id")
  int id;
  @JsonKey(name: "travelRegisterVoucherId")
  int travelRegisterVoucherId;
  @JsonKey(name: "birthYear")
  int birthYear;
  @JsonKey(name: "isOverOld")
  dynamic isOverOld;
  bool isSatisfyVoucher;

  ListDetail({
    this.id,
    this.travelRegisterVoucherId,
    this.birthYear,
    this.isOverOld,
    this.isSatisfyVoucher,
  });

  factory ListDetail.fromJson(Map<String, dynamic> json) =>
      _$ListDetailFromJson(json);

  Map<String, dynamic> toJson() => _$ListDetailToJson(this);
}
