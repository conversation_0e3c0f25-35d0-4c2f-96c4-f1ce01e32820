import 'package:flutter/material.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/models/register_vacation/voucher_model.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'list_register_vacation_response_model.g.dart';

@JsonSerializable()
class ListRegisterVacationResponseModel {
  @JsonKey(name: "status")
  int status;
  @JsonKey(name: "message")
  String message;
  @JsonKey(name: "errors")
  List<dynamic> errors;
  @Json<PERSON>ey(name: "result")
  List<VacationModel> result;

  ListRegisterVacationResponseModel({
    this.status,
    this.message,
    this.errors,
    this.result,
  });

  factory ListRegisterVacationResponseModel.fromJson(
          Map<String, dynamic> json) =>
      _$ListRegisterVacationResponseModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$ListRegisterVacationResponseModelToJson(this);
}

@JsonSerializable()
class VacationModel {
  @Json<PERSON>ey(name: "id")
  int id;
  @JsonKey(name: "createdAt")
  String createdAt;
  @JsonKey(name: "createdBy")
  int createdBy;
  @JsonKey(name: "createdName")
  String createdName;
  @JsonKey(name: "updatedAt")
  String updatedAt;
  @JsonKey(name: "updatedBy")
  int updatedBy;
  @JsonKey(name: "updatedName")
  String updatedName;
  @JsonKey(name: "employeeCode")
  String employeeCode;
  @JsonKey(name: "employeeName")
  String employeeName;
  @JsonKey(name: "employeePhone")
  dynamic employeePhone;
  @JsonKey(name: "employeeEmail")
  dynamic employeeEmail;
  @JsonKey(name: "positionId")
  int positionId;
  @JsonKey(name: "positionCode")
  String positionCode;
  @JsonKey(name: "positionName")
  String positionName;
  @JsonKey(name: "unitId")
  int unitId;
  @JsonKey(name: "unitCode")
  String unitCode;
  @JsonKey(name: "unitName")
  String unitName;
  @JsonKey(name: "departmentId")
  int departmentId;
  @JsonKey(name: "departmentCode")
  String departmentCode;
  @JsonKey(name: "departmentName")
  String departmentName;
  @JsonKey(name: "periodId")
  int periodId;
  @JsonKey(name: "travelYear")
  int travelYear;
  @JsonKey(name: "locationStatus")
  int locationStatus;
  @JsonKey(name: "travelMonth")
  int travelMonth;
  @JsonKey(name: "startDate")
  String startDate;
  @JsonKey(name: "endDate")
  String endDate;
  @JsonKey(name: "location")
  String location;
  @JsonKey(name: "timeStatus")
  dynamic timeStatus;
  @JsonKey(name: "approveStatus")
  dynamic approveStatus;
  @JsonKey(name: "approveBy")
  dynamic approveBy;
  @JsonKey(name: "approveCode")
  dynamic approveCode;
  @JsonKey(name: "approveName")
  dynamic approveName;
  @JsonKey(name: "rejectReason")
  dynamic rejectReason;
  @JsonKey(name: "registerLock")
  int registerLock;
  @JsonKey(name: "registerEndDate")
  String registerEndDate;
  @JsonKey(name: "noRegisterReason")
  dynamic noRegisterReason;
  @JsonKey(name: "periodDetailId")
  int periodDetailId;
  @JsonKey(name: "periodName")
  String periodName;
  @JsonKey(name: "voucherStatus")
  dynamic voucherStatus;
  @JsonKey(name: "latchData")
  dynamic latchData;
  @JsonKey(name: "standardLevel")
  int standardLevel;
  @JsonKey(name: "listDetail")
  List<LocationVacation> listDetail;
  int registerId;
  int numberPerson;
  int numberChildSix;
  int numberChildTwelve;
  List<VoucherModel> listVoucher;
  String filePath;
  String locationRegisterAt;
  String timeRegisterAt;
  String approvePhone;
  int countVoucher;

  VacationModel({
    this.id,
    this.createdAt,
    this.createdBy,
    this.createdName,
    this.updatedAt,
    this.updatedBy,
    this.updatedName,
    this.employeeCode,
    this.employeeName,
    this.employeePhone,
    this.employeeEmail,
    this.positionId,
    this.positionCode,
    this.positionName,
    this.unitId,
    this.unitCode,
    this.unitName,
    this.departmentId,
    this.departmentCode,
    this.departmentName,
    this.periodId,
    this.travelYear,
    this.locationStatus,
    this.travelMonth,
    this.startDate,
    this.endDate,
    this.location,
    this.timeStatus,
    this.approveStatus,
    this.approveBy,
    this.approveCode,
    this.approveName,
    this.rejectReason,
    this.registerLock,
    this.registerEndDate,
    this.noRegisterReason,
    this.periodDetailId,
    this.periodName,
    this.latchData,
    this.listDetail,
    this.standardLevel,
    this.voucherStatus,
    this.registerId,
    this.listVoucher,
    this.numberChildTwelve,
    this.numberChildSix,
    this.numberPerson,
    this.filePath,
    this.locationRegisterAt,
    this.timeRegisterAt,
    this.approvePhone,
    this.countVoucher,
  });

  factory VacationModel.fromJson(Map<String, dynamic> json) =>
      _$VacationModelFromJson(json);

  Map<String, dynamic> toJson() => _$VacationModelToJson(this);

  DateTime get registerEndDateTime {
    var inputFormat = DateFormat('dd/MM/yyyy');
    var inputDate = inputFormat.parse(this.registerEndDate);
    return inputDate;
  }

  String get vacationStatusStr {
    if (this.registerLock == 1) {
      return "Đã khóa đợt đăng ký";
    } else {
      if (this.locationStatus == 0) {
        return "Chưa đăng ký";
      }
      if (this.locationStatus == 1 && this.timeStatus == null) {
        return "Đã đăng ký địa điểm";
      }
      if (this.locationStatus == 2) {
        if (this.approveStatus == 0) {
          return "Chờ TCLĐ phê duyệt";
        }
        if (this.approveStatus == 1) {
          return "P.TCLĐ phê duyệt";
        }
        if (this.approveStatus == 2) {
          return "P.TCLĐ từ chối duyệt";
        }
      }
      if (this.timeStatus == 0) {
        return "Chưa đăng ký thời gian nghỉ dưỡng";
      }
      if (this.timeStatus == 1) {
        return "Đã đăng ký thời gian nghỉ dưỡng";
      }
      if (this.timeStatus == 2) {
        return "Đã gửi email cho khách sạn";
      }
      if (this.approveStatus == 0) {
        return "Chờ duyệt";
      }
      if (this.approveStatus == 1) {
        return "Đã duyệt";
      }
      if (this.approveStatus == 2) {
        return "Từ chối duyệt";
      } else {
        return "";
      }
    }
  }

  Color get vacationStatusTextColor {
    if (this.registerLock == 1) {
      return Colors.black54;
    } else {
      if (this.locationStatus == 0) {
        return Colors.orange;
      }
      if (this.locationStatus == 1 && this.timeStatus == null) {
        return Colors.green;
      }
      if (this.locationStatus == 2) {
        if (this.approveStatus == 0) {
          return Colors.orange;
        }
        if (this.approveStatus == 1) {
          return Colors.green;
        }
        if (this.approveStatus == 2) {
          return AppColors.primaryColor;
        }
      }
      if (this.timeStatus == 0) {
        return Colors.orange;
      }
      if (this.timeStatus == 1) {
        return Colors.green;
      }
      if (this.timeStatus == 2) {
        return Colors.green;
      }
      if (this.approveStatus == 0) {
        return Colors.orange;
      }
      if (this.approveStatus == 1) {
        return Colors.green;
      }
      if (this.approveStatus == 2) {
        return AppColors.primaryColor;
      } else {
        return null;
      }
    }
  }

  Color get vacationStatusBackgroundColor {
    if (this.registerLock == 1) {
      return AppColors.greyButtonColor;
    } else {
      if (this.locationStatus == 0) {
        return AppColors.warringSurfaceColor;
      }
      if (this.locationStatus == 1 && this.timeStatus == null) {
        return AppColors.successSurfaceColor;
      }
      if (this.locationStatus == 2) {
        if (this.approveStatus == 0) {
          return AppColors.warringSurfaceColor;
        }
        if (this.approveStatus == 1) {
          return AppColors.successSurfaceColor;
        }
        if (this.approveStatus == 2) {
          return AppColors.errorSurfaceColor;
        }
      }
      if (this.timeStatus == 0) {
        return AppColors.warringSurfaceColor;
      }
      if (this.timeStatus == 1) {
        return AppColors.successSurfaceColor;
      }
      if (this.timeStatus == 2) {
        return AppColors.successSurfaceColor;
      }
      if (this.approveStatus == 0) {
        return AppColors.warringSurfaceColor;
      }
      if (this.approveStatus == 1) {
        return AppColors.successSurfaceColor;
      }
      if (this.approveStatus == 2) {
        return AppColors.errorSurfaceColor;
      } else {
        return null;
      }
    }
  }
}

@JsonSerializable()
class LocationVacation {
  int id;
  int periodId;
  int voucherId;
  int levelVoucher;
  int remainAmount;
  String hotelName;
  String hotelAddress;
  String hotelEmail;

  LocationVacation(
      {this.periodId,
      this.id,
      this.hotelAddress,
      this.hotelEmail,
      this.hotelName,
      this.levelVoucher,
      this.remainAmount,
      this.voucherId});

  factory LocationVacation.fromJson(Map<String, dynamic> json) =>
      _$LocationVacationFromJson(json);

  Map<String, dynamic> toJson() => _$LocationVacationToJson(this);
}
