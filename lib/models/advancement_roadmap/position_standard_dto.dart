import 'package:hstd/models/advancement_roadmap/option_dto.dart';
import 'package:json_annotation/json_annotation.dart';

part 'position_standard_dto.g.dart';

@JsonSerializable()
class PositionStandardDto {
  @JsonKey()
  final int id;
  final String positionStandardCode;
  final String positionStandardName;
  final int unitId;
  final String unitName;
  final int departmentId;
  final String departmentName;
  final String positionSapName;
  final List<int> positionSapIds;
  final List<OptionDto> options;
  final int positionStandardNowId;
  final String positionStandardNowName;

  PositionStandardDto({
    this.id,
    this.positionStandardName,
    this.positionStandardCode,
    this.unitName,
    this.unitId,
    this.departmentName,
    this.departmentId,
    this.positionSapName,
    this.positionSapIds,
    this.options,
    this.positionStandardNowId,
    this.positionStandardNowName,
  });

  factory PositionStandardDto.fromJson(Map<String, dynamic> json) =>
      _$PositionStandardDtoFromJson(json);

  Map<String, dynamic> toJson() => _$PositionStandardDtoToJson(this);

}