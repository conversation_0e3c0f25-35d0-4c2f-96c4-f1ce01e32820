import 'package:hstd/models/advancement_roadmap/process_career_detail_dto.dart';

/// positionStandardIdNow : 902
/// positionStandardNameNow : "TBA"
/// positionStandardIdTarget : 853
/// positionStandardNameTarget : "a Trung tâm2"
/// id : 852
/// details : [{"orderValue":1,"positionStandardCode":"BA","positionStandardName":"BA","positionStandardId":902}]
/// isRegisted : true
/// order : 1

class RegisterRoadmapDto {
  RegisterRoadmapDto({
      int positionStandardIdNow, 
      String positionStandardNameNow, 
      int positionStandardIdTarget, 
      String positionStandardNameTarget, 
      int id, 
      List<ProcessCareerDetailDto> details,
      bool isRegisted, 
      int order,}){
    _positionStandardIdNow = positionStandardIdNow;
    _positionStandardNameNow = positionStandardNameNow;
    _positionStandardIdTarget = positionStandardIdTarget;
    _positionStandardNameTarget = positionStandardNameTarget;
    _id = id;
    _details = details;
    _isRegisted = isRegisted;
    _order = order;
}

  RegisterRoadmapDto.fromJson(dynamic json) {
    _positionStandardIdNow = json['positionStandardIdNow'];
    _positionStandardNameNow = json['positionStandardNameNow'];
    _positionStandardIdTarget = json['positionStandardIdTarget'];
    _positionStandardNameTarget = json['positionStandardNameTarget'];
    _id = json['id'];
    if (json['details'] != null) {
      _details = [];
      json['details'].forEach((v) {
        _details.add(ProcessCareerDetailDto.fromJson(v));
      });
    }
    _isRegisted = json['isRegisted'];
    _order = json['order'];
  }
  int _positionStandardIdNow;
  String _positionStandardNameNow;
  int _positionStandardIdTarget;
  String _positionStandardNameTarget;
  int _id;
  List<ProcessCareerDetailDto> _details;
  bool _isRegisted;
  int _order;

  int get positionStandardIdNow => _positionStandardIdNow;
  String get positionStandardNameNow => _positionStandardNameNow;
  int get positionStandardIdTarget => _positionStandardIdTarget;
  String get positionStandardNameTarget => _positionStandardNameTarget;
  int get id => _id;
  List<ProcessCareerDetailDto> get details => _details;
  bool get isRegisted => _isRegisted;
  int get order => _order;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['positionStandardIdNow'] = _positionStandardIdNow;
    map['positionStandardNameNow'] = _positionStandardNameNow;
    map['positionStandardIdTarget'] = _positionStandardIdTarget;
    map['positionStandardNameTarget'] = _positionStandardNameTarget;
    map['id'] = _id;
    if (_details != null) {
      map['details'] = _details.map((v) => v.toJson()).toList();
    }
    map['isRegisted'] = _isRegisted;
    map['order'] = _order;
    return map;
  }

}