import 'package:json_annotation/json_annotation.dart';

part 'process_career_detail_dto.g.dart';

@JsonSerializable()
class ProcessCareerDetailDto {
  @Json<PERSON>ey()
  final int positionStandardId;
  @J<PERSON><PERSON>ey()
  final String positionStandardName;
  @J<PERSON><PERSON>ey()
  final String positionStandardCode;
  @JsonKey()
  final int orderValue;

  bool isSelected;

  ProcessCareerDetailDto(
      {this.positionStandardId,
      this.positionStandardName,
      this.positionStandardCode,
      this.orderValue,
      this.isSelected});

  factory ProcessCareerDetailDto.fromJson(Map<String, dynamic> json) =>
      _$ProcessCareerDetailDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ProcessCareerDetailDtoToJson(this);
}
