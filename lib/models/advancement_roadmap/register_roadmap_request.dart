import 'package:hstd/models/advancement_roadmap/process_career_detail_dto.dart';

/// positionStandardIdNow : 356
/// positionStandardIdTarget : 354
/// processCareerId : 271
/// details : [{"orderValue":2,"positionStandardName":"Trưởng ban <PERSON>ế hoạch nguồn nhân lực","positionStandardId":356,"positionStandardCode":"IIID3"},{"orderValue":3,"positionStandardName":"Phó phòng PT tuyển dụng, đào tạo","positionStandardId":353,"positionStandardCode":"IIIF1"},{"orderValue":4,"positionStandardName":"Nhân viên Quản trị thành tích","positionStandardId":362,"positionStandardCode":"IIIA5"},{"orderValue":5,"positionStandardName":"<PERSON><PERSON> phòng PT tiền lương, chính sách","positionStandardId":354,"positionStandardCode":"IIIF2"}]

class RegisterRoadmapRequest {
  RegisterRoadmapRequest({
      int positionStandardIdNow, 
      int positionStandardIdTarget, 
      int processCareerId, 
      List<ProcessCareerDetailDto> details,}){
    _positionStandardIdNow = positionStandardIdNow;
    _positionStandardIdTarget = positionStandardIdTarget;
    _processCareerId = processCareerId;
    _details = details;
}

  RegisterRoadmapRequest.fromJson(dynamic json) {
    _positionStandardIdNow = json['positionStandardIdNow'];
    _positionStandardIdTarget = json['positionStandardIdTarget'];
    _processCareerId = json['processCareerId'];
    if (json['details'] != null) {
      _details = [];
      json['details'].forEach((v) {
        _details.add(ProcessCareerDetailDto.fromJson(v));
      });
    }
  }
  int _positionStandardIdNow;
  int _positionStandardIdTarget;
  int _processCareerId;
  List<ProcessCareerDetailDto> _details;

  int get positionStandardIdNow => _positionStandardIdNow;
  int get positionStandardIdTarget => _positionStandardIdTarget;
  int get processCareerId => _processCareerId;
  List<ProcessCareerDetailDto> get details => _details;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['positionStandardIdNow'] = _positionStandardIdNow;
    map['positionStandardIdTarget'] = _positionStandardIdTarget;
    map['processCareerId'] = _processCareerId;
    if (_details != null) {
      map['details'] = _details.map((v) => v.toJson()).toList();
    }
    return map;
  }

}