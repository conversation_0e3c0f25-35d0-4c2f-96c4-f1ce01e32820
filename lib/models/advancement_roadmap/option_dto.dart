import 'package:hstd/models/advancement_roadmap/value_option.dart';
import 'package:json_annotation/json_annotation.dart';

part 'option_dto.g.dart';

@JsonSerializable()
class OptionDto {
  @J<PERSON><PERSON>ey()
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>()
  final bool delFlag;
  @<PERSON><PERSON><PERSON><PERSON>()
  final bool isActive;
  @Json<PERSON>ey()
  final String optionCode;
  @J<PERSON><PERSON>ey()
  final int positionStandardId;
  @Json<PERSON>ey()
  final int optionType;
  @Json<PERSON>ey()
  final String value;
  @Json<PERSON><PERSON>()
  final String name;
  @J<PERSON><PERSON><PERSON>()
  final String code;
  @Json<PERSON>ey()
  final int type;
  @<PERSON><PERSON><PERSON><PERSON>()
  final int order;
  @J<PERSON><PERSON>ey()
  final bool fullRow;

  List<ValueOption> listValue;

  OptionDto({
   this.id,
    this.delFlag,
    this.isActive,
    this.optionCode,
    this.positionStandardId,
    this.optionType,
    this.value,
    this.name,
    this.code,
    this.type,
    this.order,
    this.fullRow,
    this.listValue,
  });

  factory OptionDto.fromJson(Map<String, dynamic> json) =>
      _$OptionDtoFromJson(json);

  Map<String, dynamic> toJson() => _$OptionDtoToJson(this);

}