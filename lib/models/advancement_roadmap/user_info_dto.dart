import 'package:json_annotation/json_annotation.dart';

part 'user_info_dto.g.dart';

@JsonSerializable()
class AdRoadUserInfoDto {
  @J<PERSON><PERSON>ey()
  final String employeeCode;
  @<PERSON><PERSON><PERSON><PERSON>()
  final String employeeName;
  @<PERSON><PERSON><PERSON><PERSON>()
  final String positionName;
  @Json<PERSON><PERSON>()
  final String departmentName;
  @Json<PERSON>ey()
  final String unitName;
  @J<PERSON><PERSON>ey()
  final String joinCompanyDate;
  @JsonKey()
  final String degreeName;
  @J<PERSON><PERSON>ey()
  final String educationSubjectName;
  @JsonKey()
  final List<String> certificates;
  @J<PERSON><PERSON>ey()
  final String note;



  AdRoadUserInfoDto({
    this.employeeName,
    this.employeeCode,
    this.positionName,
    this.departmentName,
    this.unitName,
    this.joinCompanyDate,
    this.degreeName,
    this.educationSubjectName,
    this.certificates,
    this.note,
  });

  factory AdRoadUserInfoDto.fromJson(Map<String, dynamic> json) =>
      _$AdRoadUserInfoDtoFromJson(json);

  Map<String, dynamic> toJson() => _$AdRoadUserInfoDtoToJson(this);

}