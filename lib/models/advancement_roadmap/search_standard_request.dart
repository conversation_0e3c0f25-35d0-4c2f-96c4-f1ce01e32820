import 'package:json_annotation/json_annotation.dart';

part 'search_standard_request.g.dart';

@JsonSerializable()
class SearchStandardRequest {
  @Json<PERSON>ey()
  int page;
  @Json<PERSON>ey()
  int size;
  @J<PERSON><PERSON><PERSON>()
  String positionStandard;
  @<PERSON><PERSON><PERSON><PERSON>()
  final int unitId;
  @<PERSON><PERSON><PERSON><PERSON>()
  final int departmentId;
  @J<PERSON><PERSON><PERSON>()
  final int unitBlock;

  SearchStandardRequest({
    this.page,
    this.size,
    this.positionStandard,
    this.unitId,
    this.departmentId = -1,
    this.unitBlock = -1,
  });

  factory SearchStandardRequest.fromJson(Map<String, dynamic> json) =>
      _$SearchStandardRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SearchStandardRequestToJson(this);

}