import 'package:hstd/models/advancement_roadmap/process_career_detail_dto.dart';
import 'package:json_annotation/json_annotation.dart';

part 'standard_dto.g.dart';

@JsonSerializable()
class StandardDto {
  @JsonKey()
  final String positionStandardName;
  @JsonKey()
  final String unitName;
  @JsonKey()
  final String departmentName;
  @JsonKey()
  final List<ProcessCareerDetailDto> processCareerDetails;

  StandardDto(
      {this.positionStandardName,
      this.unitName,
      this.departmentName,
      this.processCareerDetails});

  factory StandardDto.fromJson(Map<String, dynamic> json) =>
      _$StandardDtoFromJson(json);

  Map<String, dynamic> toJson() => _$StandardDtoToJson(this);
}
