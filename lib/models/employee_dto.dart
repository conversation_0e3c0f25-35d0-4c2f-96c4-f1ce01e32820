import 'package:json_annotation/json_annotation.dart';

part 'employee_dto.g.dart';

@JsonSerializable()
class Employee {
  int employeeId;
  String fullname;
  String employeeCode;
  int gender;
  String telephoneNumber;
  String bankAccountNumber;
  String positionName;
  String bankName;
  String unitName;
  int unitId;
  String mobileNumber;
  String email;
  String dateOfBirth;
  int organizationId;
  int positionId;
  String trainingLevel;
  String trainingSpeciality;
  String employeeName;
  String employeeEmail;
  String employeePhoneNumber;
  String departmentName;

  Employee({
    this.employeeId,
    this.employeeCode,
    this.fullname,
    this.gender,
    this.telephoneNumber,
    this.bankAccountNumber,
    this.bankName,
    this.mobileNumber,
    this.email,
    this.dateOfBirth,
    this.organizationId,
    this.positionId,
    this.trainingLevel,
    this.trainingSpeciality,
    this.unitName,
    this.positionName,
    this.employeeName,
    this.employeeEmail,
    this.employeePhoneNumber,
    this.departmentName,
    this.unitId,
  });

  factory Employee.fromJson(Map<String, dynamic> json) => _$EmployeeFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeToJson(this);
}
