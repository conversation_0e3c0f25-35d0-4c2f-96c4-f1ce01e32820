import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/bloc/auth/authentication.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/screen/home_screen.dart';
import 'package:hstd/screen/information/information.dart';
import 'package:hstd/screen/notify_contract/notify_contract_page.dart';
import 'package:hstd/screen/information_rice_vouchers.dart';
import 'package:hstd/screen/profile_screen.dart';
import 'package:hstd/screen/smart_otp/smart_otp_page.dart';
import 'package:hstd/utils/utils.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:hstd/widget/menu_item_widget.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../widget/switch_login_face_widget.dart';
import 'employee_profile/employee_profile_page.dart';
import 'information_update/information_update.dart';
import 'management_device/management_device_page.dart';
import 'otp_screen_fake.dart';
import 'privacy/privacy.dart';

class AccoutScreen extends StatefulWidget {
  @override
  State<AccoutScreen> createState() => _AccoutScreenState();
}

class _AccoutScreenState extends State<AccoutScreen> {
  String _userName;
  String _email;
  String _position;
  String _version;
  bool hasData = false;

  @override
  void initState() {
    getData();
    super.initState();
  }

  void getData() async {
    var platform = await PackageInfo.fromPlatform();
    _version = platform.version;
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    _userName = sharedPreferences.get(AppConstant.KEY_USER_NAME) ?? '';
    _email = sharedPreferences.get(AppConstant.KEY_USER_EMAIL) ?? '';
    _position = sharedPreferences.get(AppConstant.KEY_USER_POSITION_NAME) ?? '';
    setState(() {
      hasData = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (hasData) {
      return Scaffold(
        resizeToAvoidBottomInset: false,
        body: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(15.0),
                  child: Column(
                    children: [
                      Stack(
                        children: [
                          Center(
                            child: Icon(
                              Icons.circle,
                              color: Color(0xFFB5B4B4),
                              size: MediaQuery.of(context).size.width * 3 / 10,
                            ),
                          ),
                          Center(
                            child: Container(
                              height: MediaQuery.of(context).size.width * 3 / 10,
                              width: MediaQuery.of(context).size.width * 3 / 10,
                              child: Center(
                                child: Text(
                                  Utils.getAvatarFromName(_userName),
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: MediaQuery.of(context).size.width *
                                        1 /
                                        10,
                                  ),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10),
                        child: Text(
                          '$_userName - $_email',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 30),
                        child: Text(
                          '$_position',
                          style: TextStyle(
                            color: Color(0xFFB5B4B4),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 12, bottom: 6, left: 20),
                child: Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    'Thông tin chung',
                    style: TextStyle(
                        color: Color(0xFF404040),
                        fontSize: 16,
                        fontWeight: FontWeight.w600
                    ),
                  ),
                ),
              ),
              Container(
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: Column(
                    children: [
                      MenuItemWidget(
                        title: 'Hồ sơ nhân viên',
                        icon: AppVectors.ic_user_login,
                        onTap: () {
                          Navigator.of(context).push(EmployeeProfilePage.route());
                        },
                      ),
                      Divider(
                        height: 1,
                        color: Color(0xFFB5B4B4),
                      ),
                      MenuItemWidget(
                        title: 'Biên bản và văn bản thông báo',
                        icon: AppVectors.icListContract,
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute<void>(
                              // builder: (_) => WarningTaskPage(),
                              builder: (_) => NotifyContractPage(
                                  arguments: NotifyContractArguments()),
                            ),
                          );
                        },
                      ),
                      Divider(
                        height: 1,
                        color: Color(0xFFB5B4B4),
                      ),
                      MenuItemWidget(
                        title: 'Phiếu cơm',
                        icon: AppVectors.icCook,
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute<void>(
                              builder: (_) => InformationRiceVoucher(),
                            ),
                          );
                        },
                      ),
                      Divider(
                        height: 1,
                        color: Color(0xFFB5B4B4),
                      ),
                      MenuItemWidget(
                        title: 'Cập nhật thông tin cá nhân',
                        icon: AppVectors.icUpdateUser,
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (_context) => InformationUpdateProfileScreen(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 12, bottom: 6, left: 20),
                child: Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    'Cấu hình tài khoản',
                    style: TextStyle(
                      color: Color(0xFF404040),
                      fontSize: 16,
                      fontWeight: FontWeight.w600
                    ),
                  ),
                ),
              ),
              Container(
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: InkWell(
                          onTap: (){
                            Navigator.of(context).push(
                              MaterialPageRoute<void>(
                                builder: (_) => SmartOtpPage(),
                              ),
                            );
                          },
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(width: 5),
                              SvgPicture.asset(AppVectors.icSmartLock),
                              SizedBox(width: 10),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          flex: 3,
                                          child: Text(
                                            "Thiết lập Smart OTP",
                                            style: TextStyle(fontSize: 16),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 2,
                                          child: Container(
                                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                                            decoration: BoxDecoration(
                                              color: Color(0xffE5FFF2),
                                              borderRadius: BorderRadius.circular(360)
                                            ),
                                            child: Center(
                                              child: Text(
                                                "Khuyên dùng",
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xff39B54A),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 5,),
                                    Text(
                                      "Tăng cường bảo mật và tốc độ xác thực OTP",
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: Color(0xff7F7F7F),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.chevron_right,
                                color: Color(0xFFB5B4B4),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Divider(
                        height: 1,
                        color: Color(0xFFB5B4B4),
                      ),
                      MenuItemWidget(
                          title: 'Cài đặt quyền',
                          icon: AppVectors.ic_setting_permission,
                          onTap: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_context) => PrivacyScreen(),
                              ),
                            );
                          }),
                      Divider(
                        height: 1,
                        color: Color(0xFFB5B4B4),
                      ),
                      MenuItemWidget(
                          title: 'Đăng nhập sinh trắc học',
                          icon: AppVectors.ic_bio_authen,
                          rightButton: SwitchFaceWidget(),
                          onTap: () {
                          }),
                      // Divider(
                      //   height: 1,
                      //   color: Color(0xFFB5B4B4),
                      // ),
                      // menuItem(
                      //     title: 'Quản lý thiết bị',
                      //     icon: AppVectors.icProfileInfo,
                      //     onTap: () {
                      //       Navigator.of(context).push(
                      //         MaterialPageRoute(
                      //           builder: (_context) => ManagementDevicePage(),
                      //         ),
                      //       );
                      //     }),
                      Divider(
                        height: 1,
                        color: Color(0xFFB5B4B4),
                      ),
                      MenuItemWidget(
                          title: 'Đăng xuất',
                          icon: AppVectors.ic_logout,
                          onTap: () {
                            showDialog<String>(
                              context: context,
                              builder: (BuildContext context) => AlertDialog(
                                title: const Text('Đăng xuất'),
                                content: const Text(
                                    'Bạn có chắc chắn muốn đăng xuất?'),
                                actions: <Widget>[
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.pop(context, 'Cancel'),
                                    child: const Text('Huỷ'),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      HomeScreen.showNotify = true;
                                      HomeScreen.showLabor = true;
                                      BlocProvider.of<AuthenticationBloc>(context)
                                        ..add(AuthenticationLogoutRequested());
                                    },
                                    child: const Text('Xác nhận'),
                                  ),
                                ],
                              ),
                            );
                          }),
                      Divider(
                        height: 1,
                        color: Color(0xFFB5B4B4),
                      ),
                      MenuItemWidget(
                          title: 'Xóa tài khoản',
                          icon: AppVectors.ic_delete_account_2,
                          onTap: () {
                            showDialog<String>(
                              context: context,
                              builder: (BuildContext context) => AlertDialog(
                                title: const Text('Xoá tài khoản'),
                                content: const Text(
                                    'Bạn có chắc chắn muốn xoá tài khoản?'),
                                actions: <Widget>[
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.pop(context, 'Cancel'),
                                    child: const Text('Huỷ'),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      HomeScreen.showLabor = true;
                                      Navigator.pop(context, 'OK');
                                      Navigator.push(
                                          context, OtpScreenFake.route());
                                    },
                                    child: const Text('Xác nhận'),
                                  ),
                                ],
                              ),
                            );
                          }),
                    ],
                  ),
                ),
              ),
              Align(
                alignment: Alignment.center,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Version $_version',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFFB5B4B4),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      return LoadingIndicator();
    }
  }
}

Widget menuItem({Widget rightButton, String title, String icon, Function onTap}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 12),
    child: InkWell(
      onTap: onTap,
      child: Row(
        children: [
          SizedBox(width: 5),
          SvgPicture.asset(icon, color: Color(0xff7F7F7F),),
          SizedBox(width: 10),
          Expanded(
            child: Text(title),
          ),
          rightButton ?? Icon(
            Icons.chevron_right,
            color: Color(0xFFB5B4B4),
          ),
        ],
      ),
    ),
  );
}

// class _LogoutButton extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Row(children: <Widget>[
//       Expanded(
//         child: Container(
//           margin: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
//           width: double.infinity, // match_parent
//           child: MaterialButton(
//             elevation: 0,
//             padding: EdgeInsets.symmetric(vertical: 15),
//             child: Text(
//               "Đăng xuất",
//               style: TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.w500,
//                   color: Colors.white),
//             ),
//             onPressed: () {
//               HomeScreen.showNotify = true;
//               BlocProvider.of<AuthenticationBloc>(context)
//                 ..add(AuthenticationLogoutRequested());
//             },
//             color: Color(0xFFEE0033),
//             textColor: Colors.white,
//             shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(50),
//                 side: BorderSide(style: BorderStyle.none)),
//           ),
//         ),
//       ),
//       Expanded(
//           child: Container(
//             margin: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
//             width: double.infinity, // match_parent
//             child: MaterialButton(
//               elevation: 0,
//               padding: EdgeInsets.symmetric(vertical: 15),
//               child: Text(
//                 "Xoá tài khoản",
//                 style: TextStyle(
//                     fontSize: 16, fontWeight: FontWeight.w500, color: Colors.white),
//               ),
//               onPressed: () {
//                 showDialog<String>(
//                   context: context,
//                   builder: (BuildContext context) => AlertDialog(
//                     title: const Text('Xoá tài khoản'),
//                     content: const Text('Bạn có chắc chắn muốn xoá tài khoản?'),
//                     actions: <Widget>[
//                       TextButton(
//                         onPressed: () => Navigator.pop(context, 'Cancel'),
//                         child: const Text('Huỷ'),
//                       ),
//                       TextButton(
//                         onPressed: () {
//                           Navigator.pop(context, 'OK');
//                           Navigator.push(context, OtpScreenFake.route());
//                         },
//                         child: const Text('Xác nhận'),
//                       ),
//                     ],
//                   ),
//                 );
//               },
//               color: Color(0xFFEE0033),
//               textColor: Colors.white,
//               shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(50),
//                   side: BorderSide(style: BorderStyle.none)),
//             ),
//           ))
//     ]);
//   }
// }
