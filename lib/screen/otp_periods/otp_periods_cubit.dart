import 'dart:async';
import 'dart:typed_data';

import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/payment_periods/payment_periods_confirm_dto.dart';
import 'package:hstd/repositories/payment_periods_service.dart';
import 'package:nb_utils/nb_utils.dart';

part 'otp_periods_state.dart';

class OtpPeriodsCubit extends Cubit<OtpPeriodsState> {
  OtpPeriodsCubit() : super(const OtpPeriodsState());
  PaymentPeriodsService _service = PaymentPeriodsServiceImpl();

  // Future<void> submitOtp({Otp otp}) async {
  //   emit(state.copyWith(submitStatus: LoadStatus.loading));
  //
  //   try {
  //     final response = await _service.submitOtp(otp);
  //
  //     if (response.result) {
  //       emit(state.copyWith(
  //         submitStatus: LoadStatus.success,
  //       ));
  //     } else {
  //       emit(
  //         state.copyWith(
  //           submitStatus: LoadStatus.failure,
  //           message: response.message,
  //         ),
  //       );
  //     }
  //   } catch (e) {
  //     emit(
  //       state.copyWith(
  //         submitStatus: LoadStatus.failure,
  //         message: AppStrings.has_error,
  //       ),
  //     );
  //   }
  // }

  Future<void> confirmPayment(Uint8List imageSign) async {
    emit(state.copyWith(submitStatus: LoadStatus.loading));
    final dto = state.paymentPeriodsConfirmDto;
    PaymentPeriodsConfirmDto request = new PaymentPeriodsConfirmDto(
      paymentId: dto.paymentId,
      otpId: dto.otpId,
      otpCode: state.otpCode,
      deviceId: dto.deviceId,
      isSmartOtp: dto.isSmartOtp,
    );
    try {
      final response =
          await _service.confirmPayment(request, dto.fileBill, imageSign);

      if (response.result) {
        emit(state.copyWith(
          submitStatus: LoadStatus.success,
        ));
      } else {
        emit(
          state.copyWith(
            submitStatus: LoadStatus.failure,
            message: response.message,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          submitStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  void changeCurrentText(String value) {
    bool showButtonSubmit = false;
    if (value.length == 6) {
      showButtonSubmit = true;
    }
    emit(
      state.copyWith(
        showButtonSubmit: showButtonSubmit,
        otpCode: value,
      ),
    );
  }

  void changeDuration({VoidCallback onCancel}) {
    if (state.duration == 0) {
      onCancel();
    } else {
      emit(
        state.copyWith(
          duration: state.duration - 1,
        ),
      );
    }
  }

  void setDuration({int duration}) {
    emit(
      state.copyWith(
        duration: duration,
      ),
    );
  }

  void setPaymentPeriodsConfirmDto(PaymentPeriodsConfirmDto value) {
    emit(
      state.copyWith(
        paymentPeriodsConfirmDto: value,
      ),
    );
  }

  void clearStatus() {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.initial,
        submitStatus: LoadStatus.initial,
      ),
    );
  }
}
