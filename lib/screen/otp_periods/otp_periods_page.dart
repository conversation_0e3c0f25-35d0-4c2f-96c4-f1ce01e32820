import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/core/constant.dart';
import 'package:hstd/models/payment_periods/payment_periods_confirm_dto.dart';
import 'package:hstd/models/payment_periods/payment_periods_dto.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/screen/information_salary/payment_periods/payment_periods_page.dart';
import 'package:hstd/screen/information_salary/payment_periods_detail/payment_periods_detail_page.dart';
import 'package:hstd/screen/sign/sign_document/widgets/dialog_success.dart';
import 'package:hstd/utils/utils.dart';
import 'package:hstd/widget/dialog_auto_close.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:url_launcher/url_launcher.dart';

import 'otp_periods_cubit.dart';

class OtpPeriodsArguments {
  String phoneNumber;
  PaymentPeriodsConfirmDto paymentPeriodsConfirmDto;
  Uint8List imageSign;
  OtpPeriodsArguments(
      {this.phoneNumber,
      this.paymentPeriodsConfirmDto,
      this.imageSign});
}

class OtpPeriodsPage extends StatelessWidget {
  final OtpPeriodsArguments arguments;

  const OtpPeriodsPage({
    Key key,
    this.arguments,
  }) : super(key: key);

  static route({OtpPeriodsArguments arguments}) => MaterialPageRoute<bool>(
        builder: (_) => OtpPeriodsPage(
          arguments: arguments,
        ),
      );

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return OtpPeriodsCubit();
      },
      child: OtpChildPage(
        arguments: arguments,
      ),
    );
  }
}

class OtpChildPage extends StatefulWidget {
  final OtpPeriodsArguments arguments;

  const OtpChildPage({
    this.arguments,
    Key key,
  }) : super(key: key);

  @override
  State<OtpChildPage> createState() => _OtpChildPageState();
}

class _OtpChildPageState extends State<OtpChildPage> {
  OtpPeriodsCubit _cubit;
  Timer _timer;

  // TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.setDuration(
        duration: widget.arguments.paymentPeriodsConfirmDto.duration);
    _cubit
        .setPaymentPeriodsConfirmDto(widget.arguments.paymentPeriodsConfirmDto);
    startTime();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<OtpPeriodsCubit, OtpPeriodsState>(
      listenWhen: (previous, current) =>
          previous.submitStatus != current.submitStatus,
      listener: (context, state) {
        if (state.submitStatus == LoadStatus.loading) {
          LoadingDialogTransparent.show(context);
        } else if (state.submitStatus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          _timer.cancel();
          showDialog(
            context: context,
            builder: (BuildContext context) => ShowDialogSignSuccess(
              onGoToList: () {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
                Navigator.of(context).pop();
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PaymentPeriodsPage(),
                  ),
                );
              },
              onViewDetail: () {
                Navigator.of(context).pop(true);
              },
            ),
            barrierDismissible: false,
          );
        } else if (state.submitStatus == LoadStatus.failure) {
          LoadingDialogTransparent.hide(context);
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: state.message,
              showIcon: false,
            ),
          );
        }

      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            title: Text(
              AppStrings.input_otp,
              style: AppTextStyle.blackS18W700,
            ),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            centerTitle: true,
            elevation: 0,
            actions: [
              InkWell(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: () {
                  launchUrl(Uri.parse('tel:190098968'));
                },
                child: Padding(
                  padding: EdgeInsets.only(right: 12),
                  child: SvgPicture.asset(
                    AppVectors.ic_headphone,
                    color: Colors.black,
                  ),
                ),
              ),
            ],
          ),
          body: SafeArea(
            child: _buildBodyWidget(),
          ),
        );
      },
    );
  }

  Widget _buildBodyWidget() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                AppStrings.otp_has_been_sent,
                style: AppTextStyle.greyLoginOtpS14W400,
              ),
            ),
            Text(
              Utils.formatPhoneNumber(widget.arguments.phoneNumber),
              style: AppTextStyle.blackS16W600,
            ),
            _countDown(),
            Form(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0),
                child: Container(
                  width: 328,
                  child: PinCodeTextField(
                    appContext: context,
                    length: 6,
                    pastedTextStyle: TextStyle(
                        color: Colors.white, fontWeight: FontWeight.bold),
                    obscureText: false,
                    // obscuringCharacter: '*',
                    blinkWhenObscuring: true,
                    animationType: AnimationType.fade,
                    validator: (String value) {
                      if (value == null || value.isEmpty) {
                        return AppStrings.please_input_otp;
                      }
                      return null;
                    },
                    pinTheme: PinTheme(
                        shape: PinCodeFieldShape.box,
                        borderWidth: 1,
                        borderRadius: BorderRadius.circular(6),
                        fieldHeight: 56,
                        fieldWidth: 48,
                        inactiveFillColor: Colors.white,
                        inactiveColor: AppColors.greyRoadmap,
                        activeColor: AppColors.greyRoadmap,
                        activeFillColor: Colors.white,
                        selectedFillColor: Colors.white,
                        selectedColor: AppColors.greyRoadmap,
                        errorBorderColor: Colors.red),
                    cursorColor: Colors.black,
                    animationDuration: Duration(milliseconds: 300),
                    enableActiveFill: true,
                    autoFocus: true,
                    // controller: _controller,
                    // errorAnimationController: _errorController,
                    keyboardType: TextInputType.number,

                    onCompleted: (v) {
                      print("Completed $v");
                    },
                    onChanged: (value) {
                      _cubit.changeCurrentText(value);
                    },
                    beforeTextPaste: (text) {
                      return true;
                    },
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 15),
              child: Text(
                AppStrings.contact_otp_period_payment,
                style: AppTextStyle.greyLoginOtpS14W400H13,
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 50),
            _buttonSubmit(),
          ],
        ),
      ),
    );
  }

  Widget _buttonSubmit() {
    return BlocBuilder<OtpPeriodsCubit, OtpPeriodsState>(
      buildWhen: (previous, current) =>
          previous.showButtonSubmit != current.showButtonSubmit,
      builder: (context, state) {
        return Opacity(
          opacity: state.showButtonSubmit ? 1 : 0.5,
          child: PrimaryButton(
            borderRadius: BorderRadius.circular(24),
            title: AppStrings.confirm,
            titleStyle: AppTextStyle.whiteS16W600,
            color: AppColors.primaryColor,
            borderColor: AppColors.primaryColor,
            onTap: () {
              if (state.showButtonSubmit) {
                _cubit.confirmPayment(widget.arguments.imageSign);
              }
            },
          ),
        );
      },
    );
  }

  Widget _countDown() {
    return BlocBuilder<OtpPeriodsCubit, OtpPeriodsState>(
      buildWhen: (previous, current) => previous.duration != current.duration,
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                AppStrings.validity_period,
                style: AppTextStyle.greyLoginOtpS14W400,
              ),
              Text(' ${state.duration} ${AppStrings.second}',
                  style: AppTextStyle.primaryS14W400),
            ],
          ),
        );
      },
    );
  }

  void startTime() {
    const seconds = Duration(seconds: 1);
    _timer = Timer.periodic(seconds, (timer) {
      _cubit.changeDuration(
        onCancel: () => _timer.cancel(),
      );
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _cubit.close();
    super.dispose();
  }
}
