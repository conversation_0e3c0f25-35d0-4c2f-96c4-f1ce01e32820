part of 'sign_document_cubit.dart';

class SignDocumentState extends Equatable {
  final LoadStatus loadDataStatus;
  final LoadStatus createOtpStatus;
  final LoadStatus signStautus;
  final LoadStatus validateStatus;
  final FileData fileData;
  final FileData fileResponse;
  final String message;
  final bool accessTerms;
  final bool signed;
  final Otp otp;
  final LaborContractDto laborContractDto;
  final FileIncomeTaxDto fileIncomeTaxDto;
  final Contract contract;
  final AnnexContract annexContract;
  final CommitDto commitContract;
  final FileData signatureFile;
  final LoadStatus loadDefaultSignature;

  const SignDocumentState({
    this.loadDataStatus = LoadStatus.initial,
    this.createOtpStatus = LoadStatus.initial,
    this.signStautus = LoadStatus.initial,
    this.validateStatus = LoadStatus.initial,
    this.fileData,
    this.fileResponse,
    this.message,
    this.accessTerms = false,
    this.signed = false,
    this.otp,
    this.laborContractDto,
    this.fileIncomeTaxDto,
    this.contract,
    this.annexContract,
    this.commitContract,
    this.signatureFile,
    this.loadDefaultSignature,
  });

  @override
  List<Object> get props => [
        loadDataStatus,
        signStautus,
        createOtpStatus,
        validateStatus,
        fileData,
        fileResponse,
        message,
        accessTerms,
        signed,
        otp,
        laborContractDto,
        fileIncomeTaxDto,
        contract,
        annexContract,
        commitContract,
        signatureFile,
        loadDefaultSignature,
      ];

  SignDocumentState copyWith({
    LoadStatus loadDataStatus,
    LoadStatus createOtpStatus,
    LoadStatus signStautus,
    LoadStatus validateStatus,
    FileData fileData,
    FileData fileResponse,
    String message,
    bool accessTerms,
    bool signed,
    Otp otp,
    LaborContractDto laborContractDto,
    FileIncomeTaxDto fileIncomeTaxDto,
    Contract contract,
    AnnexContract annexContract,
    CommitDto commitContract,
    FileData signatureFile,
    LoadStatus loadDefaultSignature,
  }) {
    return SignDocumentState(
      loadDataStatus: loadDataStatus ?? this.loadDataStatus,
      createOtpStatus: createOtpStatus ?? this.createOtpStatus,
      signStautus: signStautus ?? this.signStautus,
      validateStatus: validateStatus ?? this.validateStatus,
      fileData: fileData ?? this.fileData,
      fileResponse: fileResponse ?? this.fileResponse,
      message: message ?? this.message,
      accessTerms: accessTerms ?? this.accessTerms,
      signed: signed ?? this.signed,
      otp: otp ?? this.otp,
      laborContractDto: laborContractDto ?? this.laborContractDto,
      fileIncomeTaxDto: fileIncomeTaxDto ?? this.fileIncomeTaxDto,
      contract: contract ?? this.contract,
      annexContract: annexContract ?? this.annexContract,
      commitContract: commitContract ?? this.commitContract,
      signatureFile: signatureFile ?? this.signatureFile,
      loadDefaultSignature: loadDefaultSignature ?? this.loadDefaultSignature,
    );
  }
}
