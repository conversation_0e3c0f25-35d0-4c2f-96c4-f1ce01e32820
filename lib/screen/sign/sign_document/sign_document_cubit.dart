import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/annex_contract_dto.dart';
import 'package:hstd/models/certification/request_create_certification.dart';
import 'package:hstd/models/commit/commit_dto.dart';
import 'package:hstd/models/commit/sign_commit_request.dart';
import 'package:hstd/models/contract_dto.dart';
import 'package:hstd/models/document/labor_contract.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/models/income_tax/file_income_tax_dto.dart';
import 'package:hstd/models/otp_dto.dart';
import 'package:hstd/models/smart_otp/validate_smart_otp_request.dart';
import 'package:hstd/preferences/data_center.dart';
import 'package:hstd/repositories/certification_service.dart';
import 'package:hstd/repositories/commit_service.dart';
import 'package:hstd/repositories/contract_service.dart';
import 'package:hstd/repositories/document_service.dart';
import 'package:hstd/repositories/file_service.dart';
import 'package:hstd/repositories/otp_service.dart';
import 'package:hstd/repositories/smart_otp_service.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/screen/income_tax/income_tax_page.dart';
import 'package:hstd/screen/login_by_sdt/document_outside/document_outside_page.dart';
import 'package:hstd/screen/sign/view_document/view_document_page.dart';
import 'package:hstd/widget/dialog_auto_close.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'widgets/dialog_success.dart';

part 'sign_document_state.dart';

class SignDocumentCubit extends Cubit<SignDocumentState> {
  DocumentService _documentService = new DocumentServiceImp();
  ContractServiceImp _contractService = new ContractServiceImp();
  CommitService _commitService = new CommitServiceImp();
  OtpService _otpService = new OtpServiceImp();
  FileService _fileService = new FileServiceImp();
  CertificationService _certificationService = CertificationServiceImp();
  OtpService _service = new OtpServiceImp();
  SmartOtpService _smartOtpService = new SmartOtpServiceImp();

  SignDocumentCubit() : super(const SignDocumentState());

  void loadInitialData({
    TypeSign type,
    int taxPaymentRegisterId,
    FileData fileData,
    Contract contract,
    CommitDto commit,
  }) {
    switch (type) {
      case TypeSign.CONTRACT_OUTSIDE:
        getContractOutSide();
        break;
      case TypeSign.INCOME_TAX:
        getIncomeTax(taxPaymentRegisterId);
        break;
      case TypeSign.PROBATION:
        getContractProbation(contract);
        break;
      case TypeSign.LABOR:
        getContractLabor(contract);
        break;
      case TypeSign.ANNEX_CONTRACT:
        getAnnexContract();
        break;
      case TypeSign.COMMIT:
        getContractCommit(commit);
        break;
      case TypeSign.SOCIAL_INSURANCE:
        getDocumentBHXH(fileData: fileData);
        break;
      case TypeSign.CERTIFICATION:
        getDocumentBHXH(fileData: fileData);
        break;
      case TypeSign.CERTIFICATE_COMMIT:
        getCertificateCommitment(fileData: fileData);
        break;
    }
  }

  void sign({
    TypeSign type,
    Uint8List sign,
    int taxPaymentRegisterId,
    RequestCreateCertification requestCreateCertification,
    SignCommitRequest signCommitRequest,
    SignOSHRequest signOSHRequest,
    SignElectricalSafetyRequest signElectricalSafetyRequest,
    int addDocumentStatus,
  }) {
    switch (type) {
      case TypeSign.CONTRACT_OUTSIDE:
        signContractOutSide(
          sign: sign,
          endOfFile: 'png',
        );
        break;
      case TypeSign.INCOME_TAX:
        signContractIncomeTax(
          sign: sign,
          endOfFile: 'png',
          id: taxPaymentRegisterId,
        );
        break;
      case TypeSign.PROBATION:
        signContractProbation(
          sign: sign,
          endOfFile: 'png',
        );
        break;
      case TypeSign.LABOR:
        signContractLabor(
          sign: sign,
          endOfFile: 'png',
        );
        break;
      case TypeSign.ANNEX_CONTRACT:
        signAnnexContract(
          sign: sign,
          endOfFile: 'png',
        );
        break;
      case TypeSign.COMMIT:
        signContractCommit(
          sign: sign,
          endOfFile: 'png',
        );
        break;
      case TypeSign.SOCIAL_INSURANCE:
        signDocumentBHXH(
          sign: sign,
          endOfFile: 'png',
        );
        break;
      case TypeSign.CERTIFICATION:
        signCertification(
          sign: sign,
          endOfFile: 'png',
          request: requestCreateCertification,
        );
        break;
      case TypeSign.CERTIFICATE_COMMIT:
        signCertificateCommitment(sign: sign, endOfFile: 'png', request: signCommitRequest, addDocumentStatus: addDocumentStatus);
        break;
      case TypeSign.TRAINING_SESSION_BOOK:
        signOSH(
          sign: sign,
          endOfFile: 'png',
          request: signOSHRequest,
        );
        break;
      case TypeSign.TRAINING_ELECTRICAL_SAFETY_BOOK:
        signElectricalSafety(
          sign: sign,
          endOfFile: 'png',
          request: signElectricalSafetyRequest,
        );
        break;
    }
  }

  ///sign
  Future<void> signContractOutSide({Uint8List sign, String endOfFile}) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        ContractOutsideRequest request = new ContractOutsideRequest(
            contractOsId: state.laborContractDto.contractOsId, signatureFileName: fileUploadData.title, signatureFileEncodePath: fileUploadData.url);

        final response = await _contractService.signContractOutside(request);

        if (response != null) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              fileResponse: response,
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_contract,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signContractIncomeTax({Uint8List sign, String endOfFile, int id}) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        TaxPaymentRequest request =
            new TaxPaymentRequest(taxPaymentRegisterId: id, signatureFileName: fileUploadData.title, signatureFileEncodePath: fileUploadData.url);

        final response = await _contractService.signTaxPayment(request);

        if (response != null) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              fileResponse: response,
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_contract,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signContractProbation({Uint8List sign, String endOfFile}) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        ContractSignRequest request = new ContractSignRequest(
            contractId: state.contract.contractId, signatureFileName: fileUploadData.title, signatureFileEncodePath: fileUploadData.url);

        final response = await _contractService.signProbation(request);

        if (response != null) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              fileResponse: response,
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_contract,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signContractLabor({Uint8List sign, String endOfFile}) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        ContractSignRequest request = new ContractSignRequest(
            contractId: state.contract.contractId, signatureFileName: fileUploadData.title, signatureFileEncodePath: fileUploadData.url);

        final response = await _contractService.signLabor(request);

        if (response != null) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              fileResponse: response,
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_contract,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signAnnexContract({Uint8List sign, String endOfFile}) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        ContractSignRequest request =
            new ContractSignRequest(contractId: state.annexContract.id, signatureFileName: fileUploadData.title, signatureFileEncodePath: fileUploadData.url);

        final response = await _contractService.signAnnexContract(request);

        if (response != null) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              fileResponse: response,
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_contract,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signContractCommit({Uint8List sign, String endOfFile}) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        CommitSignRequest request = new CommitSignRequest(
            employeeCommitId: state.commitContract.id, signatureFileName: fileUploadData.title, signatureFileEncodePath: fileUploadData.url);

        final response = await _contractService.signCommit(request);

        if (response != null) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              fileResponse: response,
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_commit,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signCertificateCommitment({
    Uint8List sign,
    String endOfFile,
    SignCommitRequest request,
    int addDocumentStatus,
  }) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        request.fileEncodePath = state.fileData.filePath;
        request.signatureFileEncodePath = fileUploadData.url;

        var response;
        if (addDocumentStatus != null) {
          response = await _commitService.signCommitAddDocs(request: request);
        } else {
          response = await _commitService.signCommit(request: request);
        }

        if (response != null) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              fileResponse: response,
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_commit,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signOSH({
    Uint8List sign,
    String endOfFile,
    SignOSHRequest request,
  }) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        // request.fileEncodePath = state.fileData.filePath;
        request.signatureFilePath = fileUploadData.url;
        request.signatureFileName = fileUploadData.title;

        final response = await _commitService.signOSH(request: request);

        if (response == true) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              message: 'Ký thành công',
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_commit,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signElectricalSafety({
    Uint8List sign,
    String endOfFile,
    SignElectricalSafetyRequest request,
  }) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {

        // request.fileEncodePath = state.fileData.filePath;
        request.signatureFilePath = fileUploadData.url;
        request.signatureFileName = fileUploadData.title;

        final response = await _commitService.signElectricalSafety(request: request);

        if (response == true) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              message: 'Ký thành công',
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_commit,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signDocumentBHXH({Uint8List sign, String endOfFile}) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
        int commitId = sharedPreferences.getInt("COMMIT_ID");
        CommitSignRequest request =
            new CommitSignRequest(employeeCommitId: commitId, signatureFileName: fileUploadData.title, signatureFileEncodePath: fileUploadData.url);

        final response = await _contractService.signCommit(request);

        if (response != null) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              fileResponse: response,
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_commit,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> signCertification({
    Uint8List sign,
    String endOfFile,
    RequestCreateCertification request,
  }) async {
    emit(state.copyWith(signStautus: LoadStatus.loading));

    try {
      FileUploadData fileUploadData = await uploadSign(sign, endOfFile);

      if (fileUploadData != null) {
        request.filePath = state.fileData.encodePath;
        request.signatureFilePath = fileUploadData.url;

        final response = await _certificationService.signCertification(
          request: request,
        );

        if (response != null) {
          emit(
            state.copyWith(
              signStautus: LoadStatus.success,
              fileResponse: response,
            ),
          );
        } else {
          emit(
            state.copyWith(
              signStautus: LoadStatus.failure,
              message: AppStrings.error_sign_commit,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            signStautus: LoadStatus.failure,
            message: AppStrings.error_upload_sign_file,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          signStautus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  ///---------------------------------------------------------------------------

  ///get data
  Future<void> getContractOutSide() async {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.loading,
      ),
    );

    try {
      final laborContract = await _documentService.getLaborContractSign();

      if (laborContract != null) {
        emit(
          state.copyWith(
            laborContractDto: laborContract,
          ),
        );

        final fileData = await _documentService.downloadFile(laborContract.contractOsId);

        if (fileData != null) {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.success,
              fileData: fileData,
            ),
          );
        } else {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.failure,
              message: AppStrings.file_not_found,
            ),
          );
        }
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.contract_not_found,
        ));
      }
    } catch (e) {
      emit(
        state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> getIncomeTax(int taxPaymentRegisterId) async {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.loading,
      ),
    );

    try {
      final fileIncomeTax = await _documentService.getIncomeTaxSign(taxPaymentRegisterId);

      if (fileIncomeTax != null) {
        emit(
          state.copyWith(
            loadDataStatus: LoadStatus.success,
            fileIncomeTaxDto: fileIncomeTax,
          ),
        );
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.contract_not_found,
        ));
      }
    } catch (e) {
      emit(
        state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> getContractProbation(Contract contract) async {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.loading,
      ),
    );

    try {
      // final contract = await _contractService.getContractProbation();

      if (contract != null) {
        emit(
          state.copyWith(
            contract: contract,
          ),
        );

        var fileData = await _fileService.downloadFile(contract.contractFileEncodePath);

        if (fileData != null) {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.success,
              fileData: fileData,
            ),
          );
        } else {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.failure,
              message: AppStrings.file_not_found,
            ),
          );
        }
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.contract_not_found,
        ));
      }
    } catch (e) {
      emit(
        state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> getContractLabor(Contract contract) async {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.loading,
      ),
    );

    try {
      // final contract = await _contractService.getContractLabor();

      if (contract != null) {
        emit(
          state.copyWith(
            contract: contract,
          ),
        );

        var fileData = await _fileService.downloadFile(contract.contractFileEncodePath);

        if (fileData != null) {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.success,
              fileData: fileData,
            ),
          );
        } else {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.failure,
              message: AppStrings.file_not_found,
            ),
          );
        }
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.contract_not_found,
        ));
      }
    } catch (e) {
      emit(
        state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> getAnnexContract() async {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.loading,
      ),
    );

    try {
      final contract = await _contractService.getSignAnnexContract();

      if (contract != null && (contract.status == null || contract.status == 'INFORMED_TO_EMPLOYEE' || contract.status == 'ANNEX_CONTRACT_CREATED')) {
        emit(
          state.copyWith(
            annexContract: contract,
          ),
        );

        var fileData = await _fileService.downloadFile(contract.fileUrl);

        if (fileData != null) {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.success,
              fileData: fileData,
            ),
          );
        } else {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.failure,
              message: AppStrings.file_not_found,
            ),
          );
        }
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.contract_not_found,
        ));
      }
    } catch (e) {
      emit(
        state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> getContractCommit(CommitDto contract) async {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.loading,
      ),
    );

    try {
      // final contract = await _commitService.getCommitToSign();

      if (contract != null) {
        emit(
          state.copyWith(
            commitContract: contract,
          ),
        );

        var fileData = await _commitService.downloadFile(contract.id);

        if (fileData != null) {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.success,
              fileData: fileData,
            ),
          );
        } else {
          emit(
            state.copyWith(
              loadDataStatus: LoadStatus.failure,
              message: AppStrings.file_not_found,
            ),
          );
        }
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.contract_not_found,
        ));
      }
    } catch (e) {
      emit(
        state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> getCertificateCommitment({
    FileData fileData,
  }) async {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.loading,
      ),
    );

    try {
      if (fileData != null) {
        emit(
          state.copyWith(
            loadDataStatus: LoadStatus.success,
            fileData: fileData,
          ),
        );
      } else {
        emit(
          state.copyWith(
            loadDataStatus: LoadStatus.failure,
            message: AppStrings.file_not_found,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<void> getDocumentBHXH({FileData fileData}) async {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.loading,
      ),
    );
    try {
      if (fileData != null) {
        emit(
          state.copyWith(
            loadDataStatus: LoadStatus.success,
            fileData: fileData,
          ),
        );
      } else {
        emit(
          state.copyWith(
            loadDataStatus: LoadStatus.failure,
            message: AppStrings.file_not_found,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          loadDataStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  ///---------------------------------------------------------------------------

  Future<void> createOtp({int otpType}) async {
    emit(state.copyWith(createOtpStatus: LoadStatus.loading));

    OtpRequest otpRequest = new OtpRequest(otpType: otpType, content: null);

    try {
      final Otp result = await _otpService.createOtp(otpRequest);

      if (result != null) {
        emit(state.copyWith(
          createOtpStatus: LoadStatus.success,
          otp: result,
        ));
      } else {
        emit(
          state.copyWith(
            createOtpStatus: LoadStatus.failure,
            message: AppStrings.error_create_otp,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          createOtpStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  Future<FileUploadData> uploadSign(Uint8List sign, String endOfFile) async {
    try {
      List<FileUploadRequest> images = [];
      SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
      String userCode = sharedPreferences.get(AppConstant.KEY_USER_CODE);
      String fileName = 'sign_' + userCode + '.' + endOfFile;
      images.add(new FileUploadRequest(fileUnit: sign, fileName: fileName));
      List<FileUploadData> fileUpload = await _fileService.uploadFileBytes(images);
      if (fileUpload == null || fileUpload.length == 0) {
        return null;
      }
      return fileUpload.first;
    } catch (ex) {
      return null;
    }
  }

  void setAccessTerms(bool value) {
    emit(
      state.copyWith(
        accessTerms: value,
      ),
    );
  }

  void setSigned(bool value) {
    emit(
      state.copyWith(
        signed: value,
      ),
    );
  }

  Future<void> confirm({TypeSign typeSign, int addDocumentStatus}) async {
    switch (typeSign) {
      case TypeSign.CONTRACT_OUTSIDE:
        createOtp(otpType: TYPE_OTP.CONTRACT_OUTSIDE.values);
        break;
      case TypeSign.INCOME_TAX:
        createOtp(otpType: TYPE_OTP.COMMIT.values);
        break;
      case TypeSign.PROBATION:
        createOtp(otpType: TYPE_OTP.PROBATION.values);
        break;
      case TypeSign.LABOR:
        createOtp(otpType: TYPE_OTP.LABOR.values);
        break;
      case TypeSign.ANNEX_CONTRACT:
        createOtp(otpType: TYPE_OTP.ANNEX_CONTRACT.values);
        break;
      case TypeSign.COMMIT:
        createOtp(otpType: TYPE_OTP.COMMIT.values);
        break;
      case TypeSign.SOCIAL_INSURANCE:
        createOtp(otpType: TYPE_OTP.COMMIT.values);
        break;
      case TypeSign.CERTIFICATION:
        createOtp(otpType: TYPE_OTP.CERTIFICATION.values);
        break;
      case TypeSign.CERTIFICATE_COMMIT:
        createOtp(otpType: TYPE_OTP.CERTIFICATE_COMMIT.values);
        break;
      case TypeSign.TRAINING_ELECTRICAL_SAFETY_BOOK:
        createOtp(otpType: TYPE_OTP.TRAINING_ELECTRICAL_SAFETY_BOOK.values);
        break;
      case TypeSign.TRAINING_SESSION_BOOK:
        SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
        if (sharedPreferences.getStringList(AppConstant.KEY_ROLE)[0] == 'HSTD_OUTSOURCE') {
          createOtp(otpType: TYPE_OTP.CONTRACT_OUTSIDE.values);
        } else {
          createOtp(otpType: TYPE_OTP.TRAINING_SESSION_BOOK.values);
        }
        break;
    }
  }

  void actionAfterSignSuccess({
    BuildContext context,
    TypeSign typeSign,
  }) {
    switch (typeSign) {
      case TypeSign.INCOME_TAX:
        showDialog(
          context: context,
          builder: (BuildContext context) => ShowDialogSignSuccess(
            onGoToList: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => IncomeTaxPage(
                    arguments: IncomeTaxArguments(),
                  ),
                ),
              );
            },
            onViewDetail: () {
              Navigator.of(context).pop();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => ViewDocumentPage(
                    arguments: ViewDocumentArguments(
                      fileData: state.fileResponse,
                      typeSign: typeSign,
                      title: AppStrings.electronicAttorney,
                    ),
                  ),
                ),
              );
            },
          ),
          barrierDismissible: false,
        );
        break;
      case TypeSign.CONTRACT_OUTSIDE:
        showDialog(
          context: context,
          builder: (BuildContext context) => ShowDialogSignSuccess(
            onGoToList: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => DocumentOutsidePage(
                    arguments: DocumentOutsideArguments(type: TypeListContract.all),
                  ),
                ),
              );
            },
            onViewDetail: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => ViewDocumentPage(
                    arguments: ViewDocumentArguments(fileData: state.fileResponse, typeSign: typeSign),
                  ),
                ),
              );
            },
          ),
          barrierDismissible: false,
        );
        break;
      case TypeSign.PROBATION:
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: AppStrings.sign_contract_success,
            showIcon: false,
          ),
        );
        Future.delayed(const Duration(milliseconds: 2500), () {
          Navigator.of(context).pop(TypeSign.PROBATION);
        });
        break;
      case TypeSign.LABOR:
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: AppStrings.sign_contract_success,
            showIcon: false,
          ),
        );
        Future.delayed(const Duration(milliseconds: 2500), () {
          Navigator.of(context).pop(TypeSign.LABOR);
        });
        break;
      case TypeSign.ANNEX_CONTRACT:
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: AppStrings.sign_contract_success,
            showIcon: false,
          ),
        );
        Future.delayed(const Duration(milliseconds: 2500), () {
          Navigator.of(context).pop(TypeSign.ANNEX_CONTRACT);
        });
        break;
      case TypeSign.COMMIT:
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: AppStrings.sign_commit_success,
            showIcon: false,
          ),
        );
        Future.delayed(const Duration(milliseconds: 2500), () {
          Navigator.of(context).pop(TypeSign.COMMIT);
        });
        break;
      case TypeSign.CERTIFICATE_COMMIT:
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: AppStrings.sign_commit_success,
            showIcon: false,
          ),
        );
        Future.delayed(const Duration(milliseconds: 2500), () {
          Navigator.of(context).pop(TypeSign.CERTIFICATE_COMMIT);
        });
        break;
      case TypeSign.SOCIAL_INSURANCE:
        Navigator.of(context).pop(true);
        break;
      case TypeSign.CERTIFICATION:
        showDialog(
          context: context,
          builder: (BuildContext context) => ShowDialogSignSuccess(
            onGoToList: () {
              Navigator.of(context).pop(true);
            },
            onViewDetail: () {
              Navigator.of(context).pop();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => ViewDocumentPage(
                    arguments: ViewDocumentArguments(fileData: state.fileResponse, typeSign: typeSign, title: AppStrings.title_view_detail_certification),
                  ),
                ),
              );
            },
          ),
          barrierDismissible: false,
        );
        break;
      case TypeSign.TRAINING_SESSION_BOOK:
        Navigator.of(context).pop(true);
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: AppStrings.sign_success,
            showIcon: false,
          ),
        );
        break;
        case TypeSign.TRAINING_ELECTRICAL_SAFETY_BOOK:
        Navigator.of(context).pop(true);
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: AppStrings.sign_success,
            showIcon: false,
          ),
        );
        break;
    }
  }

  void clearCreateOtpStatus() {
    emit(
      state.copyWith(
        createOtpStatus: LoadStatus.initial,
      ),
    );
  }

  void clearValidateStatus() {
    emit(
      state.copyWith(
        validateStatus: LoadStatus.initial,
      ),
    );
  }

  Future<void> validateSmartOtp(ValidateSmartOtpRequest body) async {
    try {
      emit(
        state.copyWith(
          validateStatus: LoadStatus.loading,
        ),
      );
      var result = await _smartOtpService.validateSmartOtp(body);
      if (result.status == 1) {
        emit(
          state.copyWith(
            validateStatus: LoadStatus.success,
            message: result.message,
          ),
        );
      } else {
        emit(
          state.copyWith(
            validateStatus: LoadStatus.failure,
            message: result.message,
          ),
        );
      }
    } catch (e) {
      print(e);
      emit(
        state.copyWith(
          validateStatus: LoadStatus.failure,
          message: 'Có lỗi xảy ra',
        ),
      );
    }
  }
}
