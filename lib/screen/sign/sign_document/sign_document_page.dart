import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/annex_contract_dto.dart';
import 'package:hstd/models/certification/request_create_certification.dart';
import 'package:hstd/models/commit/commit_dto.dart';
import 'package:hstd/models/commit/sign_commit_request.dart';
import 'package:hstd/models/contract_dto.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/models/smart_otp/validate_smart_otp_request.dart';
import 'package:hstd/preferences/data_center.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/screen/otp/otp_page.dart';
import 'package:hstd/screen/smart_otp/widget/active_smart_otp_dialog.dart';
import 'package:hstd/screen/smart_otp/widget/submit_smart_otp_dialog.dart';
import 'package:hstd/widget/dialog_auto_close.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:hstd/widget/sliver_grid_delegate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:signature/signature.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import 'sign_document_cubit.dart';

class SignDocumentArguments {
  final TypeSign typeSign;
  final int taxPaymentRegisterId;
  final FileData fileData;
  final RequestCreateCertification requestCreateCertification;
  final SignCommitRequest signCommitRequest;
  final CommitDto commit;
  final Contract contract;
  final int addDocumentStatus;
  final int entityId;

  SignDocumentArguments({
    this.typeSign,
    this.taxPaymentRegisterId,
    this.fileData,
    this.requestCreateCertification,
    this.signCommitRequest,
    this.commit,
    this.contract,
    this.addDocumentStatus,
    this.entityId,
  });
}

class SignDocumentPage extends StatelessWidget {
  final SignDocumentArguments arguments;

  const SignDocumentPage({
    Key key,
    this.arguments,
  }) : super(key: key);

  static Route route({SignDocumentArguments arguments}) {
    return MaterialPageRoute<void>(
      builder: (_) => new SignDocumentPage(
        arguments: arguments,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return SignDocumentCubit();
      },
      child: SignDocumentChildPage(
        arguments: arguments,
      ),
    );
  }
}

class SignDocumentChildPage extends StatefulWidget {
  final SignDocumentArguments arguments;

  const SignDocumentChildPage({
    this.arguments,
    Key key,
  }) : super(key: key);

  @override
  State<SignDocumentChildPage> createState() => _SignDocumentChildPageState();
}

class _SignDocumentChildPageState extends State<SignDocumentChildPage> {
  SignDocumentCubit _cubit;
  final GlobalKey<SfPdfViewerState> _pdfViewerKey = GlobalKey();
  final SignatureController _controller = SignatureController(
    penStrokeWidth: 3,
    penColor: Color(0xFF283AD2),
    exportBackgroundColor: Colors.transparent,
    onDrawStart: () => print('Start draw!'),
    onDrawEnd: () => print('End draw!'),
  );

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData(
      type: widget.arguments.typeSign,
      taxPaymentRegisterId: widget.arguments.taxPaymentRegisterId,
      fileData: widget.arguments.fileData,
      commit: widget.arguments.commit,
      contract: widget.arguments.contract,
    );
    _controller.addListener(() {
      if (_controller.isEmpty) {
        _cubit.setSigned(false);
      }
      if (_controller.isNotEmpty) {
        _cubit.setSigned(true);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SignDocumentCubit, SignDocumentState>(
      listenWhen: (previous, current) =>
          previous.createOtpStatus != current.createOtpStatus ||
          previous.signStautus != current.signStautus ||
          previous.validateStatus != current.validateStatus,
      listener: (context, state) async {
        if (state.createOtpStatus == LoadStatus.loading) {
          LoadingDialogTransparent.show(context);
        }
        if (state.createOtpStatus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          SharedPreferences sharedPreferences =
              await SharedPreferences.getInstance();
          String phoneNumber =
              sharedPreferences.getString(AppConstant.KEY_PHONE_NUMBER_VCC);
          bool result = await Navigator.push(
            context,
            OtpPage.route(
              arguments: OtpArguments(
                phoneNumber: phoneNumber,
                otp: state.otp,
                typeSign: widget.arguments.typeSign,
              ),
            ),
          );
          if (result!=null) {
            _cubit.clearCreateOtpStatus();
            _controller.toPngBytes().then((value) {
              _cubit.sign(
                type: widget.arguments.typeSign,
                sign: value,
                taxPaymentRegisterId: widget.arguments.taxPaymentRegisterId,
                requestCreateCertification:
                    widget.arguments.requestCreateCertification,
                signCommitRequest: widget.arguments.signCommitRequest,
                addDocumentStatus: widget.arguments.addDocumentStatus
              );
            });
          }
        }
        if (state.createOtpStatus == LoadStatus.failure) {
          LoadingDialogTransparent.hide(context);
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: state.message,
              showIcon: false,
            ),
          );
        }
        if (state.signStautus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          _cubit.actionAfterSignSuccess(
              context: context,
              typeSign: widget.arguments.typeSign,
          );
        }
        if (state.signStautus == LoadStatus.loading) {
          LoadingDialogTransparent.show(context);
        }
        if (state.signStautus == LoadStatus.failure) {
          LoadingDialogTransparent.hide(context);
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: state.message,
              showIcon: false,
            ),
          );
        }
        if (state.validateStatus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          _cubit.clearValidateStatus();
          _controller.toPngBytes().then((value) {
            _cubit.sign(
                type: widget.arguments.typeSign,
                sign: value,
                taxPaymentRegisterId: widget.arguments.taxPaymentRegisterId,
                requestCreateCertification:
                widget.arguments.requestCreateCertification,
                signCommitRequest: widget.arguments.signCommitRequest,
                addDocumentStatus: widget.arguments.addDocumentStatus
            );
          });
        }
        if (state.validateStatus == LoadStatus.loading) {
          LoadingDialogTransparent.show(context);
        }
        if (state.validateStatus == LoadStatus.failure) {
          LoadingDialogTransparent.hide(context);
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: state.message,
              showIcon: false,
            ),
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
            appBar: AppBar(
              title: Text(
                AppStrings.sign_electronic_text,
                style: TextStyle(color: Colors.black),
              ),
              leading: widget.arguments.typeSign != TypeSign.CONTRACT_OUTSIDE ?
              IconButton(
                icon: Icon(Icons.arrow_back, color: Colors.black,),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ) : SizedBox(),
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              centerTitle: true,
              elevation: 0.5,
            ),
            body: SafeArea(
              child: widget.arguments.typeSign ==
                  TypeSign.INCOME_TAX ?
                  _buildBodyIncomeTaxWidget() :
              _buildBodyWidget(),
            ),
            bottomNavigationBar: _buildBottomWidget());
      },
    );
  }

  Widget _buildBodyWidget() {
    return BlocBuilder<SignDocumentCubit, SignDocumentState>(
      buildWhen: (previous, current) =>
          previous.loadDataStatus != current.loadDataStatus,
      builder: (context, state) {
        if (state.loadDataStatus == LoadStatus.loading) {
          return LoadingIndicator();
        } else if (state.loadDataStatus == LoadStatus.failure) {
          return EmptyListWidget(
            onRefresh: () async {
              _cubit.loadInitialData(
                type: widget.arguments.typeSign,
                taxPaymentRegisterId: widget.arguments.taxPaymentRegisterId,
                fileData: widget.arguments.fileData,
                commit: widget.arguments.commit,
                contract: widget.arguments.contract,
              );
            },
          );
        } else {
          return Stack(
            children: [
              Column(
                children: <Widget>[
                  Expanded(
                    child: SfPdfViewer.memory(
                      base64.decode(state.fileData.data),
                      key: _pdfViewerKey,
                      canShowScrollStatus: true,
                      pageSpacing: 2.0,
                    ),
                  ),
                ],
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildBodyIncomeTaxWidget() {
    return BlocBuilder<SignDocumentCubit, SignDocumentState>(
      buildWhen: (previous, current) =>
          previous.loadDataStatus != current.loadDataStatus,
      builder: (context, state) {
        if (state.loadDataStatus == LoadStatus.loading) {
          return LoadingIndicator();
        } else if (state.loadDataStatus == LoadStatus.failure) {
          return EmptyListWidget(
            onRefresh: () async {
              _cubit.loadInitialData(
                type: widget.arguments.typeSign,
                taxPaymentRegisterId: widget.arguments.taxPaymentRegisterId,
                fileData: widget.arguments.fileData,
                commit: widget.arguments.commit,
                contract: widget.arguments.contract,
              );
            },
          );
        } else {
          return Stack(
            children: [
              Column(
                children: <Widget>[
                  Expanded(
                    child: SfPdfViewer.memory(
                      base64.decode(state.fileIncomeTaxDto.data),
                      key: _pdfViewerKey,
                      canShowScrollStatus: true,
                      pageSpacing: 2.0,
                    ),
                  ),
                ],
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildBottomWidget() {
    return BlocBuilder<SignDocumentCubit, SignDocumentState>(
      buildWhen: (previous, current) =>
          previous.accessTerms != current.accessTerms ||
          previous.loadDataStatus != current.loadDataStatus,
      builder: (context, state) {
        if (state.loadDataStatus == LoadStatus.success) {
          return Container(
            height: 140,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                new BoxShadow(
                  color: Color(0x14000000),
                  offset: new Offset(0.0, 0.0),
                  blurRadius: 5,
                  spreadRadius: 2,
                  blurStyle: BlurStyle.normal,
                ),
              ],
            ),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 14, 16, 16),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: state.accessTerms
                              ? AppColors.primaryColor
                              : Colors.white,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(
                            width: 1,
                            color: state.accessTerms
                                ? AppColors.primaryColor
                                : AppColors.outerSpaceColor,
                          ),
                        ),
                        child: Checkbox(
                          value: state.accessTerms,
                          onChanged: (value) {
                            _cubit.setAccessTerms(value);
                          },
                          side: BorderSide.none,
                          fillColor:
                              MaterialStateProperty.all<Color>(Colors.red),
                        ),
                      ),
                      SizedBox(
                        width: 12,
                      ),
                      Expanded(
                        child: Text(
                          AppStrings.access_terms,
                          style: AppTextStyle.outerSpaceS13W400,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.arguments.typeSign==TypeSign.SOCIAL_INSURANCE)...[
                  GridView(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight(
                      crossAxisCount: 2,
                      crossAxisSpacing: 0,
                      mainAxisSpacing: 0,
                      height: 65,
                    ),
                    children: [
                      PrimaryButton(
                        borderRadius: BorderRadius.circular(24),
                        title: AppStrings.edit,
                        titleStyle: AppTextStyle.blackTextS16W600Roadmap,
                        height: 44,
                        padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                        color: AppColors.backgroundCloseButton,
                        borderColor: AppColors.backgroundCloseButton,
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                      ),
                      Opacity(
                        opacity: state.accessTerms ? 1 : 0.5,
                        child: PrimaryButton(
                          borderRadius: BorderRadius.circular(24),
                          title: AppStrings.sign_electronic,
                          titleStyle: AppTextStyle.whiteS16W600,
                          height: 44,
                          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                          color: AppColors.primaryColor,
                          borderColor: AppColors.primaryColor,
                          onTap: () {
                            if (state.accessTerms) {
                              showDialog(
                                  context: context,
                                  barrierDismissible: true,
                                  builder: (BuildContext context) {
                                    return _bottomSheetSign();
                                  });
                            }
                          },
                        ),
                      ),
                    ],
                  )
                ] else ...[
                  Opacity(
                    opacity: state.accessTerms ? 1 : 0.5,
                    child: PrimaryButton(
                      borderRadius: BorderRadius.circular(24),
                      title: AppStrings.sign_electronic,
                      titleStyle: AppTextStyle.whiteS16W600,
                      height: 44,
                      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      color: AppColors.primaryColor,
                      borderColor: AppColors.primaryColor,
                      onTap: () {
                        if (state.accessTerms) {
                          showDialog(
                              context: context,
                              barrierDismissible: true,
                              builder: (BuildContext context) {
                                return _bottomSheetSign();
                              });
                        }
                      },
                    ),
                  ),
                ]
              ],
            ),
          );
        } else {
          return SizedBox();
        }
      },
    );
  }

  Widget _bottomSheetSign() {
    return BlocBuilder<SignDocumentCubit, SignDocumentState>(
      bloc: _cubit,
      buildWhen: (previous, current) => previous.signed != current.signed,
      builder: (context, state) {
        return Dialog(
          alignment: Alignment.bottomCenter,
          insetPadding: EdgeInsets.zero,
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            height: (MediaQuery.of(context).size.height * 0.4) + 180,
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 16,
                  right: 16,
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: SvgPicture.asset(
                      AppVectors.ic_delete_type_3,
                    ),
                  ),
                ),
                if (!state.signed) ...[
                  Align(
                    alignment: Alignment.center,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 25),
                      child: Text(
                        AppStrings.sign_here,
                        style: AppTextStyle.blackS14Normal,
                      ),
                    ),
                  ),
                ],
                Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Text(
                        AppStrings.employee_signature,
                        style: AppTextStyle.blackS16W600,
                      ),
                    ),
                    Divider(height: 1),
                    Opacity(
                      opacity: 0.8,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppColors.backgroundItemListView,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Stack(
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(5),
                                child: Signature(
                                  controller: _controller,
                                  backgroundColor:
                                      AppColors.backgroundItemListView,
                                  height:
                                      MediaQuery.of(context).size.height * 0.4,
                                  width: MediaQuery.of(context).size.width - 38,
                                ),
                              ),
                              Visibility(
                                visible: state.signed,
                                child: Positioned(
                                  top: 0,
                                  right: 0,
                                  child: InkWell(
                                    onTap: () {
                                      _controller.clear();
                                    },
                                    child: Container(
                                      width: 38,
                                      height: 38,
                                      color: AppColors.backgroundItemListView,
                                      // color: Colors.green,
                                      child: Padding(
                                        padding: const EdgeInsets.all(8),
                                        child: SvgPicture.asset(
                                          AppVectors.ic_clear_sign,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Opacity(
                      opacity: state.signed ? 1 : 0.5,
                      child: PrimaryButton(
                        borderRadius: BorderRadius.circular(24),
                        title: AppStrings.confirm,
                        titleStyle: AppTextStyle.whiteS16W600,
                        height: 48,
                        padding:
                            EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                        color: AppColors.primaryColor,
                        borderColor: AppColors.primaryColor,
                        onTap: () async {
                          if (state.signed) {
                            Navigator.of(context).pop();
                            var dataCenter = DataCenter.shared();
                            if(dataCenter.getUserDeviceId() != null && dataCenter.getUserDeviceId() == dataCenter.getDeviceId()){
                              var result = await showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return SubmitSmartOtpDialog(
                                    argument:
                                    SubmitSmartOtpArgument(
                                        entityId: widget.arguments?.entityId != null ? widget.arguments?.entityId.toString() : dataCenter.getUserCode(),
                                        entityType: widget.arguments?.typeSign?.name),
                                  );
                                },
                              );
                              if(result != null){
                                String otp = result as String;
                                _cubit.validateSmartOtp(
                                  ValidateSmartOtpRequest(
                                    otp: otp,
                                    entityId: widget.arguments?.entityId != null ? widget.arguments?.entityId.toString() : dataCenter.getUserCode(),
                                    entityType: widget.arguments?.typeSign?.name,
                                    deviceId: dataCenter.getUserDeviceId(),
                                  ),
                                );
                              }
                              return;
                            } else {
                              var result = await showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return ActiveSmartOtpDialog(
                                    smartOtpPageContext: context,
                                  );
                                },
                              );
                              if (result ?? false) {
                                _cubit.confirm(typeSign: widget.arguments.typeSign, addDocumentStatus: widget.arguments.addDocumentStatus);
                              }
                            }
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }
}
