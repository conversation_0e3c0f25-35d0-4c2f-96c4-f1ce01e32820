import 'dart:async';
import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/bloc/auth/authentication.dart';
import 'package:hstd/bloc/employee_family_bloc/employee_families_bloc.dart';
import 'package:hstd/bloc/info_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_images.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/auth_dto.dart';
import 'package:hstd/screen/advancemet_roadmap/advancement_roadmap.dart';
import 'package:hstd/screen/certification/ask_for_certification/ask_for_certification_page.dart';
import 'package:hstd/screen/income_tax/income_tax_page.dart';
import 'package:hstd/screen/information_salary/information_salary_page.dart';
import 'package:hstd/screen/information_salary/payment_periods/payment_periods_page.dart';
import 'package:hstd/screen/information_update/information_update.dart';
import 'package:hstd/screen/labor_protection/labor_protection_page.dart';
import 'package:hstd/screen/login_by_sdt/document_outside/document_outside_page.dart';
import 'package:hstd/screen/new_password.dart';
import 'package:hstd/screen/osh/menu_atvsld.dart';
import 'package:hstd/screen/working_on_holiday/working_on_holiday_list_pages/working_on_holiday_list_pages.dart';
import 'package:hstd/utils/utils.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../main.dart';
import '../models/notificaton/count_noti_not_read.dart';
import 'employee_profile/ki_information/ki_information.dart';
import 'employee_profile/reward_employees/reward_employees_page.dart';
import 'list_survey_screen.dart';
import 'notifications/navigator_noti_by_message.dart';
import 'notifications/notifications_page.dart';

class InformationScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _InformationScreenState();
  final bool isLLTN;

  InformationScreen({this.isLLTN});

  static Route route() {
    return MaterialPageRoute<void>(builder: (_) => new InformationScreen());
  }
}

class _InformationScreenState extends State<InformationScreen> {
  InfoBloc _infoBloc;
  EmployeeFamiliesBloc employeeFamiliesBloc;

  String bgStr;
  bool isLoading = true;

  /*listening*/
  StreamSubscription _listeningAuth;
  bool _isInit = true;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    initData();
  }

  void initData() async {
    if (_isInit) {
      if (_infoBloc == null) {
        _infoBloc = new InfoBloc();
      }

      if (employeeFamiliesBloc == null) {
        employeeFamiliesBloc = new EmployeeFamiliesBloc();
      }

      await employeeFamiliesBloc.getEmployeeById(
          BlocProvider.of<AuthenticationBloc>(context)
                  .state
                  .user
                  .employeeCode ??
              "");
      _isInit = false;
    }
  }

  @override
  void initState() {
    init();
    super.initState();
  }

  void init() async {
    _infoBloc = new InfoBloc();
    _infoBloc.getContracts();
    _infoBloc.getAnnexContract();
    _infoBloc.countDataNotRead();

    SharedPreferences sharedPreferences =
    await SharedPreferences.getInstance();
    bgStr = sharedPreferences.get(AppConstant.KEY_BACKGROUND) ?? '';
    setState(() {
      isLoading = false;
    });
    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage message) {
      if (message != null) {
        navigateNotiByMessage(context, message);
      }
      });
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      RemoteNotification notification = message.notification;
      AndroidNotification android = message.notification?.android;
      if (notification != null && android != null) {
        flutterLocalNotificationsPlugin.show(
            notification.hashCode,
            notification.title,
            notification.body,
            NotificationDetails(
              android: AndroidNotificationDetails(
                channel.id,
                channel.name,
                channelDescription: channel.description
              ),
            ));
      }
      initLocalNotifications(context, message);
    });
    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      print('onMessageOpenedApp event was published!');
      navigateNotiByMessage(context, message);
    });


  }

  _listening(context) {
    _listeningAuth = _infoBloc.isAuthorization.listen((event) {
      if (event != null && event == false)
        BlocProvider.of<AuthenticationBloc>(context)
          ..add(AuthenticationLogoutRequested());
    });
  }

  @override
  void dispose() {
    super.dispose();
    _infoBloc.dispose();
  }

  Widget build(BuildContext context) {
    ProfileResponse user =
        BlocProvider.of<AuthenticationBloc>(context).state.user;
    if (_listeningAuth == null) {
      _listening(context);
    }
    double height = MediaQuery.of(context).size.height;
    double width = MediaQuery.of(context).size.width;

    List<ItemGridview> listItems = <ItemGridview>[

      ItemGridview(
          icon: AppVectors.ic_infoCollected,
          title: AppStrings.infomation_collected,
          ontap: () {
            Navigator.of(context).push(
              MaterialPageRoute<void>(
                builder: (_) => AskForCertificationPage(),
              ),
            );
            // Navigator.of(context).push(SalaryInfoScreen.route());
          }),
      ItemGridview(
          icon: AppVectors.ic_kpi,
          title: AppStrings.kpi_employee,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => KIEmployeeInformation(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.icSalary,
          title: AppStrings.salary,
          ontap: () {
            Navigator.of(context).push(ListServeyScreen.route());
          }),
      // ItemGridview(
      //     icon: AppVectors.ic_signDocument,
      //     title: AppStrings.sign_document,
      //     ontap: () {
      //       Navigator.of(context).push(ListContractsAppendixScreen.route());
      //     }),
      ItemGridview(
          icon: AppVectors.ic_protectionGear,
          title: AppStrings.protection_gear,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LaborProtectionPage(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_chungTu,
          title: AppStrings.thue_TNCN,
          ontap: () {
            // Navigator.of(context).push(ListTaxVouchersScreen.route());
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => IncomeTaxPage(
                  arguments: IncomeTaxArguments(),
                ),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_cardSafety,
          title: AppStrings.card_labor_safety,
          ontap: () {
            Navigator.of(context).push(MenuOSHScreen.route());
          }),
      ItemGridview(
          icon: AppVectors.ic_roadmap,
          title: AppStrings.advancement_roadmap,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AdvancementRoadmapScreen(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_bhxh,
          title: AppStrings.inforSocialInsurance,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => InformationUpdateScreen(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.medal_star,
          title: AppStrings.rewardEmployee,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RewardEmployeesPage(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.working_on_holiday,
          title: AppStrings.workingOnHoliday,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => WorkingOnHolidayListPages(),
              ),
            );
          }),
    ];

    List<ItemGridview> listItemsLLTN = <ItemGridview>[
      ItemGridview(
          icon: AppVectors.ic_infoCollected,
          title: AppStrings.infomation_salary,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PaymentPeriodsPage(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_signDocument,
          title: AppStrings.sign_document,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DocumentOutsidePage(
                  arguments: DocumentOutsideArguments(
                    type: TypeListContract.all
                  ),
                ),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_cardSafety,
          title: AppStrings.card_labor_safety,
          ontap: () {
            Navigator.of(context).push(MenuOSHScreen.route());
          }),
      ItemGridview(
          icon: "assets/ic_lock.svg",
          title: "Đổi mật khẩu",
          ontap: () {
            Navigator.of(context).push(NewPassWordScreen.route());
          }),
    ];

    if (isLoading) {
      return LoadingIndicator();
    } else
    return Stack(
      children: [
          Positioned(
            top: 0,
            child: bgStr.isEmpty
                ? Image.asset(
                    AppImages.ic_home_logo,
                    width: MediaQuery.of(context).size.width,
                    fit: BoxFit.fill,
                    height: MediaQuery.of(context).size.height*0.4,
                  )
                : Image.memory(
                    base64.decode(bgStr.split(',').last),
                    width: MediaQuery.of(context).size.width,
                    fit: BoxFit.fill,
                    height: MediaQuery.of(context).size.height*0.4,
                  ),
          ),
          Positioned(
          top: height * 0.1,
          child: Padding(
            padding: const EdgeInsets.only(left: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Chào ngày mới, ${Utils.getUserName(user.fullName)}',
                  style: AppTextStyle.whiteS18W600Quicksand,
                ),
                SizedBox(height: 10,),
                SizedBox(
                  width: width * 0.5,
                  child: Text(
                    user.unitName??'',
                    style: AppTextStyle.whiteS14,
                  ),
                ),
              ],
            ),
          ),
        ),
          StreamBuilder<CountNotiNotRead>(
            initialData: null,
            stream: _infoBloc.countNoti$,
            builder: (context, snapshot) {
              return Positioned(
                top: height * 0.1,
                right: 16,
                child: Container(
                  child: Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 10),
                        child: InkWell(
                            onTap: () async {
                              Navigator.of(context).push(
                                MaterialPageRoute<void>(
                                  builder: (_) => ListNotificationPage(),
                                ),
                              ).whenComplete(() => _infoBloc.countDataNotRead());
                            },
                            child: SvgPicture.asset(AppVectors.icNotification)),
                      ),
                      Positioned(
                        right: 0,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 3),
                          decoration: BoxDecoration(color: AppColors.primaryColor, borderRadius: BorderRadius.circular(100)),
                          child: Center(
                            child: Text(
                              '${snapshot.data?.data ?? 0}',
                              style: TextStyle(color: Colors.white, fontSize: 10),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              );
            },
          ),
          Column(
          children: [
            SizedBox(
              height: height * 0.35,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SingleChildScrollView(
                  child: Container(
                    height: height * 0.65 - 56,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: GridView.builder(
                      scrollDirection: Axis.vertical,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 0,
                        childAspectRatio: 1.2
                        // childAspectRatio: 6 <= 6 ? area * 0.49 : area * 0.56
                      ),
                      itemCount: (widget.isLLTN == null || widget.isLLTN == false)
                          ? listItems.length
                          : listItemsLLTN.length,
                      itemBuilder: (context, position) {
                        return _itemGrid(
                            icon:
                            (widget.isLLTN == null || widget.isLLTN == false)
                                ? listItems[position].icon
                                : listItemsLLTN[position].icon,
                            title:
                            (widget.isLLTN == null || widget.isLLTN == false)
                                ? listItems[position].title
                                : listItemsLLTN[position].title,
                            ontap: () {
                              (widget.isLLTN == null || widget.isLLTN == false)
                                  ? listItems[position].ontap()
                                  : listItemsLLTN[position].ontap();
                            });
                      },
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _itemGrid({Function ontap, String title, String icon}) {
    return InkWell(
      onTap: () {
        ontap();
      },
      child: Container(
        child: Column(children: [
          Container(
            height: 52,
            width: 52,
            decoration: BoxDecoration(
              color: AppColors.pinkItemHome,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Align(
              alignment: Alignment.center,
              child: SvgPicture.asset(
                icon,
                height: 30,
                width: 30,
              ),
            ),
          ),
          SizedBox(
            height: 6,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 20,
              ),
              child: Text(
                title,
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ]),
      ),
    );
  }
}

class ItemGridview {
  String icon;
  String title;
  Function ontap;

  ItemGridview({this.icon, this.ontap, this.title});
}
