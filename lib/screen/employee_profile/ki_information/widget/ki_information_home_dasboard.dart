import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/extension/date_extention.dart';
import 'package:hstd/preferences/data_center.dart';
import 'package:hstd/screen/employee_profile/ki_information/ki_information.dart';
import 'package:hstd/screen/employee_profile/ki_information/model/kpi_request.dart';
import 'package:hstd/widget/item_kpi_month.dart';

import '../ki_information_cubit.dart';

class KiInformationHome extends StatelessWidget {
  const KiInformationHome({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return KpiCubit();
      },
      child: const KiInformationHomeChild(),
    );
  }
}

class KiInformationHomeChild extends StatefulWidget {
  const KiInformationHomeChild({Key key}) : super(key: key);

  @override
  State<KiInformationHomeChild> createState() => _KiInformationHomeState();
}

class _KiInformationHomeState extends State<KiInformationHomeChild> {
  KpiCubit _cubit;
  DateTime now = DateTime.now();
  final dataCenter = DataCenter.shared();

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.getKPI(KpiRequest(
        employeeCode: dataCenter.getUserCode(),
        monthStart: 1,
        yearStart: now.year,
        monthTo: now.month,
        yearTo: now.year));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<KpiCubit, KpiState>(
        buildWhen: (previous, current) =>
        (previous.loadStatus != current.loadStatus) ||
            (previous.loadStatusUpdate != current.loadStatusUpdate),
        builder: (context, state) {
          return InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => KIEmployeeInformation(),
                ),
              );
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16),
              padding: EdgeInsets.all(12),
              width: MediaQuery
                  .of(context)
                  .size
                  .width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    offset: Offset(0, 1),
                    color: Colors.black12,
                    blurRadius: 4,
                  )
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "KI cá nhân",
                            style: AppTextStyle.blackColorS14W600,
                          ),
                          SizedBox(
                            height: 5,
                          ),
                          Text(
                            "Năm 2024",
                            style: AppTextStyle.blackS12Normal
                                .copyWith(color: AppColors.textInfoColor),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Text(
                            "Xem thêm",
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: AppColors.textInfoColor,
                            ),
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Icon(
                            Icons.arrow_forward_ios,
                            size: 12,
                          )
                        ],
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  if (state.kpiResult != null &&
                      (state.kpiResult?.totalKi ?? 0) > 0)
                    Row(
                      children: [
                        if((state.kpiResult?.totalKiA ?? 0) > 0)...[
                          ItemKpiMonth(title: "A",
                              count: state.kpiResult?.totalKiA,
                              totalCount: state.kpiResult?.totalKi),
                          SizedBox(
                            width: 5,
                          ),
                        ],
                        if((state.kpiResult?.totalKiB ?? 0) > 0)...[
                          ItemKpiMonth(title: "B",
                              count: state.kpiResult?.totalKiB,
                              totalCount: state.kpiResult?.totalKi),
                          SizedBox(
                            width: 5,
                          ),
                        ],
                        if((state.kpiResult?.totalKiC ?? 0) > 0)...[
                          ItemKpiMonth(title: "C",
                              count: state.kpiResult?.totalKiC,
                              totalCount: state.kpiResult?.totalKi),
                          SizedBox(
                            width: 5,
                          ),
                        ],
                        if((state.kpiResult?.totalKiD ?? 0) > 0)
                        ItemKpiMonth(title: "D",
                            count: state.kpiResult?.totalKiD,
                            totalCount: state.kpiResult?.totalKi),
                      ],
                    )
                ],
              ),
            ),
          );
        });
  }
}
