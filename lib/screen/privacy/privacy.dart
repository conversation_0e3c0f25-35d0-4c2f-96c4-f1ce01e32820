import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hstd/widget/dropdown/privacy_app_setting.dart';
import 'package:hstd/widget/layout_page.dart';
import 'package:hstd/widget/switch_card.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:permission_handler/permission_handler.dart';

class PrivacyScreen extends StatefulWidget {
  @override
  _PrivacyScreenState createState() => _PrivacyScreenState();
}

class _PrivacyScreenState extends State<PrivacyScreen>
    with WidgetsBindingObserver {
  bool accessCamera = false;
  bool accessLocation = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    getPermission();
  }

  void getPermission() async {
    var camera = await Permission.camera.isGranted;
    var location = await Permission.location.isGranted;
    setState(() {
      accessCamera = camera;
      accessLocation = location;
    });

    // if (accessLocation ?? false) {
    //   await BackgroundLocation.setAndroidNotification(
    //     title: 'Hồ sơ điện tử đan',
    //     message: 'Background location in progress',
    //     icon: '@mipmap/ic_launcher',
    //   );
    //   //await BackgroundLocation.setAndroidConfiguration(1000);
    //   await BackgroundLocation.startLocationService(distanceFilter: 0.1);
    //   BackgroundLocation.getLocationUpdates((location) async {
    //     try {
    //       var latitude = location.latitude.toString();
    //       var longitude = location.longitude.toString();
    //       var accuracy = location.accuracy.toString();
    //       var altitude = location.altitude.toString();
    //       var bearing = location.bearing.toString();
    //       var speed = location.speed.toString();
    //       var time = DateTime.fromMillisecondsSinceEpoch(location.time.toInt())
    //           .toString();
    //       print('''\n
    //                     Latitude:  $latitude
    //                     Longitude: $longitude
    //                     Altitude: $altitude
    //                     Accuracy: $accuracy
    //                     Bearing:  $bearing
    //                     Speed: $speed
    //                     Time: $time
    //                   ''');
    //       Dio dio = Dio();
    //
    //       Response response = await dio.post(
    //         'https://www.themealdb.com/api/json/v1/1/categories.php',
    //         // options: Options(
    //         //   headers: {
    //         //     'Content-Type': 'application/json',
    //         //   },
    //         // ),
    //         // data: {
    //         //   "employeeCode": "223982",
    //         //   "employeeName": "Test",
    //         //   "device": "Iphone",
    //         //   "deviceId": "Iphone",
    //         //   // "inspectTime": "yyyy-MM-dd HH:mm:ss",
    //         //   "longitude": "$longitude",
    //         //   "latitude": "$latitude"
    //         // },
    //       );
    //
    //       print(response.data);
    //     } catch (e) {
    //       print('Error: $e');
    //     }
    //   });
    // } else {
    //   BackgroundLocation.stopLocationService();
    // }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      getPermission();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Cài đặt quyền",
          style: TextStyle(color: Colors.black),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          10.height,
          SwitchCard(
            value: accessCamera,
            iconData: Icons.camera_alt,
            switchLabel: 'Truy cập camera',
            handleSwitch: (val) async {
              if (val) {
                var cameraStatus = await Permission.camera.status;
                print(cameraStatus);
                if (cameraStatus.isDenied || cameraStatus.isPermanentlyDenied) {
                  PrivacyAppSetting.openModalAlert(
                    context,
                    title: "Thông báo",
                    content: "Cho phép VCC - Hồ sơ điện tử truy cập camera?",
                    onAccept: () {
                      Navigator.of(context).pop();
                      openAppSettings();
                    },
                  );
                }
              } else {
                PrivacyAppSetting.openModalConfirm(context, pricacy: "Camera",
                    onAccept: () {
                  Navigator.of(context).pop();
                  openAppSettings();
                });
              }
            },
          ),
          10.height,
          SwitchCard(
            value: accessLocation,
            iconData: Icons.location_on_outlined,
            switchLabel: 'Truy cập vị trí',
            handleSwitch: (val) async {
              // var locationStatus = await Permission.location.status;
              // print(locationStatus);
              // if (locationStatus.isDenied ||
              //     locationStatus.isPermanentlyDenied) {
              //   PrivacyAppSetting.openModalAlert(
              //     context,
              //     title: "Xác nhận",
              //     content:
              //         "Đồng ý cho phép TCT Cổ phần công trình Viettel truy cập GPS mục đích chấm công trực/ làm thêm Lễ/Tết",
              //     onAccept: () {
              //       Navigator.of(context).pop();
              //       openAppSettings();
              //     },
              //   );
              // } else {
              //   PrivacyAppSetting.openModalConfirm(
              //     context,
              //     pricacy: "Vị trí",
              //     onAccept: () {
              //       Navigator.of(context).pop();
              //       openAppSettings();
              //     },
              //   );
              // }
            },
          ),
        ],
      ),
    );
  }
}
