import 'dart:async';
import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hstd/bloc/auth/authentication_bloc.dart';
import 'package:hstd/bloc/employee_family_bloc/employee_families_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_images.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/main.dart';
import 'package:hstd/models/auth_dto.dart';
import 'package:hstd/models/gifts/event_banner.dart';
import 'package:hstd/screen/gifts/gift_informations/gift_information_page.dart';
import 'package:hstd/screen/gifts/gift_not_registration/gift_not_registration_page.dart';
import 'package:hstd/screen/gifts/gift_registration/gift_registration_page.dart';
import 'package:hstd/screen/income_tax/income_tax_menu/income_tax_menu_page.dart';
import 'package:hstd/screen/information_salary/payment_periods/payment_periods_page.dart';
import 'package:hstd/screen/working_on_holiday/working_on_holiday_list_pages/working_on_holiday_list_pages.dart';
import 'package:hstd/utils/utils.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../bloc/info_bloc.dart';
import '../models/notificaton/count_noti_not_read.dart';
import 'advancemet_roadmap/advancement_roadmap.dart';
import 'employee_profile/ki_information/widget/ki_information_home_dasboard.dart';
import 'employee_profile/reward_employees/reward_employees_page.dart';
import 'information_screen.dart';
import 'information_update/information_update.dart';
import 'labor_protection/labor_protection_page.dart';
import 'list_survey_screen.dart';
import 'login_by_sdt/document_outside/document_outside_page.dart';
import 'new_password.dart';
import 'notifications/navigator_noti_by_message.dart';
import 'notifications/notifications_page.dart';
import 'osh/menu_atvsld.dart';
import 'resignation/resignation_page.dart';
import 'vacation/list_vacation_page/list_vacation_page.dart';

class InformationScreenNew extends StatefulWidget {
  const InformationScreenNew({Key key, this.isLLTN}) : super(key: key);
  final bool isLLTN;

  @override
  State<InformationScreenNew> createState() => _InformationScreenNewState();
}

class _InformationScreenNewState extends State<InformationScreenNew> {
  InfoBloc _infoBloc;
  EmployeeFamiliesBloc employeeFamiliesBloc;

  String bgStr = "";
  bool isLoading = true;

  /*listening*/
  StreamSubscription _listeningAuth;
  bool _isInit = true;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    initData();
  }

  void initData() async {
    if (_isInit) {
      if (_infoBloc == null) {
        _infoBloc = new InfoBloc();
      }

      if (employeeFamiliesBloc == null) {
        employeeFamiliesBloc = new EmployeeFamiliesBloc();
      }

      await employeeFamiliesBloc.getEmployeeById(
          BlocProvider.of<AuthenticationBloc>(context)
                  .state
                  .user
                  .employeeCode ??
              "");
      _isInit = false;
    }
  }

  @override
  void initState() {
    init();
    super.initState();
  }

  void init() async {
    _infoBloc = new InfoBloc();
    _infoBloc.getContracts();
    _infoBloc.getAnnexContract();
    _infoBloc.countDataNotRead();
    _infoBloc.getBannerHome();

    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    // bgStr = sharedPreferences.getString(AppConstant.KEY_GIFT_BANNER) ?? '';

    try {
      final value = await _infoBloc.getBannerHome();
      if (value != null && value.file != null && value.file.data != null && !value.file.data.isEmpty) {
        bgStr = value.file.data;
      }
      if (bgStr == '') {
        bgStr = sharedPreferences.get(AppConstant.KEY_BACKGROUND) ?? '';
      }
    } catch(e) {}
    setState(() {
      isLoading = false;
    });
    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage message) {
      if (message != null) {
        navigateNotiByMessage(context, message);
      }
    });
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      RemoteNotification notification = message.notification;
      AndroidNotification android = message.notification?.android;
      if (notification != null && android != null) {
        flutterLocalNotificationsPlugin.show(
            notification.hashCode,
            notification.title,
            notification.body,
            NotificationDetails(
              android: AndroidNotificationDetails(channel.id, channel.name,
                  channelDescription: channel.description),
            ));
      }
      initLocalNotifications(context, message);
    });
    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      print('onMessageOpenedApp event was published!');
      navigateNotiByMessage(context, message);
    });
  }

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height;
    double width = MediaQuery.of(context).size.width;
    ProfileResponse user =
        BlocProvider.of<AuthenticationBloc>(context).state.user;
    List<ItemGridview> listItems = <ItemGridview>[
      ItemGridview(
          icon: AppVectors.ic_roadmap,
          title: AppStrings.advancement_roadmap,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AdvancementRoadmapScreen(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.icHardHat,
          title: AppStrings.protection_gear,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LaborProtectionPage(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_chungTu,
          title: AppStrings.thue_TNCN,
          ontap: () {
            // Navigator.of(context).push(ListTaxVouchersScreen.route());
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => IncomeTaxMenuPage(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_cardSafety,
          title: AppStrings.card_labor_safety,
          ontap: () {
            Navigator.of(context).push(MenuOSHScreen.route());
          }),
      ItemGridview(
          icon: AppVectors.ic_bhxh,
          title: AppStrings.inforSocialInsurance,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => InformationUpdateScreen(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.medal_star,
          title: AppStrings.rewardEmployee,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RewardEmployeesPage(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.working_on_holiday,
          title: AppStrings.workingOnHoliday,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => WorkingOnHolidayListPages(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_delete_account_2,
          title: AppStrings.resignation,
          ontap: () {
            Navigator.of(context).push(ResignationPage.route());
          }),
      ItemGridview(
          icon: AppVectors.icSalary,
          title: AppStrings.salary,
          ontap: () {
            Navigator.of(context).push(ListServeyScreen.route());
          }),
      ItemGridview(
          icon: AppVectors.ic_holiday,
          title: "Đăng ký nghỉ dưỡng",
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ListVacationPage(),
              ),
            );
          }),
    ];

    List<ItemGridview> listItemsLLTN = <ItemGridview>[
      ItemGridview(
          icon: AppVectors.ic_infoCollected,
          title: AppStrings.infomation_salary,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PaymentPeriodsPage(),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_signDocument,
          title: AppStrings.sign_document,
          ontap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => DocumentOutsidePage(
                  arguments:
                      DocumentOutsideArguments(type: TypeListContract.all),
                ),
              ),
            );
          }),
      ItemGridview(
          icon: AppVectors.ic_cardSafety,
          title: AppStrings.card_labor_safety,
          ontap: () {
            Navigator.of(context).push(MenuOSHScreen.route());
          }),
      ItemGridview(
          icon: "assets/ic_lock.svg",
          title: "Đổi mật khẩu",
          ontap: () {
            Navigator.of(context).push(NewPassWordScreen.route());
          }),
    ];
    if (isLoading) {
      return LoadingIndicator();
    } else
      return Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Stack(
              children: [
                Image.asset(
                  AppImages.ic_home_logo_header,
                  width: MediaQuery.of(context).size.width,
                  fit: BoxFit.fill,
                  height: 120,
                ),
                Positioned(
                  bottom: 0,
                  child: Container(
                    width: width,
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Row(
                        children: [
                          Stack(
                            children: [
                              Center(
                                child: Icon(
                                  Icons.circle,
                                  color: Color(0xFFB5B4B4),
                                  size: 40,
                                ),
                              ),
                              Center(
                                child: Container(
                                  height: 40,
                                  width: 40,
                                  child: Center(
                                    child: Text(
                                      Utils.getAvatarFromName(user.fullName),
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 13,
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${user.fullName ?? ""}',
                                  style: AppTextStyle.whiteS16W600,
                                ),
                                Text(
                                  '${user.unitName ?? ""}',
                                  style: AppTextStyle.outerSpaceS13W400
                                      .copyWith(color: Colors.white),
                                )
                              ],
                            ),
                          ),
                          StreamBuilder<CountNotiNotRead>(
                            initialData: null,
                            stream: _infoBloc.countNoti$,
                            builder: (context, snapshot) {
                              return Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    color: AppColors.bgAdRoadmapIncome
                                        .withOpacity(0.4)),
                                width: 40,
                                height: 40,
                                child: Center(
                                  child: Stack(
                                    children: [
                                      InkWell(
                                          onTap: () async {
                                            Navigator.of(context)
                                                .push(
                                                  MaterialPageRoute<void>(
                                                    builder: (_) =>
                                                        ListNotificationPage(),
                                                  ),
                                                )
                                                .whenComplete(() => _infoBloc
                                                    .countDataNotRead());
                                          },
                                          child: SvgPicture.asset(
                                              AppVectors.icNotificationRing)),
                                      Positioned(
                                        right: 0,
                                        child: Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 3),
                                          decoration: BoxDecoration(
                                              color: AppColors.primaryColor,
                                              borderRadius:
                                                  BorderRadius.circular(100)),
                                          child: Center(
                                            child: Text(
                                              '${snapshot.data?.data ?? 0}',
                                              style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 10),
                                            ),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 20,
                    ),
                    StreamBuilder<EventBanner>(
                      initialData: null,
                      stream: _infoBloc.bannerHome$,
                      builder: (context, snapshot) {
                        bool isShowBanner = snapshot.data != null &&
                            snapshot.data.file != null &&
                            snapshot.data.file.data != null &&
                            snapshot.data.file.data.isNotEmpty;
                        print("isShowBanner: ${isShowBanner}");
                        print("bgStr: ${bgStr}");
                        return isShowBanner
                            ? GestureDetector(
                                child: Container(
                                  height: 160,
                                  margin: EdgeInsets.symmetric(horizontal: 16),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(12.0),
                                    child: snapshot.data != null
                                        ? Image.memory(
                                            base64.decode(
                                                snapshot.data.file.data ?? ""),
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            fit: BoxFit.fill,
                                          )
                                        : SizedBox(),
                                  ),
                                ),
                                onTap: () async {
                                  if (snapshot.data.configValue
                                      .contains('/gift-regs')) {
                                    String year = snapshot.data.configValue
                                        .substring(
                                            snapshot.data.configValue
                                                    .indexOf("=") +
                                                1,
                                            snapshot.data.configValue.length);
                                    SharedPreferences sharedPreferences =
                                        await SharedPreferences.getInstance();
                                    sharedPreferences.setString(
                                        AppConstant.KEY_YEAR_CONFIG, year);
                                    _infoBloc
                                        .checkGiftRegistered(context, year)
                                        .then((value) {
                                      final registrationDeadline =
                                      DateFormat('dd/MM/yyyy').parse(value
                                          .giftGroupsResponse.registrationDeadline);
                                      final currentDate = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
                                      if (value != null) {
                                        if (value.listGiftRegDetail == null ||
                                            value.listGiftRegDetail.isEmpty) {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => value
                                                          .giftGroupsResponse
                                                          .lockRegistration ==
                                                      "LOCKED" || registrationDeadline.isBefore(currentDate)
                                                  ? GiftNotRegistrationPage()
                                                  : GiftRegistrationPage(),
                                            ),
                                          );
                                        } else {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  GiftInformationPage(
                                                argument: GiftInformationArgument(
                                                    isLocked: value
                                                            .giftGroupsResponse
                                                            .lockRegistration ==
                                                        "LOCKED" || registrationDeadline.isBefore(currentDate)),
                                              ),
                                            ),
                                          );
                                        }
                                      }
                                    });
                                  }
                                },
                              )
                            : bgStr.isNotEmpty
                                ? Container(
                                    height: 160,
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 16),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(12.0),
                                      child: Image.memory(
                                        base64.decode(bgStr.split(',').last),
                                        width:
                                            MediaQuery.of(context).size.width,
                                        fit: BoxFit.fill,
                                      ),
                                    ),
                                  )
                                : SizedBox();
                      },
                    ),
                    SizedBox(height: 20),
                    Wrap(
                      // spacing: 8.0, // gap between adjacent chips
                      // runSpacing: 4.0, // gap between lines
                      direction: Axis.horizontal,
                      children: List.generate(
                        (widget.isLLTN == null || widget.isLLTN == false)
                            ? listItems.length
                            : listItemsLLTN.length,
                            (position) => _itemGrid(
                            icon: (widget.isLLTN == null ||
                                widget.isLLTN == false)
                                ? listItems[position].icon
                                : listItemsLLTN[position].icon,
                            title: (widget.isLLTN == null ||
                                widget.isLLTN == false)
                                ? listItems[position].title
                                : listItemsLLTN[position].title,
                            ontap: () {
                              (widget.isLLTN == null || widget.isLLTN == false)
                                  ? listItems[position].ontap()
                                  : listItemsLLTN[position].ontap();
                            }),
                      ),
                    ),
                    if (widget.isLLTN == null || widget.isLLTN == false)
                      KiInformationHome()
                  ],
                ),
              ),
            )
          ],
        ),
      );
  }

  Widget _itemGrid({Function ontap, String title, String icon}) {
    return InkWell(
      onTap: () {
        ontap();
      },
      child: Container(
        width: MediaQuery.of(context).size.width / 4,
        height: 100,
        child: Column(children: [
          Container(
            height: 52,
            width: 52,
            decoration: BoxDecoration(
              color: AppColors.pinkItemHome,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Align(
              alignment: Alignment.center,
              child: SvgPicture.asset(
                icon,
                height: 30,
                width: 30,
                color: Colors.red,
              ),
            ),
          ),
          SizedBox(
            height: 4,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 5,
              ),
              child: Text(
                title,
                style: TextStyle(fontSize: 13),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ]),
      ),
    );
  }
}
