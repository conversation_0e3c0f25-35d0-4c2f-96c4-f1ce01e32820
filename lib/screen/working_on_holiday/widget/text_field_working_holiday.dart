import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hstd/common/app_colors.dart';

class TextFieldWorkingHoliday extends StatelessWidget {
  const TextFieldWorkingHoliday(
      {this.title,
      this.onTap,
      this.suffixIcon,
      this.controller,
      this.readOnly = false,
      this.tapOnly = false,
      this.minLines = 1,
      this.maxLength,
        this.onChanged,
        this.keyboardType,
        this.validator,
        this.onFieldSubmitted,
        this.inputFormatter,
        this.textCapitalization,
      Key key})
      : super(key: key);
  final Function onTap;
  final String title;
  final Widget suffixIcon;
  final TextEditingController controller;
  final bool readOnly, tapOnly;
  final int minLines;
  final int maxLength;
  final Function(String) onChanged;
  final Function(String) onFieldSubmitted;
  final TextInputType keyboardType;
  final String Function(String) validator;
  final List<TextInputFormatter> inputFormatter;
  final TextCapitalization textCapitalization;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: readOnly ? AppColors.greyButtonColor : Colors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(10),
        ),
        border: Border.all(
          color: AppColors.greyRoadmap,
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        style: TextStyle(color: readOnly ? AppColors.textInfoColor : null),
        readOnly: readOnly || tapOnly,
        onTap: () async {
          if(!readOnly)
          onTap();
        },
        keyboardType: keyboardType ?? TextInputType.text,
        onChanged: onChanged,
        onFieldSubmitted: onFieldSubmitted,
        inputFormatters: inputFormatter ?? [LengthLimitingTextInputFormatter(maxLength)],
        textCapitalization: textCapitalization ?? TextCapitalization.none,
        minLines: minLines,
        maxLines: null,
        maxLength: maxLength,
        decoration: InputDecoration(
          label: Container(
            margin: EdgeInsets.only(bottom: minLines >= 3 ? 50 : 0),
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: title,
                    style: TextStyle(
                      color: AppColors.textInfoColor,
                    ),
                  ),
                  TextSpan(text: ' *', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.fromLTRB(
            10.0,
            0.0,
            20.0,
            0.0,
          ),
          suffixIcon: suffixIcon,
        ),
        validator: validator,
      ),
    );
  }
}
