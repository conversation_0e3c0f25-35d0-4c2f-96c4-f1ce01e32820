import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/working_on_holiday/current_period_response_model.dart';
import 'package:hstd/models/working_on_holiday/working_holiday_request.dart';
import 'package:hstd/repositories/working_on_holiday_service.dart';

import '../../../models/working_on_holiday/working_on_holiday_dto.dart';

part 'register_working_on_holiday_state.dart';

class RegisterWorkingOnHolidayCubit
    extends Cubit<RegisterWorkingOnHolidayState> {
  RegisterWorkingOnHolidayCubit()
      : super(const RegisterWorkingOnHolidayState());
  WorkingOnHolidayService workingOnHolidayService =
      WorkingOnHolidayServiceImp();

  Future<void> loadInitialData({int workingOnHolidayId}) async {
    await getCurrentPeriod();
    if(workingOnHolidayId != null){
      await getWorkById(workingOnHolidayId: workingOnHolidayId);
    }
  }

  Future<void> getCurrentPeriod() async {
    try {
      emit(state.copyWith(loadDataStatus: LoadStatus.loading));
      //Todo: add API calls
      var result = await workingOnHolidayService.getCurrentPeriod();
      await getTypeRegister();
      if (result != null) {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.success,
          initData: result.result,
        ));
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
        ));
      }
    } catch (e, s) {
      //Todo: should print exception here
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }

  Future<void> getWorkById({int workingOnHolidayId}) async {
    try {
      emit(state.copyWith(loadDataStatus: LoadStatus.loading));
      //Todo: add API calls
      var result = await workingOnHolidayService.getWorkById(id: workingOnHolidayId);
      if (result != null) {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.success,
          detailWorkingHoliday: result,
        ));
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
        ));
      }
    } catch (e, s) {
      //Todo: should print exception here
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }

  Future<void> getTypeRegister() async {
    try {
      //Todo: add API calls
      var result = await workingOnHolidayService.getTypeRegister();
      if (result != null) {
        emit(state.copyWith(
          typeRegister: result.data,
        ));
      }
    } catch (e, s) {
      print(e);
    }
  }

  Future<void> registerWorkingOnHoliday({WorkingOnHolidayRequest request,}) async {
    try {
      emit(state.copyWith(
        saveStatus: LoadStatus.loading,
      ));
      //Todo: add API calls
      var result = await workingOnHolidayService.registerWorkingOnHoliday(request: request);
      if (result != null && result.status == 1) {
        emit(state.copyWith(
          saveStatus: LoadStatus.success,
          messageSave: result.message,
        ));
      } else{
        emit(state.copyWith(
          saveStatus: LoadStatus.failure,
          messageSave: result.message,
        ));
      }
    } catch (e, s) {
      print(e);
      emit(state.copyWith(
        saveStatus: LoadStatus.failure,
        messageSave: e.toString(),
      ));
    }
  }

  void clearSaveStatus(){
    emit(
      state.copyWith(
        saveStatus: LoadStatus.initial,
        loadDataStatus: LoadStatus.initial,
      ),
    );
  }

  void updateVerify(bool checkVerify) {
    emit(
      state.copyWith(
        checkVerify: checkVerify
      ),
    );
  }
}
