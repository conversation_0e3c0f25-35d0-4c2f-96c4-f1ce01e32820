import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cupertino_datetime_picker/flutter_cupertino_datetime_picker.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/screen/working_on_holiday/widget/select_field_working_holiday.dart';
import 'package:hstd/screen/working_on_holiday/widget/text_field_working_holiday.dart';
import 'package:hstd/widget/dropdown/dialog_helper.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../models/working_on_holiday/working_holiday_request.dart';
import 'register_working_on_holiday_cubit.dart';

class RegisterWorkingOnHolidayPage extends StatelessWidget {
  const RegisterWorkingOnHolidayPage(
      {Key key, this.workingOnHolidayId, this.onlyView = false})
      : super(key: key);
  final int workingOnHolidayId;
  final bool onlyView;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return RegisterWorkingOnHolidayCubit();
      },
      child: RegisterWorkingOnHolidayChildPage(
        workingOnHolidayId: workingOnHolidayId,
        onlyView: onlyView,
      ),
    );
  }
}

class RegisterWorkingOnHolidayChildPage extends StatefulWidget {
  const RegisterWorkingOnHolidayChildPage(
      {Key key, this.workingOnHolidayId, this.onlyView})
      : super(key: key);
  final int workingOnHolidayId;
  final bool onlyView;

  @override
  State<RegisterWorkingOnHolidayChildPage> createState() =>
      _RegisterWorkingOnHolidayChildPageState();
}

class _RegisterWorkingOnHolidayChildPageState
    extends State<RegisterWorkingOnHolidayChildPage> {
  RegisterWorkingOnHolidayCubit _cubit;
  int valueSelect;
  DateTime endDateTime = DateTime.now();
  final tooltipkey = GlobalKey<State<Tooltip>>();
  TextEditingController dateTimeController = TextEditingController();
  TextEditingController addressWorkController = TextEditingController();
  TextEditingController headingWorkController = TextEditingController();
  TextEditingController detailWorkController = TextEditingController();

  Future showAndCloseTooltip() async {
    await Future.delayed(const Duration(milliseconds: 10));
    // tooltipkey.currentState.ensureTooltipVisible();
    final dynamic tooltip = tooltipkey.currentState;
    tooltip?.ensureTooltipVisible();
    await Future.delayed(const Duration(seconds: 4));
    // tooltipkey.currentState.deactivate();
    tooltip?.deactivate();
  }

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData(workingOnHolidayId: widget.workingOnHolidayId);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RegisterWorkingOnHolidayCubit,
            RegisterWorkingOnHolidayState>(
        listenWhen: (previous, current) =>
            previous.saveStatus != current.saveStatus ||
            previous.loadDataStatus != current.loadDataStatus ||
            previous.checkVerify != current.checkVerify,
        listener: (context, state) {
          if (state.saveStatus == LoadStatus.loading) {
            LoadingDialogTransparent.show(context);
          }
          if (state.saveStatus == LoadStatus.failure) {
            LoadingDialogTransparent.hide(context);
            _cubit.clearSaveStatus();
            DialogHelper.showSnackBar(context,
                message: state.messageSave ?? "");
          }
          if (state.saveStatus == LoadStatus.success) {
            LoadingDialogTransparent.hide(context);
            _cubit.clearSaveStatus();
            Navigator.pop(context, true);
            DialogHelper.showSnackBar(context,
                message:
                    "Bạn đã hoàn thành đăng ký ngày trực ${state.initData.periodName}");
          }
          if (state.loadDataStatus == LoadStatus.success) {
            dateTimeController.text = state.initData?.startDate ?? "";
          }
        },
        buildWhen: (previous, current) =>
            previous.loadDataStatus != current.loadDataStatus ||
            previous.saveStatus != current.saveStatus ||
                previous.checkVerify != current.checkVerify,
        builder: (context, state) {
          if (widget.workingOnHolidayId != null) {
            dateTimeController.text =
                state.detailWorkingHoliday?.extraDate ?? "";
            addressWorkController.text =
                state.detailWorkingHoliday?.extraAddress ?? "";
            headingWorkController.text =
                state.detailWorkingHoliday?.workItems ?? "";
            detailWorkController.text =
                state.detailWorkingHoliday?.description ?? "";
            valueSelect = state.detailWorkingHoliday?.extraType;
            _cubit.state.copyWith(
                typeRegister: state.detailWorkingHoliday?.registerStatus ?? 0);
          } else {
            if (state.initData != null) {
              endDateTime = DateFormat(AppStrings.TYPE_DD_MM_YYYY)
                  .parse(state.initData?.endDate);
            }
          }
          return Scaffold(
            appBar: AppBar(
              title: Text(
                widget.onlyView
                    ? "Chi tiết đăng ký trực Lễ/tết"
                    : widget.workingOnHolidayId != null
                        ? "Chỉnh sửa đăng ký trực Lễ/Tết"
                        : "Đăng ký trực Lễ/Tết",
                style: TextStyle(color: Colors.black),
              ),
              foregroundColor: Colors.black,
              centerTitle: true,
              backgroundColor: Colors.white,
              elevation: 0,
            ),
            body: state.loadDataStatus == LoadStatus.loading
                ? LoadingIndicator()
                : (widget.workingOnHolidayId != null ||
                        (state.initData != null &&
                            state.initData.registerLock != 1 &&
                            endDateTime.millisecondsSinceEpoch >=
                                DateTime.now().millisecondsSinceEpoch))
                    ? GestureDetector(
                        onTap: () {
                          FocusScope.of(context).requestFocus(new FocusNode());
                        },
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                color: Colors.white,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        state.initData?.periodName ?? "",
                                        style: AppTextStyle
                                            .blackColorS14W600,
                                      ),
                                      SizedBox(
                                        height: 8,
                                      ),
                                      Text(
                                        "Thời gian trực: ${state.initData?.startDate} - ${state.initData?.endDate}",
                                        style: AppTextStyle.blackS12Normal
                                            .copyWith(
                                                color: AppColors
                                                    .textInfoColor),
                                      ),
                                      if (widget.onlyView) ...[
                                        SizedBox(
                                          height: 8,
                                        ),
                                        Text(
                                          "Đăng ký lúc: ${state.initData?.createdAt}",
                                          style: AppTextStyle
                                              .blackS12Normal
                                              .copyWith(
                                                  color: AppColors
                                                      .textInfoColor),
                                        ),
                                      ],
                                      SizedBox(
                                        height: 8,
                                      ),
                                      Divider(
                                        height: 1,
                                        thickness: 0.5,
                                        color: AppColors
                                            .textDateTimeDividerColor,
                                      ),
                                      SizedBox(
                                        height: 16,
                                      ),
                                      if (widget.onlyView) ...[
                                        _itemInfo(
                                            title: "Loại ca trực",
                                            value: ParseEnumExtraTime
                                                        .getType(state
                                                            .detailWorkingHoliday
                                                            ?.extraType)
                                                    .display ??
                                                ""),
                                        SizedBox(
                                          height: 16,
                                        ),
                                        _itemInfo(
                                            title: "Ngày trực",
                                            value: state
                                                    .detailWorkingHoliday
                                                    ?.extraDate ??
                                                ""),
                                        SizedBox(
                                          height: 16,
                                        ),
                                        _itemInfo(
                                            title: "Loại đăng ký",
                                            value: state.detailWorkingHoliday
                                                        ?.registerType ==
                                                    1
                                                ? "Đăng ký trực"
                                                : "Trực bổ sung"),
                                        if (state.detailWorkingHoliday
                                                ?.approveName !=
                                            null) ...[
                                          SizedBox(
                                            height: 16,
                                          ),
                                          Row(
                                            children: [
                                              Expanded(
                                                child: _itemInfo(
                                                    title:
                                                        "Người phê duyệt",
                                                    value: state
                                                        .detailWorkingHoliday
                                                        ?.approveName),
                                              ),
                                              Tooltip(
                                                message:
                                                    "Nhân viên: ${state.detailWorkingHoliday?.approveCode} - ${state.detailWorkingHoliday?.approveName}\nSố điện thoại: ${state.detailWorkingHoliday?.approvePhoneNumber}",
                                                triggerMode:
                                                    TooltipTriggerMode
                                                        .manual,
                                                key: tooltipkey,
                                                preferBelow: false,
                                                child: IconButton(
                                                  padding:
                                                      EdgeInsets.zero,
                                                  icon: const Icon(
                                                    Icons.info,
                                                    color: Colors.blue,
                                                  ),
                                                  color: AppColors
                                                      .primaryColor,
                                                  onPressed: () {
                                                    showAndCloseTooltip();
                                                  },
                                                ),
                                              ),
                                            ],
                                          )
                                        ],
                                        SizedBox(
                                          height: 16,
                                        ),
                                        _itemInfo(
                                            title: "Trạng thái",
                                            value: state
                                                    .detailWorkingHoliday
                                                    ?.getStatusWorkingHoliday() ??
                                                ""),
                                        if (state.detailWorkingHoliday
                                                    ?.registerStatus ==
                                                3 ||
                                            state.detailWorkingHoliday
                                                    ?.registerStatus ==
                                                8) ...[
                                          SizedBox(
                                            height: 16,
                                          ),
                                          _itemInfo(
                                              title: "Lý do từ chối",
                                              value: state
                                                      .detailWorkingHoliday
                                                      ?.rejectReason ??
                                                  ""),
                                        ],
                                        SizedBox(
                                          height: 16,
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ),
                              Container(
                                color: Colors.white,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Thông tin đăng ký trực",
                                        style: AppTextStyle
                                            .blackColorS14W600,
                                      ),
                                      SizedBox(
                                        height: 16,
                                      ),
                                      if ((widget.workingOnHolidayId !=
                                                  null &&
                                              state.detailWorkingHoliday
                                                      ?.registerType !=
                                                  1) ||
                                          state.typeRegister != 1) ...[
                                        Container(
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      8),
                                              border: Border.all(
                                                  color:
                                                      Color(0xFF2D84FF)),
                                              color: Color(0XFFE9F2FF)),
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 12),
                                          child: Text(
                                            AppStrings
                                                .warring_working_holiday,
                                            style: AppTextStyle
                                                .blackS12Normal
                                                .copyWith(
                                                    color: AppColors
                                                        .textInfoColor,
                                                    height: 1.5),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 16,
                                        ),
                                      ],
                                      if (!widget.onlyView) ...[
                                        TextFieldWorkingHoliday(
                                          title: "Loại đăng ký",
                                          onTap: () {},
                                          readOnly: true,
                                          controller: TextEditingController(
                                              text:
                                                  state.typeRegister == 1
                                                      ? "Đăng ký trực"
                                                      : "Trực bổ sung"),
                                          suffixIcon: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsets.only(
                                                        right: 10),
                                                child: SvgPicture.asset(
                                                  AppVectors.ic_dropdown,
                                                  color: AppColors
                                                      .borderColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 16,
                                        ),
                                        TextFieldWorkingHoliday(
                                          title: "Ngày trực",
                                          tapOnly: true,
                                          readOnly: widget.onlyView,
                                          onTap: () {
                                            DatePicker.showDatePicker(
                                              context,
                                              onMonthChangeStartWithFirstDate:
                                                  true,
                                              pickerTheme:
                                                  DateTimePickerTheme(
                                                showTitle: true,
                                                itemTextStyle: AppTextStyle
                                                    .blackTextS16NormalIncome,
                                                title: Expanded(
                                                  child: Container(
                                                    width: MediaQuery.of(
                                                            context)
                                                        .size
                                                        .width,
                                                    // color: Colors.white,
                                                    decoration:
                                                        BoxDecoration(
                                                      color: Colors.white,
                                                      borderRadius:
                                                          BorderRadius
                                                              .only(
                                                        topLeft: Radius
                                                            .circular(20),
                                                        topRight: Radius
                                                            .circular(20),
                                                      ),
                                                    ),
                                                    child: Padding(
                                                      padding: EdgeInsets
                                                          .symmetric(
                                                              vertical:
                                                                  10),
                                                      child: Stack(
                                                        children: [
                                                          Container(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        16),
                                                            child: Align(
                                                              alignment:
                                                                  Alignment
                                                                      .topRight,
                                                              child:
                                                                  GestureDetector(
                                                                child: SvgPicture.asset(
                                                                    AppVectors
                                                                        .icCloseDialog),
                                                                onTap:
                                                                    () {
                                                                  Navigator.of(context)
                                                                      .pop();
                                                                },
                                                              ),
                                                            ),
                                                          ),
                                                          Center(
                                                              child: Text(
                                                                  "Ngày trực",
                                                                  style: AppTextStyle
                                                                      .blackS16Bold)),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              maxDateTime: DateFormat(
                                                      AppStrings
                                                          .TYPE_DD_MM_YYYY)
                                                  .parse(state
                                                      .initData?.endDate),
                                              minDateTime: DateFormat(
                                                      AppStrings
                                                          .TYPE_DD_MM_YYYY)
                                                  .parse(state.initData
                                                      ?.startDate),
                                              initialDateTime: DateFormat(
                                                      AppStrings
                                                          .TYPE_DD_MM_YYYY)
                                                  .parse(state.initData
                                                      ?.startDate),
                                              dateFormat: "dd MMMM yyyy",
                                              locale:
                                                  DateTimePickerLocale.vi,
                                              onChange: (dateTime,
                                                  List<int> index) {
                                                dateTimeController.text =
                                                    DateFormat(AppStrings
                                                            .TYPE_DD_MM_YYYY)
                                                        .format(dateTime);
                                                _cubit.updateVerify(
                                                    isValidate());
                                              },
                                              onConfirm: (dateTime,
                                                  List<int> index) {},
                                            );
                                          },
                                          controller: dateTimeController,
                                          suffixIcon: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsets.only(
                                                        right: 10),
                                                child: SvgPicture.asset(
                                                  AppVectors.icCalendar2,
                                                  color: AppColors
                                                      .borderColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(
                                          height: 16,
                                        ),
                                        SelectFieldWorkingHoliday(
                                          valueSelect: valueSelect,
                                          item: EnumExtraTime.values
                                              .map((item) =>
                                                  DropdownMenuItem<int>(
                                                    value:
                                                        item.keyToServer,
                                                    child: Text(
                                                        item.display),
                                                  ))
                                              .toList(),
                                          onChanged: (value) {
                                            valueSelect = value;
                                            _cubit.updateVerify(
                                                isValidate());
                                          },
                                          enabled: !widget.onlyView,
                                        ),
                                        SizedBox(
                                          height: 16,
                                        ),
                                      ],
                                      Focus(
                                        onFocusChange: (hasFocus) {
                                          if (!hasFocus)
                                            _cubit.updateVerify(
                                                isValidate());
                                        },
                                        child: TextFieldWorkingHoliday(
                                          title: "Địa điểm trực",
                                          onTap: () {},
                                          readOnly: widget.onlyView,
                                          controller:
                                              addressWorkController,
                                        ),
                                      ),
                                      SizedBox(
                                        height: 16,
                                      ),
                                      Focus(
                                        onFocusChange: (hasFocus) {
                                          if (!hasFocus)
                                            _cubit.updateVerify(
                                                isValidate());
                                        },
                                        child: TextFieldWorkingHoliday(
                                          title: "Đầu mục công việc",
                                          readOnly: widget.onlyView,
                                          controller:
                                              headingWorkController,
                                          minLines: 3,
                                          maxLength: 500,
                                        ),
                                      ),
                                      SizedBox(
                                        height: 16,
                                      ),
                                      Focus(
                                        onFocusChange: (hasFocus) {
                                          if (!hasFocus)
                                            _cubit.updateVerify(
                                                isValidate());
                                        },
                                        child: TextFieldWorkingHoliday(
                                          title:
                                              "Mô tả chi tiết công việc",
                                          readOnly: widget.onlyView,
                                          controller:
                                              detailWorkController,
                                          minLines: 3,
                                          maxLength: 4000,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (!widget.onlyView) ...[
                                PrimaryButton(
                                  title: widget.workingOnHolidayId != null
                                      ? "Cập nhật"
                                      : "Đăng ký",
                                  padding: EdgeInsets.all(20),
                                  color: state.checkVerify ?? false
                                      ? AppColors.primaryColor
                                      : AppColors.primaryColor.withOpacity(0.5),
                                  onTap: () async {
                                    FocusScope.of(context).requestFocus(new FocusNode());
                                    if (validateRegister()) {
                                      SharedPreferences sharedPreferences =
                                      await SharedPreferences.getInstance();
                                      String employeeCode = sharedPreferences
                                          .get(AppConstant.KEY_USER_CODE);
                                      _cubit.registerWorkingOnHoliday(
                                        request: WorkingOnHolidayRequest(
                                          extraWorkRegisterId:
                                          widget.workingOnHolidayId,
                                          extraWorkPeriodId:
                                          state.initData?.extraWorkPeriodId,
                                          employeeCode: employeeCode,
                                          extraAddress:
                                          addressWorkController.text,
                                          extraDate: dateTimeController.text,
                                          extraType: valueSelect,
                                          workItems: headingWorkController.text,
                                          description: detailWorkController.text,
                                          registerType: state.typeRegister,
                                        ),
                                      );
                                    }
                                  },
                                ),
                                SizedBox(
                                  height: 16,
                                ),
                              ]
                            ],
                          ),
                        ),
                      )
                    : EmptyListWidget(
                        content:
                            "Đã quá thời gian đăng ký trực, bạn vui lòng thử lại sau",
                        onRefresh: () async {
                          _cubit.loadInitialData();
                        }),
          );
        });
  }

  Widget _itemInfo({String title, String value}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: AppTextStyle.blackS14W400
              .copyWith(color: AppColors.textInfoColor),
        ),
        Text(
          value,
          style: AppTextStyle.blackS14W400,
        ),
      ],
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  bool validateRegister() {
    if (dateTimeController.text.trim().isEmpty) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng chọn ngày trực");
      return false;
    }
    if (valueSelect == null) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng chọn loại ca trực");
      return false;
    }
    if (addressWorkController.text.trim().isEmpty) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập địa điểm trực");
      return false;
    }
    if (headingWorkController.text.trim().isEmpty) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập đầu mục công việc");
      return false;
    }
    if (detailWorkController.text.trim().isEmpty) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập mô tả chi tiết công việc");
      return false;
    }
    return true;
  }

  bool isValidate() {
    return dateTimeController.text.trim().isNotEmpty &&
        valueSelect != null &&
        addressWorkController.text.trim().isNotEmpty &&
        headingWorkController.text.trim().isNotEmpty &&
        detailWorkController.text.trim().isNotEmpty;
  }
}
