part of 'register_working_on_holiday_cubit.dart';

class RegisterWorkingOnHolidayState extends Equatable {
  final LoadStatus loadDataStatus;
  final LoadStatus saveStatus;
  final CurrentPeriodModel initData;
  final WorkingOnHolidayDto detailWorkingHoliday;
  final int typeRegister;
  final String messageSave;
  final bool checkVerify;

  const RegisterWorkingOnHolidayState({
    this.loadDataStatus = LoadStatus.initial,
    this.saveStatus = LoadStatus.initial,
    this.initData,
    this.typeRegister,
    this.messageSave,
    this.detailWorkingHoliday,
    this.checkVerify,
  });

  @override
  List<Object> get props => [
        loadDataStatus,
        saveStatus,
        initData,
        typeRegister,
        messageSave,
        detailWorkingHoliday,
        checkVerify,
      ];

  RegisterWorkingOnHolidayState copyWith({
    LoadStatus loadDataStatus,
    LoadStatus saveStatus,
    CurrentPeriodModel initData,
    int typeRegister,
    String messageSave,
    WorkingOnHolidayDto detailWorkingHoliday,
    bool checkVerify,
  }) {
    return RegisterWorkingOnHolidayState(
        loadDataStatus: loadDataStatus ?? this.loadDataStatus,
        saveStatus: saveStatus ?? this.saveStatus,
        initData: initData ?? this.initData,
        typeRegister: typeRegister ?? this.typeRegister,
        messageSave: messageSave ?? this.messageSave,
        checkVerify: checkVerify ?? this.checkVerify,
        detailWorkingHoliday:
            detailWorkingHoliday ?? this.detailWorkingHoliday);
  }
}
