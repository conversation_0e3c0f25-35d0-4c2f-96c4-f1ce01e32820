part of 'detail_result_working_on_holiday_cubit.dart';

class DetailResultWorkingOnHolidayState extends Equatable {
  final LoadStatus loadDataStatus;
  final LoadStatus saveStatus;
  final CurrentPeriodModel initData;
  final WorkingOnHolidayDto detailWorkingHoliday;
  final int typeRegister;
  final String messageSave;

  const DetailResultWorkingOnHolidayState({
    this.loadDataStatus = LoadStatus.initial,
    this.saveStatus = LoadStatus.initial,
    this.initData,
    this.typeRegister,
    this.messageSave,
    this.detailWorkingHoliday,
  });

  @override
  List<Object> get props => [
    loadDataStatus,
    saveStatus,
    initData,
    typeRegister,
    messageSave,
    detailWorkingHoliday,
      ];

  DetailResultWorkingOnHolidayState copyWith({
    LoadStatus loadDataStatus,
    LoadStatus saveStatus,
    CurrentPeriodModel initData,
    int typeRegister,
    String messageSave,
    WorkingOnHolidayDto detailWorkingHoliday,
  }) {
    return DetailResultWorkingOnHolidayState(
        loadDataStatus: loadDataStatus ?? this.loadDataStatus,
        saveStatus: saveStatus ?? this.saveStatus,
        initData: initData ?? this.initData,
        typeRegister: typeRegister ?? this.typeRegister,
        messageSave: messageSave ?? this.messageSave,
        detailWorkingHoliday:
        detailWorkingHoliday ?? this.detailWorkingHoliday,
    );
  }
}
