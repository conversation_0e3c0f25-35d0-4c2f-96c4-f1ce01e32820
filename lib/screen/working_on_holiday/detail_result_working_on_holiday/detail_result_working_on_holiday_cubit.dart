import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/working_on_holiday/current_period_response_model.dart';
import 'package:hstd/models/working_on_holiday/working_holiday_request.dart';
import 'package:hstd/models/working_on_holiday/working_on_holiday_dto.dart';
import 'package:hstd/repositories/working_on_holiday_service.dart';

part 'detail_result_working_on_holiday_state.dart';

class DetailResultWorkingOnHolidayCubit extends Cubit<DetailResultWorkingOnHolidayState> {

  DetailResultWorkingOnHolidayCubit() : super(const DetailResultWorkingOnHolidayState());
  WorkingOnHolidayService workingOnHolidayService =
  WorkingOnHolidayServiceImp();

  Future<void> loadInitialData({int workingOnHolidayId}) async {
    await getWorkById(workingOnHolidayId: workingOnHolidayId);
  }

  Future<void> getWorkById({int workingOnHolidayId}) async {
    try {
      emit(state.copyWith(loadDataStatus: LoadStatus.loading));
      //Todo: add API calls
      var result = await workingOnHolidayService.getResultWorkById(id: workingOnHolidayId);
      if (result != null) {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.success,
          detailWorkingHoliday: result,
        ));
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
        ));
      }
    } catch (e, s) {
      //Todo: should print exception here
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }

  Future<void> updateWorkingOnHoliday({WorkingOnHolidayRequest request,}) async {
    try {
      emit(state.copyWith(
        saveStatus: LoadStatus.loading,
      ));
      //Todo: add API calls
      var result = await workingOnHolidayService.updateWorkingOnHoliday(request: request);
      if (result != null && result.status == 1) {
        emit(state.copyWith(
          saveStatus: LoadStatus.success,
          messageSave: result.message,
        ));
      } else{
        emit(state.copyWith(
          saveStatus: LoadStatus.failure,
          messageSave: result.message,
        ));
      }
    } catch (e, s) {
      print(e);
      emit(state.copyWith(
        saveStatus: LoadStatus.failure,
        messageSave: e.toString(),
      ));
    }
  }

  void clearSaveStatus(){
    emit(
      state.copyWith(
        saveStatus: LoadStatus.initial,
      ),
    );
  }
}
