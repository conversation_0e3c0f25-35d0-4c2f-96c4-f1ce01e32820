import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/working_on_holiday/working_holiday_request.dart';
import 'package:hstd/screen/working_on_holiday/widget/text_field_working_holiday.dart';
import 'package:hstd/widget/dropdown/dialog_helper.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'detail_result_working_on_holiday_cubit.dart';

class DetailResultWorkingOnHolidayPage extends StatelessWidget {
  const DetailResultWorkingOnHolidayPage(
      {Key key, this.workingOnHolidayId, this.onlyView = false})
      : super(key: key);
  final int workingOnHolidayId;
  final bool onlyView;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return DetailResultWorkingOnHolidayCubit();
      },
      child: DetailResultWorkingOnHolidayChildPage(
        workingOnHolidayId: workingOnHolidayId,
        onlyView: onlyView,
      ),
    );
  }
}

class DetailResultWorkingOnHolidayChildPage extends StatefulWidget {
  const DetailResultWorkingOnHolidayChildPage(
      {Key key, this.workingOnHolidayId, this.onlyView})
      : super(key: key);
  final int workingOnHolidayId;
  final bool onlyView;

  @override
  State<DetailResultWorkingOnHolidayChildPage> createState() =>
      _DetailResultWorkingOnHolidayChildPageState();
}

class _DetailResultWorkingOnHolidayChildPageState
    extends State<DetailResultWorkingOnHolidayChildPage> {
  DetailResultWorkingOnHolidayCubit _cubit;
  TextEditingController addressWorkController = TextEditingController();
  TextEditingController headingWorkController = TextEditingController();
  TextEditingController detailWorkController = TextEditingController();
  final tooltipkey = GlobalKey<State<Tooltip>>();

  Future showAndCloseTooltip() async {
    await Future.delayed(const Duration(milliseconds: 10));
    // tooltipkey.currentState.ensureTooltipVisible();
    final dynamic tooltip = tooltipkey.currentState;
    tooltip?.ensureTooltipVisible();
    await Future.delayed(const Duration(seconds: 4));
    // tooltipkey.currentState.deactivate();
    tooltip?.deactivate();
  }
  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData(workingOnHolidayId: widget.workingOnHolidayId);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<DetailResultWorkingOnHolidayCubit,
            DetailResultWorkingOnHolidayState>(
        listenWhen: (previous, current) =>
            previous.saveStatus != current.saveStatus ||
            previous.loadDataStatus != current.loadDataStatus,
        listener: (context, state) {
          if (state.saveStatus == LoadStatus.loading) {
            LoadingDialogTransparent.show(context);
          }
          if (state.saveStatus == LoadStatus.failure) {
            LoadingDialogTransparent.hide(context);
            _cubit.clearSaveStatus();
            DialogHelper.showSnackBar(context, message: state.messageSave);
          }
          if (state.saveStatus == LoadStatus.success) {
            LoadingDialogTransparent.hide(context);
            _cubit.clearSaveStatus();
            Navigator.pop(context, true);
            DialogHelper.showSnackBar(context, message: state.messageSave);
          }
        },
        buildWhen: (previous, current) =>
            previous.loadDataStatus != current.loadDataStatus,
        builder: (context, state) {
          if (widget.workingOnHolidayId != null) {
            addressWorkController.text =
                state.detailWorkingHoliday?.location ?? "";
            headingWorkController.text =
                state.detailWorkingHoliday?.workItems ?? "";
            detailWorkController.text =
                state.detailWorkingHoliday?.contentWork ?? "";
          }
          return Scaffold(
            appBar: AppBar(
              title: Text(
                widget.onlyView
                    ? "Chi tiết kết quả trực Lễ/tết"
                    : "Kết quả công việc trực Lễ/Tết",
                style: TextStyle(color: Colors.black),
              ),
              foregroundColor: Colors.black,
              centerTitle: true,
              backgroundColor: Colors.white,
              elevation: 0,
            ),
            body: GestureDetector(
              onTap: () {
                FocusScope.of(context).requestFocus(new FocusNode());
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            color: Colors.white,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    state.detailWorkingHoliday?.periodName ??
                                        "",
                                    style: AppTextStyle.blackColorS14W600,
                                  ),
                                  SizedBox(
                                    height: 8,
                                  ),
                                  Text(
                                    "Thời gian trực: ${state.detailWorkingHoliday?.extraWorkDay}",
                                    style: AppTextStyle.blackS12Normal.copyWith(
                                        color: AppColors.textInfoColor),
                                  ),
                                  SizedBox(
                                    height: 8,
                                  ),
                                  Divider(
                                    height: 1,
                                    thickness: 0.5,
                                    color: AppColors.textDateTimeDividerColor,
                                  ),
                                  SizedBox(
                                    height: 16,
                                  ),
                                  _itemInfo(
                                      title: "Loại ca trực",
                                      value: state.detailWorkingHoliday
                                              ?.timeTypeApp ??
                                          ""),
                                  SizedBox(
                                    height: 16,
                                  ),
                                  _itemInfo(
                                      title: "Ngày trực",
                                      value: state.detailWorkingHoliday
                                              ?.extraWorkDay ??
                                          ""),
                                  SizedBox(
                                    height: 16,
                                  ),
                                  _itemInfo(
                                      title: "Loại đăng ký",
                                      value: state.detailWorkingHoliday
                                                  ?.registerType ==
                                              1
                                          ? "Đăng ký trực"
                                          : "Trực bổ sung"),
                                  if (state.detailWorkingHoliday?.approveName !=
                                      null) ...[
                                    SizedBox(
                                      height: 16,
                                    ),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: _itemInfo(
                                              title: "Người phê duyệt",
                                              value: state.detailWorkingHoliday
                                                  ?.approveName),
                                        ),
                                        Tooltip(
                                          message: "Nhân viên: ${state.detailWorkingHoliday?.approveCode} - ${state.detailWorkingHoliday?.approveName}\nSố điện thoại: ${state.detailWorkingHoliday?.approvePhoneNumber}",
                                          triggerMode: TooltipTriggerMode.manual,
                                          key: tooltipkey,
                                          preferBelow: false,
                                          child: IconButton(
                                            padding: EdgeInsets.zero,
                                            icon: const Icon(
                                              Icons.info,
                                              color: Colors.blue,
                                            ),
                                            color: AppColors.primaryColor,
                                            onPressed: () {
                                              showAndCloseTooltip();
                                            },
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                  SizedBox(
                                    height: 16,
                                  ),
                                  _itemInfo(
                                      title: "Trạng thái",
                                      value: state.detailWorkingHoliday
                                              ?.getStatusResultWorkingHoliday() ??
                                          ""),
                                  if (state.detailWorkingHoliday?.status == 3 ||
                                      state.detailWorkingHoliday?.status ==
                                          8) ...[
                                    SizedBox(
                                      height: 16,
                                    ),
                                    _itemInfo(
                                        title: "Lý do từ chối",
                                        value: state.detailWorkingHoliday
                                                ?.rejectReason ??
                                            ""),
                                  ],
                                  SizedBox(
                                    height: 16,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            color: Colors.white,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.onlyView
                                        ? "Thông tin đăng ký trực"
                                        : "Công việc thực tế",
                                    style: AppTextStyle.blackColorS14W600,
                                  ),
                                  if (state
                                          .detailWorkingHoliday?.registerType !=
                                      1) ...[
                                    SizedBox(
                                      height: 16,
                                    ),
                                    Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: Color(0xFF2D84FF)),
                                        color: Color(0XFFE9F2FF)
                                      ),
                                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                                      child: Text(
                                        AppStrings.warring_working_holiday,
                                        style: AppTextStyle.blackS12Normal
                                            .copyWith(
                                                color: AppColors.textInfoColor,
                                                height: 1.5),
                                      ),
                                    ),
                                  ],
                                  SizedBox(
                                    height: 16,
                                  ),
                                  TextFieldWorkingHoliday(
                                    title: "Địa điểm trực",
                                    readOnly: widget.onlyView,
                                    onTap: () {},
                                    controller: addressWorkController,
                                  ),
                                  SizedBox(
                                    height: 16,
                                  ),
                                  TextFieldWorkingHoliday(
                                    title: "Đầu mục công việc",
                                    readOnly: widget.onlyView,
                                    controller: headingWorkController,
                                    minLines: 3,
                                    maxLength: 500,
                                  ),
                                  SizedBox(
                                    height: 16,
                                  ),
                                  TextFieldWorkingHoliday(
                                    title: "Mô tả chi tiết công việc",
                                    readOnly: widget.onlyView,
                                    controller: detailWorkController,
                                    minLines: 3,
                                    maxLength: 4000,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (!widget.onlyView)...[
                    PrimaryButton(
                      title: "Cập nhật",
                      padding: EdgeInsets.all(20),
                      onTap: () async {
                        if (validateRegister()) {
                          SharedPreferences sharedPreferences =
                          await SharedPreferences.getInstance();
                          String employeeCode =
                          sharedPreferences.get(AppConstant.KEY_USER_CODE);
                          _cubit.updateWorkingOnHoliday(
                            request: WorkingOnHolidayRequest(
                              extraWorkId: widget.workingOnHolidayId,
                              location: addressWorkController.text,
                              workItems: headingWorkController.text,
                              contentWork: detailWorkController.text,
                            ),
                          );
                        }
                      },
                    ),
                    SizedBox(
                      height: 16,
                    ),
                  ]
                ],
              ),
            ),
          );
        });
  }

  Widget _itemInfo({String title, String value}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: AppTextStyle.blackS14W400
              .copyWith(color: AppColors.textInfoColor),
        ),
        Text(
          value,
          style: AppTextStyle.blackS14W400,
        ),
      ],
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  bool validateRegister() {
    if (addressWorkController.text.trim().isEmpty) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập địa điểm trực");
      return false;
    }
    if (headingWorkController.text.trim().isEmpty) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập đầu mục công việc");
      return false;
    }
    if (detailWorkController.text.trim().isEmpty) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập mô tả chi tiết công việc");
      return false;
    }
    return true;
  }
}
