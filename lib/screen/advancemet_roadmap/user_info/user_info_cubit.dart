import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/advancement_roadmap/user_info_dto.dart';
import 'package:hstd/repositories/advancement_roadmap_service.dart';

part 'user_info_state.dart';

class UserInfoCubit extends Cubit<UserInfoState> {
  AdvancementRoadmapService _service = AdvancementRoadmapServiceImp();

  UserInfoCubit() : super(const UserInfoState());

  Future<void> loadInitialData() async {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));

    try {
      final result =
          await _service.getUserInfo();

      if (result != null) {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.success,
          data: result,
        ));
      } else {
        emit(state.copyWith(loadDataStatus: LoadStatus.failure));
      }
    } catch (e) {
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }
}
