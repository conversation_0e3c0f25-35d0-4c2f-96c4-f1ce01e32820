import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/utils/utils.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_indicator.dart';

import 'user_info_cubit.dart';

class UserInfoArguments {
  UserInfoArguments();
}

class UserInfoPage extends StatelessWidget {
  final UserInfoArguments arguments;

  const UserInfoPage({
    Key key,
    this.arguments,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return UserInfoCubit();
      },
      child: const UserInfoChildPage(),
    );
  }
}

class UserInfoChildPage extends StatefulWidget {
  const UserInfoChildPage({Key key}) : super(key: key);

  @override
  State<UserInfoChildPage> createState() => _UserInfoChildPageState();
}

class _UserInfoChildPageState extends State<UserInfoChildPage> {
  UserInfoCubit _cubit;

  TextEditingController _describeController = TextEditingController();
  TextEditingController _certificatesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppStrings.user_infomation,
          style: TextStyle(
              color: Colors.black
          ),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: BlocBuilder<UserInfoCubit, UserInfoState>(
          buildWhen: (previous, current) =>
              previous.loadDataStatus != current.loadDataStatus,
          builder: (context, state) {
            if (state.loadDataStatus == LoadStatus.loading) {
              return LoadingIndicator();
            } else if (state.loadDataStatus == LoadStatus.failure) {
              return Column(
                children: [
                  Container(
                    height: 1,
                    color: AppColors.greyRoadmap,
                  ),
                  Expanded(
                    child: EmptyListWidget(
                      onRefresh: () async {
                        _cubit.loadInitialData();
                      },
                    ),
                  ),
                ],
              );
            } else {
              _describeController.text=state.data?.note??'';
              _certificatesController.text= Utils.convertListStringToString(state.data.certificates) ??'';
              return Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          height: 1,
                          color: AppColors.greyRoadmap,
                        ),
                        Padding(
                          padding: EdgeInsets.fromLTRB(16,16,16,12),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Padding(
                                  padding: EdgeInsets.only(right: 12.0),
                                  child: Icon(
                                    Icons.account_circle,
                                    color: Color(0xFFB5B4B4),
                                    size: 22.0,
                                  )),
                              Flexible(
                                child: Text(
                                  '${state.data.employeeCode} - ${state.data.employeeName}',
                                  style: AppTextStyle.blackS14W600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          height: 1,
                          color: AppColors.greyRoadmap,
                        ),
                        SizedBox(height: 6,),
                        ItemListView(
                          title: AppStrings.position,
                          text: state.data.positionName,
                        ),
                        ItemListView(
                          title: AppStrings.unit,
                          text: state.data.unitName,
                        ),
                        ItemListView(
                          title: AppStrings.department,
                          text: state.data.departmentName,
                        ),
                        ItemListView(
                          title: AppStrings.state_join_group,
                          text: state.data.joinCompanyDate,
                        ),
                        ItemListView(
                          title: AppStrings.level,
                          text: state.data.degreeName,
                        ),
                        ItemListView(
                          title: AppStrings.specialized,
                          text: state.data.educationSubjectName,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 16, top: 8),
                          child: Text(AppStrings.certificate,
                              style:
                              AppTextStyle.greyTitleS14NormalRoadmap),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16,vertical:0),
                          child: TextFormField(
                            controller: _certificatesController,
                            style: AppTextStyle.blackTextS14NormalRoadmap,
                            enabled: false,
                            maxLines: null,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                            ),
                            keyboardType: TextInputType.multiline,

                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 16, top: 8),
                          child: Text(AppStrings.job_description,
                              style:
                              AppTextStyle.greyTitleS14NormalRoadmap),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16,vertical:0),
                          child: TextFormField(
                            controller: _describeController,
                            style: AppTextStyle.blackTextS14NormalRoadmap,
                            enabled: false,
                            maxLines: null,
                            keyboardType: TextInputType.multiline,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                        Container(
                          height: 1,
                          color: AppColors.greyRoadmap,
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: state.loadDataStatus == LoadStatus.loading,
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.primaryColor,
                            ),
                          ),
                          SizedBox(
                            height: 50,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }
}
