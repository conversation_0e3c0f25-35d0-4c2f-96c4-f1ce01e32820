import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/screen/advancemet_roadmap/process_career_rank/process_career_rank_page.dart';
import 'package:hstd/screen/advancemet_roadmap/register_roadmap/register_roadmap_page.dart';
import 'package:hstd/screen/advancemet_roadmap/roadmap_standards/roadmap_standards_page.dart';
import 'package:hstd/screen/advancemet_roadmap/user_info/user_info_page.dart';

import 'register_history/register_history_page.dart';

class AdvancementRoadmapScreen extends StatelessWidget {
  const AdvancementRoadmapScreen({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgAdRoadmap,
      appBar: AppBar(
        title: Text(
            AppStrings.advancement_roadmap,
          style: TextStyle(
            color: Colors.black,
          ),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 1,
            color: AppColors.greyRoadmap,
          ),
          Padding(
            padding: const EdgeInsets.only(top: 20, left: 16, right: 16, bottom: 10),
            child: Text(
              AppStrings.general_information,
              style: AppTextStyle.blackS14W600,
            ),
          ),
          menuItem(
            title: AppStrings.user_infomation,
            icon: AppVectors.ic_user_info,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => UserInfoPage(),
                ),
              );
            },
          ),
          SizedBox(height: 1,),
          menuItem(
            title: AppStrings.roadmap_standards,
            icon: AppVectors.ic_roadmap,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => RoadmapStandardsPage(),
                ),
              );
            },
          ),
          Padding(
            padding: const EdgeInsets.only(top: 16, left: 16, right: 16, bottom: 10),
            child: Text(
              AppStrings.advancement_roadmap,
              style: AppTextStyle.blackS14W600,
            ),
          ),
          menuItem(
            title: AppStrings.register,
            icon: AppVectors.ic_register_roadmap,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => RegisterRoadmapPage(
                    arguments: RegisterRoadmapArguments(),
                  ),
                ),
              );
            },
          ),
          SizedBox(height: 1,),
          menuItem(
            title: AppStrings.register_history,
            icon: AppVectors.ic_register_history,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => RegisterHistoryPage(),
                ),
              );
            },
          ),
          SizedBox(height: 1,),
          menuItem(
            title: AppStrings.top_10_vcc,
            icon: AppVectors.ic_ranking,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ProcessCareerRankPage(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget menuItem({String title, String icon, Function onTap}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      color: Colors.white,
      child: InkWell(
        onTap: onTap,
        child: Row(
          children: [
            SvgPicture.asset(icon),
            SizedBox(width: 12),
            Expanded(
              child: Text(title),
            ),
            Icon(
              Icons.chevron_right,
              color: Color(0xFFB5B4B4),
            ),
            SizedBox(width: 5),
          ],
        ),
      ),
    );
  }

}
