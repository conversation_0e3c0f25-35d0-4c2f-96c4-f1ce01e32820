import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:hstd/widget/sliver_grid_delegate.dart';

class ItemListView extends StatelessWidget {
  final String text;
  final String title;
  final TextStyle style;

  const ItemListView({@required this.title, @required this.text,
    this.style, Key key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            flex: 4,
            child: Padding(
              padding: const EdgeInsets.only(right: 12),
              child: Text(title,
                  style: style != null ? style :
                  AppTextStyle.greyTitleS14NormalRoadmap),
            ),
          ),
          Flexible(
            flex: 6,
            child: Text(
                text!= null ? text : '',
                style: style != null ? style :
                AppTextStyle.blackTextS14NormalRoadmap,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}

class ItemCustomListView extends StatelessWidget {
  final String text;
  final String title;
  final TextStyle style;

  const ItemCustomListView({@required this.title, @required this.text,
    this.style, Key key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            flex: 4,
            child: Padding(
              padding: const EdgeInsets.only(right: 12),
              child: Text(title,
                  style: style != null ? style :
                  AppTextStyle.outerSpaceS14Normal),
            ),
          ),
          Flexible(
            flex: 6,
            child: Text(
              text!= null ? text : '',
              style: style != null ? style :
              AppTextStyle.blackS14Normal,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}

class ItemPosition extends StatelessWidget {
  final String text;
  final bool selected;
  final double height;
  final VoidCallback onTap;
  final VoidCallback onViewDetail;
  final bool isLast;
  final bool hasIcon;

  const ItemPosition({@required this.text,
    this.selected = false,
    this.height = 10,
    this.onTap,
    this.onViewDetail,
    this.isLast,
    this.hasIcon = false,
    Key key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(
          left: 10,
          right: 10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 10, bottom: 5),
              child: InkWell(
                onTap: () {
                  onTap();
                },
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 9),
                      child: Icon(
                        Icons.circle,
                        size: 8,
                        color: selected
                            ? AppColors.primaryColor
                            : AppColors.blackColor,
                      ),
                    ),
                    SizedBox(
                      width: 16,
                    ),
                    Expanded(
                      child: Text(
                        text,
                        style: selected
                            ? AppTextStyle.primaryColorS16W500
                            : AppTextStyle.blackS16W500,
                      ),
                    )
                  ],
                ),
              ),
            ),
            Row(
              children: [
                Container(
                  height: height + 10,
                  width: 36,
                  child: isLast
                      ? SizedBox()
                      : Stack(
                    children: [
                      Positioned(
                        left: 13.6,
                        child: Container(
                          height: height,
                          width: 1.3,
                          color: selected
                              ? AppColors.primaryColor
                              : AppColors.greyRoadmap,
                        ),
                      ),
                      Positioned(
                        left: 5,
                        bottom: 0,
                        child: Icon(
                          Icons.arrow_downward_sharp,
                          size: 18.2,
                          color: selected
                              ? AppColors.primaryColor
                              : AppColors.greyRoadmap,
                        ),
                      ),
                    ],
                  ),
                ),
                if (selected) ...[
                  InkWell(
                    onTap: () {
                      onViewDetail();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(
                              width: 1, color: AppColors.primaryColor)),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 7),
                        child: Row(
                          children: [
                            if (hasIcon)...[
                              SvgPicture.asset(
                                AppVectors.ic_setting,
                              ),
                              SizedBox(width: 6,),
                            ],
                            Text(
                              'Chi tiết tiêu chuẩn',
                              style: AppTextStyle.primaryColorS13,
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                ]
              ],
            ),
          ],
        ));
  }
}

class ItemPositionHasIcon extends StatelessWidget {
  final String text;
  final bool selected;
  final double height;
  final VoidCallback onTap;
  final VoidCallback onViewDetail;
  final bool isLast;

  const ItemPositionHasIcon({@required this.text,
    this.selected = false,
    this.height = 10,
    this.onTap,
    this.onViewDetail,
    this.isLast,
    Key key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(
          left: 10,
          right: 10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 10, bottom: 5),
              child: InkWell(
                onTap: () {
                  onTap();
                },
                child: Row(
                  children: [
                    Icon(
                      Icons.circle,
                      size: 8,
                      color: selected
                          ? AppColors.primaryColor
                          : AppColors.blackColor,
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Text(
                        text,
                        style: selected
                            ? AppTextStyle.primaryColorS14W600
                            : AppTextStyle.blackS14W600,
                      ),
                    )
                  ],
                ),
              ),
            ),
            Row(
              children: [
                Container(
                  height: height + 10,
                  width: 30,
                  child: isLast
                      ? SizedBox()
                      : Stack(
                    children: [
                      Positioned(
                        left: 13.5,
                        child: Container(
                          height: height,
                          width: 0.9,
                          color: selected
                              ? AppColors.primaryColor
                              : AppColors.greyRoadmap,
                        ),
                      ),
                      Positioned(
                        left: 5,
                        bottom: 0,
                        child: Icon(
                          Icons.arrow_downward_sharp,
                          size: 18.2,
                          color: selected
                              ? AppColors.primaryColor
                              : AppColors.greyRoadmap,
                        ),
                      ),
                    ],
                  ),
                ),
                if (selected) ...[
                  InkWell(
                    onTap: () {
                      onViewDetail();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(
                              width: 1, color: AppColors.primaryColor)),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 5),
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              AppVectors.ic_setting,
                            ),
                            SizedBox(width: 6,),
                            Text(
                              'Chi tiết tiêu chuẩn',
                              style: AppTextStyle.primaryColorS13,
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                ]
              ],
            ),
          ],
        ));
  }
}

class ItemUnit extends StatelessWidget {
  final String unitName;
  final int unitBlock;

  const ItemUnit({
    @required this.unitName,
    @required this.unitBlock,
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(
          top: 15,
          left: 10,
          right: 10,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 5, bottom: 5),
                  child: Row(
                    children: [
                      Icon(
                        Icons.circle,
                        size: 8,
                        color: AppColors.blackColor,
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Container(
                        width: MediaQuery
                            .of(context)
                            .size
                            .width * 0.85,
                        child: Text(
                          unitName,
                          style: AppTextStyle.blackS14W600,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 5, bottom: 5),
                  child: Container(
                    height: 45,
                    child: Row(
                      children: [
                        Icon(
                          Icons.circle_outlined,
                          size: 8,
                          color: AppColors.blackColor,
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Container(
                          child: Text(
                            unitBlock == 3
                                ? AppStrings.select_title
                                : AppStrings.select_department,
                            style: AppTextStyle.primaryS14W500,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(left: 8.5, top: 9),
              child: Container(
                height: 32,
                width: 0.9,
                color: AppColors.blackColor,
              ),
            ),
          ],
        ));
  }
}

class ItemDepartment extends StatelessWidget {
  final String unitName;
  final String department;

  const ItemDepartment(
      {@required this.unitName, @required this.department, Key key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(
          top: 15,
          left: 10,
          right: 10,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 5, bottom: 5),
                  child: Row(
                    children: [
                      Icon(
                        Icons.circle,
                        size: 8,
                        color: AppColors.blackColor,
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Container(
                        width: MediaQuery
                            .of(context)
                            .size
                            .width * 0.85,
                        child: Text(
                          unitName,
                          style: AppTextStyle.blackS14W600,
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 5),
                  child: Container(
                    height: 40,
                    child: Row(
                      children: [
                        Icon(
                          Icons.circle,
                          size: 8,
                          color: AppColors.blackColor,
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Container(
                          width: MediaQuery
                              .of(context)
                              .size
                              .width * 0.85,
                          child: Text(
                            department,
                            style: AppTextStyle.blackS14W600,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 5, bottom: 5),
                  child: Container(
                    height: 30,
                    child: Row(
                      children: [
                        Icon(
                          Icons.circle_outlined,
                          size: 8,
                          color: AppColors.blackColor,
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Container(
                          child: Text(
                            AppStrings.select_title,
                            style: AppTextStyle.primaryS14W500,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(left: 8.5, top: 9),
              child: Container(
                height: Platform.isIOS ? 65.5 : 64,
                width: 0.9,
                color: AppColors.blackColor,
              ),
            ),
          ],
        ));
  }
}

class DialogNotify extends StatelessWidget {
  const DialogNotify({@required this.content, this.showIcon = false, Key key})
      : super(key: key);

  final String content;
  final bool showIcon;

  @override
  Widget build(BuildContext context) {
    var _timer = Timer(Duration(milliseconds: 1250), () {
      Navigator.of(context).pop();
    });

    return Dialog(
      backgroundColor: AppColors.blackColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Visibility(
              visible: showIcon,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: SvgPicture.asset(
                  AppVectors.ic_success,
                ),
              ),
            ),
            Text(
              content,
              style: AppTextStyle.whiteS14W500,
              textAlign: TextAlign.center,
            )
          ],
        ),
      ),
    );
  }
}

class ShowDialogNotifySuccess extends StatelessWidget {
  const ShowDialogNotifySuccess({
    @required this.title,
    @required this.onConfirm,
    @required this.firstContent,
    @required this.roadmap,
    @required this.lastContent,
    Key key,
  }) : super(key: key);

  final String title;
  final String firstContent;
  final String roadmap;
  final String lastContent;
  final VoidCallback onConfirm;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.all(15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 10),
              Text(title, style: AppTextStyle.blackS18Bold),
              SizedBox(height: 20),
              RichText(
                text: TextSpan(
                  style: TextStyle(color: Colors.black),
                  children: [
                    TextSpan(
                      text: firstContent,
                    ),
                    TextSpan(
                      text: roadmap,
                      style: TextStyle(color: AppColors.primaryColor),
                    ),
                    TextSpan(
                      text: lastContent,
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 20),
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceAround,
              //   children: [
              //     PrimaryButton(
              //       borderRadius: BorderRadius.circular(24),
              //       title: AppStrings.cancel,
              //       titleStyle: AppTextStyle.primaryColorS15W500,
              //       height: 44,
              //       padding: EdgeInsets.symmetric(
              //         vertical: 16,
              //         horizontal: 10,
              //       ),
              //       color: Colors.white,
              //       borderColor: AppColors.primaryColor,
              //       onTap: () {
              //         Navigator.of(context).pop();
              //       },
              //     ),
              //     PrimaryButton(
              //       borderRadius: BorderRadius.circular(24),
              //       // title: AppStrings.confirm,
              //       title: 'Xác nhận',
              //       titleStyle: AppTextStyle.whiteS16W600,
              //       height: 44,
              //       padding: EdgeInsets.symmetric(
              //         vertical: 16,
              //         horizontal: 10,
              //       ),
              //       onTap: () async {
              //         onConfirm();
              //       },
              //     ),
              //   ],
              // ),
              GridView(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                gridDelegate:
                SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight(
                  crossAxisCount: 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 0,
                  height: 80,
                ),
                children: [
                  PrimaryButton(
                    borderRadius: BorderRadius.circular(24),
                    title: AppStrings.cancel,
                    titleStyle: AppTextStyle.primaryColorS15W500,
                    height: 48,
                    padding: EdgeInsets.symmetric(
                      vertical: 16,
                    ),
                    color: Colors.white,
                    borderColor: AppColors.primaryColor,
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                  ),
                  PrimaryButton(
                    borderRadius: BorderRadius.circular(24),
                    title: AppStrings.confirm,
                    // title: 'Xác nhận từ chối',
                    titleStyle: AppTextStyle.whiteS16W600,
                    height: 48,
                    padding: EdgeInsets.symmetric(
                      vertical: 16,
                    ),
                    onTap: () async {
                      onConfirm();
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ItemHistory extends StatelessWidget {
  final String text;
  final bool selected;
  final double height;
  final VoidCallback onTap;
  final VoidCallback onViewDetail;
  final bool isLast;
  final int status;
  final String date;

  const ItemHistory({@required this.text,
    this.selected = false,
    this.height = 10,
    this.onTap,
    this.onViewDetail,
    this.isLast,
    this.status,
    this.date,
    Key key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(
          left: 10,
          right: 10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 10, bottom: 5),
              child: InkWell(
                onTap: () {
                  onTap();
                },
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 80,
                      child: Text(
                        date,
                        style: selected
                            ? AppTextStyle.primaryS14Normal
                            : AppTextStyle.blackS14Normal,
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Icon(
                        Icons.circle,
                        size: 8,
                        color: selected
                            ? AppColors.primaryColor
                            : AppColors.blackColor,
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Text(
                        text,
                        style: selected
                            ? AppTextStyle.primaryColorS14W600
                            : AppTextStyle.blackS14W600,
                      ),
                    )
                  ],
                ),
              ),
            ),
            Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 90),
                  child: Container(
                    height: height + 10,
                    width: 30,
                    child: isLast
                        ? SizedBox()
                        : Stack(
                      children: [
                        Positioned(
                          left: 13.5,
                          child: Container(
                            height: height,
                            width: 0.9,
                            color: AppColors.greyRoadmap,
                          ),
                        ),
                        Positioned(
                          left: 5,
                          bottom: 0,
                          child: Icon(
                            Icons.arrow_downward_sharp,
                            size: 18.2,
                            color: AppColors.greyRoadmap,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                if (selected) ...[
                  InkWell(
                    onTap: () {
                      onViewDetail();
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          status == 1
                              ? AppStrings.registered
                              : AppStrings.destroyed,
                          style: AppTextStyle.greyTitleS14NormalRoadmap,
                        ),
                        SizedBox(height: 20),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              border: Border.all(
                                  width: 1, color: AppColors.primaryColor)),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 5),
                            child: Row(
                              children: [
                                Text(
                                  AppStrings.salary_user_manual_see_detail,
                                  style: AppTextStyle.primaryColorS13,
                                ),
                                SizedBox(
                                  width: 5,
                                ),
                                SvgPicture.asset(
                                  AppVectors.ic_detail,
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                      ],
                    ),
                  )
                ],
                if (!selected)...[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        status == 1
                            ? AppStrings.registered
                            : AppStrings.destroyed,
                        style: AppTextStyle.greyTitleS14NormalRoadmap,
                      ),
                      SizedBox(height: 10),
                    ],
                  )
                ]
              ],
            ),
          ],
        ));
  }
}


class TextFormFieldDropdown extends StatelessWidget {
  const TextFormFieldDropdown({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.greyRoadmap,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(10),
      ),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      margin: EdgeInsets.symmetric(vertical: 8),
      child: FormBuilderTextField(
        name: 'customerPhone',
        keyboardType: TextInputType.numberWithOptions(
          decimal: false,
          signed: false,
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        decoration: InputDecoration(
            contentPadding: EdgeInsets.zero,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            errorBorder: InputBorder.none,
            focusedErrorBorder: InputBorder.none,
            border: InputBorder.none,
            disabledBorder: InputBorder.none,
            errorMaxLines: 3
        ),
      ),
    );
  }
}

