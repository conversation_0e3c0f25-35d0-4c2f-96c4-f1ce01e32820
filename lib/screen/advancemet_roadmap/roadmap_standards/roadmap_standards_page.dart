import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/screen/advancemet_roadmap/functional_standards/functional_standards_page.dart';
import 'package:hstd/screen/advancemet_roadmap/register_roadmap/register_roadmap_page.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:hstd/widget/primary_button.dart';

import 'roadmap_standards_cubit.dart';

class RoadmapStandardsArguments {
  String param;

  RoadmapStandardsArguments();
}

class RoadmapStandardsPage extends StatelessWidget {
  final RoadmapStandardsArguments arguments;

  const RoadmapStandardsPage({
    Key key,
    this.arguments,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return RoadmapStandardsCubit();
      },
      child: const RoadmapStandardsChildPage(),
    );
  }
}

class RoadmapStandardsChildPage extends StatefulWidget {
  const RoadmapStandardsChildPage({Key key}) : super(key: key);

  @override
  State<RoadmapStandardsChildPage> createState() =>
      _RoadmapStandardsChildPageState();
}

class _RoadmapStandardsChildPageState extends State<RoadmapStandardsChildPage> {
  RoadmapStandardsCubit _cubit;
  int itemSelectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppStrings.roadmap_standards,
          style: TextStyle(color: Colors.black),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: BlocBuilder<RoadmapStandardsCubit, RoadmapStandardsState>(
          buildWhen: (previous, current) =>
              previous.loadDataStatus != current.loadDataStatus ||
              previous.listenChange != current.listenChange,
          builder: (context, state) {
            if (state.loadDataStatus == LoadStatus.loading) {
              return LoadingIndicator();
            } else if (state.loadDataStatus == LoadStatus.failure) {
              return Column(
                children: [
                  Container(
                    height: 1,
                    color: AppColors.greyRoadmap,
                  ),
                  Expanded(
                    child: EmptyListWidget(
                      content: AppStrings.not_has_roadmap_standards,
                      onRefresh: () async {
                        _cubit.loadInitialData();
                      },
                    ),
                  ),
                ],
              );
            } else {
              return Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          height: 1,
                          color: AppColors.greyRoadmap,
                        ),
                        Padding(
                          padding: EdgeInsets.fromLTRB(12, 20, 12, 12),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Padding(
                                  padding: EdgeInsets.only(right: 10.0),
                                  child: Icon(
                                    Icons.account_circle,
                                    color: Color(0xFFB5B4B4),
                                    size: 22,
                                  )),
                              Flexible(
                                child: Text(
                                  '${state.employeeCode} - ${state.employeeName}',
                                  style: AppTextStyle.blackS14W600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          height: 1,
                          color: AppColors.greyRoadmap,
                        ),
                        SizedBox(height: 6),
                        ItemListView(
                          title: AppStrings.current_position,
                          text: state.data.positionStandardName,
                        ),
                        // ItemListView(
                        //   title: AppStrings.current_unit,
                        //   text: state.data.unitName,
                        // ),
                        // ItemListView(
                        //   title: AppStrings.current_department,
                        //   text: state.data.departmentName,
                        // ),
                        SizedBox(
                          height: 24,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Divider(
                            height: 1,
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        ListView.builder(
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: state.data.processCareerDetails.length,
                          itemBuilder: (context, index) {
                            final item = state.data.processCareerDetails[index];
                            return Column(
                              children: [
                                ItemPosition(
                                  text: item.positionStandardName,
                                  onTap: () {
                                    _cubit.selectItem(index);
                                    itemSelectedIndex = index;
                                  },
                                  onViewDetail: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            FunctionalStandardsPage(
                                          arguments:
                                              FunctionalStandardsArguments(
                                                  id: item.positionStandardId),
                                        ),
                                      ),
                                    );
                                  },
                                  selected: item.isSelected ?? false,
                                  height: item.isSelected ? 40 : 10,
                                  isLast: index ==
                                      state.data.processCareerDetails.length -
                                          1,
                                ),
                                SizedBox(
                                  height: 12,
                                )
                              ],
                            );
                          },
                        ),
                        SizedBox(
                          height: 60,
                        )
                      ],
                    ),
                  ),
                  Visibility(
                    visible: state.loadDataStatus == LoadStatus.loading,
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.primaryColor,
                            ),
                          ),
                          SizedBox(
                            height: 50,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
      bottomSheet: Container(
        decoration: BoxDecoration(color: Colors.white, boxShadow: [
          new BoxShadow(
            color: Colors.grey,
            offset: new Offset(0.0, 0.0),
            blurRadius: 5.0,
          )
        ]),
        child: PrimaryButton(
          borderRadius: BorderRadius.circular(24),
          title: AppStrings.register_advancement_roadmap,
          titleStyle: AppTextStyle.primaryColorS16W600,
          height: 48,
          padding: EdgeInsets.fromLTRB(16, 8, 16, 25),
          color: Colors.white,
          borderColor: AppColors.primaryColor,
          onTap: () {
            if (itemSelectedIndex==0) {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => RegisterRoadmapPage(
                    arguments: RegisterRoadmapArguments(),
                  ),
                ),
              );
            } else {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => RegisterRoadmapPage(
                    arguments: RegisterRoadmapArguments(
                      processCareerDetailDto:
                      _cubit.state.processCareerDetailSelected,
                    ),
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }
}
