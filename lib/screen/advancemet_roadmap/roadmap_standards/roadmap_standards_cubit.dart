import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/advancement_roadmap/process_career_detail_dto.dart';
import 'package:hstd/models/advancement_roadmap/standard_dto.dart';
import 'package:hstd/repositories/advancement_roadmap_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'roadmap_standards_state.dart';

class RoadmapStandardsCubit extends Cubit<RoadmapStandardsState> {

  AdvancementRoadmapService _service = AdvancementRoadmapServiceImp();

  RoadmapStandardsCubit() : super(const RoadmapStandardsState());

  Future<void> loadInitialData() async {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));

    try {
      SharedPreferences sharedPreferences = await SharedPreferences.getInstance();

      final result = await _service.getStandard();

      if (result != null) {

        var data = result;
        for (int i=0;i<data.processCareerDetails.length;i++) {
          data.processCareerDetails[i].isSelected = false;
        }
        data.processCareerDetails[0].isSelected = true;
        var processCareerDetailSelected = data.processCareerDetails[0];

        emit(state.copyWith(
          loadDataStatus: LoadStatus.success,
          data: data,
          processCareerDetailSelected: processCareerDetailSelected,
          employeeName: sharedPreferences.get(AppConstant.KEY_USER_NAME_VCC) ?? '',
          employeeCode: sharedPreferences.get(AppConstant.KEY_USER_CODE_VCC) ?? '',
        ));
      } else {
        emit(state.copyWith(loadDataStatus: LoadStatus.failure));
      }
    } catch (e) {
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }

  void selectItem(int index) {
    var data = state.data;
    var processCareerDetailSelected;
    if (data.processCareerDetails[index].isSelected) {
      data.processCareerDetails[index].isSelected=false;
    } else {
      for (int i=0;i<data.processCareerDetails.length;i++) {
        if (i==index) {
          data.processCareerDetails[i].isSelected = true;
          processCareerDetailSelected = data.processCareerDetails[i];
        } else {
          data.processCareerDetails[i].isSelected = false;
        }
      }
    }
    emit(
      state.copyWith(
        data: data,
        processCareerDetailSelected: processCareerDetailSelected,
        listenChange: !state.listenChange,
      ),
    );

  }
}
