part of 'roadmap_standards_cubit.dart';

class RoadmapStandardsState extends Equatable {
  final LoadStatus loadDataStatus;
  final StandardDto data;
  final ProcessCareerDetailDto processCareerDetailSelected;
  final bool listenChange;
  final String employeeCode;
  final String employeeName;

  const RoadmapStandardsState({
    this.loadDataStatus = LoadStatus.initial,
    this.data,
    this.processCareerDetailSelected,
    this.listenChange = false,
    this.employeeCode,
    this.employeeName,
  });

  @override
  List<Object> get props =>
      [
        loadDataStatus,
        data,
        processCareerDetailSelected,
        listenChange,
        employeeCode,
        employeeName,
      ];

  RoadmapStandardsState copyWith({
    LoadStatus loadDataStatus,
    StandardDto data,
    ProcessCareerDetailDto processCareerDetailSelected,
    bool listenChange,
    String employeeCode,
    String employeeName,
  }) {
    return RoadmapStandardsState(
      loadDataStatus: loadDataStatus ?? this.loadDataStatus,
      data: data ?? this.data,
      processCareerDetailSelected: processCareerDetailSelected ??
          this.processCareerDetailSelected,
      listenChange: listenChange ?? this.listenChange,
      employeeCode: employeeCode ?? this.employeeCode,
      employeeName: employeeName ?? this.employeeName,
    );
  }
}
