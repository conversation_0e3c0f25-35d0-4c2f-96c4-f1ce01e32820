import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_indicator.dart';

import 'functional_standards_cubit.dart';

class FunctionalStandardsArguments {
  int id;

  FunctionalStandardsArguments({this.id});
}

class FunctionalStandardsPage extends StatelessWidget {
  final FunctionalStandardsArguments arguments;

  const FunctionalStandardsPage({
    Key key,
    this.arguments,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return FunctionalStandardsCubit();
      },
      child: FunctionalStandardsChildPage(
        arguments: arguments,
      ),
    );
  }
}

class FunctionalStandardsChildPage extends StatefulWidget {
  final FunctionalStandardsArguments arguments;

  const FunctionalStandardsChildPage({
    Key key,
    this.arguments,
  }) : super(key: key);

  @override
  State<FunctionalStandardsChildPage> createState() =>
      _FunctionalStandardsChildPageState();
}

class _FunctionalStandardsChildPageState
    extends State<FunctionalStandardsChildPage> {
  FunctionalStandardsCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData(id: widget.arguments.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppStrings.functional_standards,
          style: TextStyle(color: Colors.black),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: BlocBuilder<FunctionalStandardsCubit, FunctionalStandardsState>(
          buildWhen: (previous, current) =>
              previous.loadDataStatus != current.loadDataStatus,
          builder: (context, state) {
            if (state.loadDataStatus == LoadStatus.loading) {
              return LoadingIndicator();
            } else if (state.loadDataStatus == LoadStatus.failure) {
              return Column(
                children: [
                  Container(
                    height: 1,
                    color: AppColors.greyRoadmap,
                  ),
                  Expanded(
                    child: EmptyListWidget(
                      onRefresh: () async {
                        _cubit.loadInitialData(id: widget.arguments.id);
                      },
                    ),
                  ),
                ],
              );
            } else {
              return Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          height: 1,
                          color: AppColors.greyRoadmap,
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        ItemListView(
                          title: AppStrings.target_title,
                          text: state.data.positionStandardName,
                        ),
                        // ItemListView(
                        //   title: AppStrings.unit,
                        //   text: state.data.unitName,
                        // ),
                        // ItemListView(
                        //   title: AppStrings.department,
                        //   text: state.data.departmentName,
                        // ),
                        Divider(height: 1),
                        ListView.builder(
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: state.listOption.length,
                            itemBuilder: (context, index) {
                              final item = state.listOption[index];
                              if (item.type == 4 || item.type == 3) {
                                String value = state.mapOption[item.code] ?? '';
                                if (value.isNotEmpty) {
                                  return Column(
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            left: 16, top: 10),
                                        child: Text(item.name,
                                            style: AppTextStyle
                                                .greyTitleS14NormalRoadmap),
                                      ),
                                      Container(
                                        width:
                                        MediaQuery.of(context).size.width,
                                        child: Padding(
                                          padding: const EdgeInsets.only(
                                            left: 16,
                                            top: 5,
                                            right: 16,
                                            bottom: 12,
                                          ),
                                          child: Text(
                                            value,
                                            style: AppTextStyle
                                                .blackTextS14NormalRoadmap,
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                } else {
                                  return SizedBox();
                                }
                              } else if (item.type == 2) {
                                String value = _cubit.parseValue(
                                    item: item, map: state.mapOption);
                                if (value.isNotEmpty) {
                                  return Column(
                                    crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            left: 16, top: 10),
                                        child: Text(item.name,
                                            style: AppTextStyle
                                                .greyTitleS14NormalRoadmap),
                                      ),
                                      Container(
                                        width:
                                        MediaQuery.of(context).size.width,
                                        child: Padding(
                                          padding: const EdgeInsets.only(
                                            left: 16,
                                            top: 5,
                                            right: 16,
                                            bottom: 12,
                                          ),
                                          child: Text(
                                            value,
                                            style: AppTextStyle
                                                .blackTextS14NormalRoadmap,
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                } else {
                                  return SizedBox();
                                }
                              } else {
                                String value = _cubit.parseValue(
                                    item: item, map: state.mapOption);
                                if (value.isNotEmpty) {
                                  return ItemListView(
                                    title: item.name,
                                    text: value,
                                  );
                                } else {
                                  return SizedBox();
                                }
                              }
                            }),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: state.loadDataStatus == LoadStatus.loading,
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.primaryColor,
                            ),
                          ),
                          SizedBox(
                            height: 50,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }
}
