import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/advancement_roadmap/option_dto.dart';
import 'package:hstd/models/advancement_roadmap/position_standard_dto.dart';
import 'package:hstd/repositories/advancement_roadmap_service.dart';

part 'functional_standards_state.dart';

class FunctionalStandardsCubit extends Cubit<FunctionalStandardsState> {
  AdvancementRoadmapService _service = AdvancementRoadmapServiceImp();

  FunctionalStandardsCubit() : super(const FunctionalStandardsState());

  Future<void> loadInitialData({int id}) async {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));

    try {
      final listOption = await _service.getListOption();

      final result = await _service.getPositionStandardById(id: id);

      if (result != null) {
        Map<String, String> map = Map();
        if (result.options != null) {
          for (int i = 0; i < result.options.length; i++) {
            map[result.options[i].optionCode] = result.options[i].value;
          }
        }

        emit(state.copyWith(
          loadDataStatus: LoadStatus.success,
          data: result,
          listOption: listOption,
          mapOption: map,
        ));
      } else {
        emit(state.copyWith(loadDataStatus: LoadStatus.failure));
      }
    } catch (e) {
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }

  String parseValue({OptionDto item, Map<String, dynamic> map}) {
    if (map[item.code] == null) {
      return '';
    }
    if (item.value != null) {
      String result = '';
      List<String> arr = map[item.code].split(',');
      for (int i = 0; i < arr.length; i++) {
        for (int j = 0; j < item.listValue.length; j++) {
          if (arr[i] == item.listValue[j].value.toString()) {
            if (result.isEmpty)
              result += item.listValue[j].name;
            else
              result += ', ' + item.listValue[j].name;
          }
        }
      }
      return result;
    } else {
      return map[item.code];
    }
  }
}
