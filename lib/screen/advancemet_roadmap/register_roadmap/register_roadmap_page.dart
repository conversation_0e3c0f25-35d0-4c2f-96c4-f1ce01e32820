import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/advancement_roadmap/position_standard_dto.dart';
import 'package:hstd/models/advancement_roadmap/process_career_detail_dto.dart';
import 'package:hstd/models/advancement_roadmap/process_career_dto.dart';
import 'package:hstd/models/advancement_roadmap/search_standard_request.dart';
import 'package:hstd/models/organization.dart';
import 'package:hstd/screen/advancemet_roadmap/advancement_roadmap.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/screen/advancemet_roadmap/register_history/register_history_page.dart';
import 'package:hstd/screen/advancemet_roadmap/register_roadmap/advancement_detail/advancement_roadmap_detail_page.dart';
import 'package:hstd/utils/pushMultiPageScreen.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:hstd/widget/loading_more_row_widget.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:hstd/widget/sliver_grid_delegate.dart';

import 'register_roadmap_cubit.dart';

class RegisterRoadmapArguments {
  final bool fromDialog;
  final ProcessCareerDetailDto processCareerDetailDto;

  RegisterRoadmapArguments({
    this.fromDialog = false,
    this.processCareerDetailDto,
  });
}

class RegisterRoadmapPage extends StatelessWidget {
  final RegisterRoadmapArguments arguments;

  const RegisterRoadmapPage({
    Key key,
    this.arguments,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return RegisterRoadmapCubit();
      },
      child: RegisterRoadmapChildPage(
        arguments: arguments,
      ),
    );
  }
}

class RegisterRoadmapChildPage extends StatefulWidget {
  final RegisterRoadmapArguments arguments;

  const RegisterRoadmapChildPage({this.arguments, Key key}) : super(key: key);

  @override
  State<RegisterRoadmapChildPage> createState() =>
      _RegisterRoadmapChildPageState();
}

class _RegisterRoadmapChildPageState extends State<RegisterRoadmapChildPage> {
  RegisterRoadmapCubit _cubit;
  final _scrollController = ScrollController();
  final _scrollThreshold = 200.0;
  bool showRoadmap = false;
  bool isFirstLoad = true;

  TextEditingController _titleController = TextEditingController();
  TextEditingController _roadmapController = TextEditingController();
  TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _scrollController.addListener(_onScroll);
    if (widget.arguments.processCareerDetailDto!=null) {
      _cubit.loadInitialDataFromStandards(
          widget.arguments.processCareerDetailDto
      );
    } else {
      _cubit.loadInitialData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RegisterRoadmapCubit, RegisterRoadmapState>(
      listenWhen: (previous, current) =>
          previous.loadRoadmapStatus != current.loadRoadmapStatus ||
          previous.confirmStatus != current.confirmStatus,
      listener: (context, state) {
        if (state.loadRoadmapStatus == LoadStatus.success) {
          if (state.listRoadmap.length == 0) {
            _openCustomDialog(
              context: context,
              dialog: DialogNotify(
                content: AppStrings.not_has_roadmap,
              ),
            );
            _cubit.resetLoadRoadmapStatus();
          } else {
            _cubit.selectStandard(state.standardTransient);
            _titleController.text =
                state.standardTransient.positionStandardName;
            _searchController.text = '';
            _roadmapController.text = '';
            showRoadmap = false;
            _cubit.setShowButtonRegister(false);
            _cubit.clearData();

            if (state.listProcessCareer.length > 0) {
              showRoadmap = true;
            }
            if (state.listProcessCareer.length == 1) {
              _cubit.setProcessCareer(state.listProcessCareer[0]);
              _roadmapController.text =
                  _cubit.createRoadmap(state.listProcessCareer[0].details);
              _cubit.setShowButtonRegister(true);
            }
            Navigator.pop(context);
            _cubit.resetLoadRoadmapStatus();
          }
        }
        if (state.confirmStatus == LoadStatus.success) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => RegisterHistoryPage(),
            ),
          );
          _openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: AppStrings.register_success,
              showIcon: true,
            ),
          );
        } else if (state.confirmStatus == LoadStatus.failure) {
          _openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: state.message,
            ),
          );
        }
      },
      builder: (context, state) {
        return WillPopScope(
          onWillPop: () async {
            if (widget.arguments.fromDialog) {
              Navigator.of(context).pop();
              pushWithoutAnimation(
                AdvancementRoadmapScreen(),
              );
            } else {
              Navigator.of(context).pop();
            }
            return true;
          },
          child: Stack(
            children: [
              Scaffold(
                appBar: AppBar(
                  title: Text(
                    AppStrings.register_advancement_roadmap,
                    style: TextStyle(color: Colors.black),
                  ),
                  foregroundColor: Colors.black,
                  centerTitle: true,
                  backgroundColor: Colors.white,
                  elevation: 0,
                ),
                body: SafeArea(
                  child:
                      BlocBuilder<RegisterRoadmapCubit, RegisterRoadmapState>(
                    buildWhen: (previous, current) =>
                        previous.loadDataStatus != current.loadDataStatus ||
                        previous.loadRoadmapStatus != current.loadRoadmapStatus,
                    builder: (context, state) {
                      if (state.loadDataStatus == LoadStatus.loading ||
                          state.loadRoadmapStatus == LoadStatus.loading) {
                        return LoadingIndicator();
                      } else if (state.loadDataStatus == LoadStatus.failure) {
                        return Column(
                          children: [
                            Container(
                              height: 1,
                              color: AppColors.greyRoadmap,
                            ),
                            Expanded(
                              child: EmptyListWidget(
                                onRefresh: () async {
                                  _cubit.loadInitialData();
                                },
                              ),
                            ),
                          ],
                        );
                      } else {
                        if (isFirstLoad) {
                          if (widget.arguments.processCareerDetailDto!=null) {
                            _setDataIfFromStandards();
                          } else if (_cubit.state.listRegisteredRoadmap != null) {
                            _setDataIfRegistered();
                          }
                          isFirstLoad = false;
                        }

                        return SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Container(
                                height: 1,
                                color: AppColors.greyRoadmap,
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              ItemListView(
                                title: AppStrings.current_position,
                                text: state.positionStandardNow
                                    .positionStandardNowName,
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              _textField(
                                  title: AppStrings.target_title,
                                  controller: _titleController,
                                  onTap: () async {
                                    // _cubit.getListUnit();
                                    _cubit.setUnit();
                                    showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (BuildContext context) {
                                          return _bottomSheetTitleTarget(
                                            title:
                                                AppStrings.select_target_title,
                                            controller: _searchController,
                                          );
                                        });
                                  }),
                              SizedBox(
                                height: 20,
                              ),
                              Visibility(
                                visible: showRoadmap,
                                child: _textField(
                                    title: AppStrings.advancement_roadmap,
                                    controller: _roadmapController,
                                    onTap: () {
                                      showDialog(
                                          context: context,
                                          barrierDismissible: false,
                                          builder: (BuildContext context) {
                                            return _bottomSheetAdvancementRoadmap(
                                                title: AppStrings
                                                    .select_advancement_roadmap,
                                                listItem:
                                                    state.listProcessCareer);
                                          });
                                    }),
                              ),
                              BlocBuilder<RegisterRoadmapCubit,
                                  RegisterRoadmapState>(
                                buildWhen: (previous, current) =>
                                    previous.processCareer !=
                                    current.processCareer,
                                builder: (context, state) {
                                  return SingleChildScrollView(
                                    child: Visibility(
                                      visible: showRoadmap &&
                                          _roadmapController.text.isNotEmpty,
                                      child: Align(
                                        alignment: Alignment.bottomCenter,
                                        child: InkWell(
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    AdvancementRoadmapDetail(
                                                  data: state.processCareer,
                                                ),
                                              ),
                                            );
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(
                                              color: AppColors.pinkButtonLogOut,
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            ),
                                            margin: const EdgeInsets.only(
                                                bottom: 100, top: 40),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 16,
                                                      vertical: 7),
                                              child: Text(
                                                AppStrings
                                                    .view_advancement_roadmap,
                                                style: AppTextStyle
                                                    .primaryColorS13W500,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        );
                      }
                    },
                  ),
                ),
                bottomSheet: BlocBuilder<RegisterRoadmapCubit,
                        RegisterRoadmapState>(
                    buildWhen: (previous, current) =>
                        previous.showButtonRegister !=
                        current.showButtonRegister,
                    builder: (context, state) {
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          boxShadow: [
                            new BoxShadow(
                              color: Colors.grey,
                              offset: new Offset(0.0, 0.0),
                              blurRadius: 5.0,
                            )
                          ],
                        ),
                        child: state.showButtonRegister
                            ? GridView(
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight(
                                  crossAxisCount: 2,
                                  crossAxisSpacing: 0,
                                  mainAxisSpacing: 0,
                                  height: 80,
                                ),
                                children: [
                                  PrimaryButton(
                                    borderRadius: BorderRadius.circular(24),
                                    title: AppStrings.cancel,
                                    titleStyle: AppTextStyle
                                        .greyTextRoadmapColorS16W500,
                                    height: 48,
                                    padding: EdgeInsets.fromLTRB(16, 8, 16, 25),
                                    color: Colors.white,
                                    borderColor: AppColors.grey,
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                  ),
                                  PrimaryButton(
                                    borderRadius: BorderRadius.circular(24),
                                    title: AppStrings.register,
                                    titleStyle: AppTextStyle.whiteS16W600,
                                    height: 48,
                                    padding: EdgeInsets.fromLTRB(16, 8, 16, 25),
                                    onTap: () async {
                                      String validate = await _cubit
                                          .validateRegister(context);
                                      if (validate == null) {
                                        _openCustomDialog(
                                          context: context,
                                          dialog: DialogNotify(
                                            content: AppStrings.has_error,
                                          ),
                                        );
                                      }
                                      if (validate.isEmpty) {
                                        showDialog(
                                          context: context,
                                          builder: (BuildContext context) =>
                                              ShowDialogNotifySuccess(
                                            title: AppStrings.confirm,
                                            firstContent: AppStrings
                                                .confirm_registered_roadmap,
                                            lastContent: '?',
                                            roadmap: _titleController.text,
                                            onConfirm: () {
                                              _cubit.registerRoadmap(context);
                                              Navigator.of(context).pop();
                                            },
                                          ),
                                          barrierDismissible: false,
                                        );
                                      } else {
                                        showDialog(
                                          context: context,
                                          builder: (BuildContext context) =>
                                              ShowDialogNotifySuccess(
                                            title: AppStrings.warning,
                                            firstContent: AppStrings
                                                .warning_registered_roadmap_first,
                                            lastContent: AppStrings
                                                .warning_registered_roadmap_last,
                                            roadmap: validate,
                                            onConfirm: () {
                                              _cubit.registerRoadmap(context);
                                              Navigator.of(context).pop();
                                            },
                                          ),
                                          barrierDismissible: false,
                                        );
                                      }
                                    },
                                  ),
                                ],
                              )
                            : PrimaryButton(
                                borderRadius: BorderRadius.circular(24),
                                title: AppStrings.cancel,
                                titleStyle: AppTextStyle.greyS16W600,
                                height: 48,
                                padding: EdgeInsets.fromLTRB(16, 8, 16, 25),
                                color: Colors.white,
                                borderColor: AppColors.borderColor,
                                onTap: () {
                                  Navigator.pop(context);
                                },
                              ),
                      );
                    }),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _textField({
    String title,
    VoidCallback onTap,
    TextEditingController controller,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
      ),
      child: InkWell(
        onTap: () {
          onTap();
        },
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(
                Radius.circular(10),
              ),
              border: Border.all(
                color: AppColors.greyRoadmap,
                width: 1,
              )),
          child: TextFormField(
            controller: controller,
            style: AppTextStyle.blackTextS16NormalRoadmap,
            maxLines: null,
            readOnly: true,
            enabled: false,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.fromLTRB(
                10.0,
                10.0,
                20.0,
                10.0,
              ),
              labelText: title,
              labelStyle: controller.text.isEmpty
                  ? AppTextStyle.blackColorS16Normal
                  : AppTextStyle.greyTextFieldLableS16W400,
              hintText: title,
              border: InputBorder.none,
              suffixIcon: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(AppVectors.ic_dropdown),
                  // Icon(
                  //   Icons.keyboard_arrow_down_outlined,
                  //   color: AppColors.greyTextFieldLable,
                  // )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _bottomSheetTitleTarget({
    String title,
    TextEditingController controller,
  }) {
    return Dialog(
      insetPadding: EdgeInsets.zero,
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.only(top: 20),
        padding: EdgeInsets.only(top: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: BlocBuilder<RegisterRoadmapCubit, RegisterRoadmapState>(
          bloc: _cubit,
          buildWhen: (previous, current) =>
              previous.step != current.step ||
              previous.loadGetUnitUserStatus != current.loadGetUnitUserStatus,
          builder: (context, state) {
            if (state.loadGetUnitUserStatus == LoadStatus.loading) {
              return LoadingIndicator();
            } else {
              return Column(
                children: [
                  ///button exit + Title
                  Stack(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 25),
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: GestureDetector(
                            child: Icon(
                              Icons.close,
                              color: Colors.black,
                            ),
                            onTap: () {
                              _searchController.text = '';
                              _cubit.clearData();
                              Navigator.of(context).pop();
                            },
                          ),
                        ),
                      ),
                      Center(
                          child: Text(title, style: AppTextStyle.blackS16Bold)),
                    ],
                  ),
                  SizedBox(height: 15),

                  ///Khu vực được chọn
                  if (state.step != StepSelectTitle.step_select_unit) ...[
                    Container(
                      width: MediaQuery.of(context).size.width,
                      color: AppColors.grayBackgroundRoadmap,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10, horizontal: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppStrings.selected_area,
                              style: AppTextStyle.greyTextRoadmapColorS14W500,
                            ),
                            InkWell(
                              onTap: () {
                                _searchController.text = '';
                                _cubit.backStep();
                              },
                              child: Text(
                                AppStrings.reset,
                                style: AppTextStyle.primaryS14W500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],

                  ///Cây cha con theo cấp
                  if (state.step == StepSelectTitle.step_select_department) ...[
                    ItemUnit(
                      unitName: state.unit.name,
                      unitBlock: state.unit.unitBlock,
                    ),
                  ],
                  if (state.step == StepSelectTitle.step_select_title) ...[
                    if (state.unit.unitBlock != 3) ...[
                      ItemDepartment(
                        unitName: state.unit.name,
                        department: state.department.name,
                      ),
                    ] else ...[
                      ItemUnit(
                        unitName: state.unit.name,
                        unitBlock: state.unit.unitBlock,
                      ),
                    ]
                  ],

                  ///title
                  if (state.step == StepSelectTitle.step_select_unit) ...[
                    _titleListView(title: AppStrings.unit),
                  ],
                  if (state.step == StepSelectTitle.step_select_department) ...[
                    _titleListView(title: AppStrings.department),
                  ],
                  if (state.step == StepSelectTitle.step_select_title) ...[
                    _titleListView(title: AppStrings.target_title),
                  ],

                  ///search bar
                  if (state.step == StepSelectTitle.step_select_unit) ...[
                    _searchTextBox(
                      hintText: AppStrings.search_center,
                      onChange: (value) {
                        _cubit.getListUnit(keyword: value);
                      },
                    ),
                  ],
                  if (state.step == StepSelectTitle.step_select_department) ...[
                    _searchTextBox(
                      hintText: AppStrings.search_department,
                      onChange: (value) {
                        _cubit.getListDepartment();
                      },
                    ),
                  ],
                  if (state.step == StepSelectTitle.step_select_title) ...[
                    _searchTextBox(
                      hintText: AppStrings.search_standard,
                      onChange: (value) {
                        if (state.unit.unitBlock != 3) {
                          _cubit.getListTitle(
                            searchStandardRequest: SearchStandardRequest(
                              page: 0,
                              size: 10,
                              departmentId: state.department.organizationId,
                              unitId: state.unit.organizationId,
                              positionStandard: value,
                            ),
                          );
                        } else {
                          _cubit.getListTitle(
                            searchStandardRequest: SearchStandardRequest(
                              page: 0,
                              size: 10,
                              unitId: -1,
                              unitBlock: state.unit.unitBlock,
                              positionStandard: value,
                            ),
                          );
                        }
                      },
                    ),
                  ],

                  ///listView
                  BlocBuilder<RegisterRoadmapCubit, RegisterRoadmapState>(
                    bloc: _cubit,
                    buildWhen: (previous, current) =>
                        previous.loadUnitStatus != current.loadUnitStatus ||
                        previous.step != current.step,
                    builder: (context, state) {
                      if (state.loadUnitStatus == LoadStatus.loading) {
                        return LoadingIndicator();
                      } else if (state.loadUnitStatus == LoadStatus.failure) {
                        return SizedBox();
                      } else {
                        if (state.step == StepSelectTitle.step_select_unit) {
                          return _listView(
                            onTap: (item) {
                              _cubit.selectUnit(item);
                              _searchController.text = '';
                              if (item.unitBlock != 3) {
                                _cubit.getListDepartment();
                              } else {
                                _cubit.getListTitle(
                                  searchStandardRequest: SearchStandardRequest(
                                    page: 0,
                                    size: 10,
                                    unitId: -1,
                                    unitBlock: item.unitBlock,
                                  ),
                                );
                              }
                            },
                            listOrganization: state.listUnit,
                          );
                        } else if (state.step ==
                            StepSelectTitle.step_select_department) {
                          return _listView(
                            onTap: (item) {
                              _searchController.text = '';
                              _cubit.selectDepartment(item);
                              _cubit.getListTitle(
                                searchStandardRequest: SearchStandardRequest(
                                  page: 0,
                                  size: 10,
                                  departmentId: item.organizationId,
                                  unitId: state.unit.organizationId,
                                ),
                              );
                            },
                            listOrganization: state.listDepartment,
                          );
                        } else {
                          return _listView(
                            onTap: (item) {
                              _searchController.text = '';
                              _cubit.setStandardTransient(item);
                              _cubit.getRoadmap(
                                nowId: state
                                    .positionStandardNow.positionStandardNowId,
                                targetId: item.id,
                              );
                            },
                            listStandard: state.listStandard,
                          );
                        }
                      }
                    },
                  ),

                  ///end
                ],
              );
            }
          },
        ),
      ),
    );
  }

  Widget _bottomSheetAdvancementRoadmap({
    String title,
    List<ProcessCareerDto> listItem,
  }) {
    return Dialog(
      insetPadding: EdgeInsets.zero,
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        margin:
            EdgeInsets.only(top: MediaQuery.of(context).size.height * 1 / 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            ///button exit + Title
            Padding(
              padding:
                  EdgeInsets.only(left: 16, right: 16, top: 20, bottom: 10),
              child: Stack(
                children: [
                  Container(
                    child: Align(
                      alignment: Alignment.topLeft,
                      child: GestureDetector(
                        child: SvgPicture.asset(AppVectors.icCloseDialog),
                        onTap: () {
                          _cubit.clearData();
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ),
                  Center(child: Text(title, style: AppTextStyle.blackS16Bold)),
                ],
              ),
            ),

            Divider(),

            ///listView
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: ListView.separated(
                shrinkWrap: true,
                controller: _scrollController,
                itemCount: listItem.length,
                separatorBuilder: (_, index) => SizedBox(height: 0),
                itemBuilder: (context, index) {
                  var item = listItem[index];
                  return Column(children: <Widget>[
                    InkWell(
                      onTap: () {
                        _cubit.setProcessCareer(item);
                        _cubit.setShowButtonRegister(true);
                        _roadmapController.text =
                            _cubit.createRoadmap(item.details);
                        Navigator.of(context).pop();
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(
                                vertical: 13, horizontal: 16),
                            child: Row(
                              children: [
                                Text(
                                  '${AppStrings.roadmap} ${index + 1}',
                                  style: AppTextStyle.primaryS14W500,
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 5),
                                  child: Icon(
                                    Icons.circle,
                                    size: 4,
                                    color: AppColors.borderColor,
                                  ),
                                ),
                                Text(
                                  '${item.details.length} ${AppStrings.step}',
                                  style: AppTextStyle.greyTitleS14NormalRoadmap,
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 16, right: 16, bottom: 10),
                            child: Text(
                              _cubit.createRoadmap(item.details),
                              style: AppTextStyle.blackTextS14NormalRoadmap,
                              maxLines: null,
                            ),
                          )
                        ],
                      ),
                    ),
                    Divider(),
                  ]);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget get _loadMoreWidget {
    return BlocBuilder<RegisterRoadmapCubit, RegisterRoadmapState>(
      bloc: _cubit,
      buildWhen: (previous, current) =>
          previous.loadUnitStatus != current.loadUnitStatus,
      builder: (context, state) {
        return Visibility(
          visible: state.loadUnitStatus == LoadStatus.loadMore,
          child: const SafeArea(
            top: false,
            child: SizedBox(
              height: 100,
              child: LoadingMoreRowWidget(),
            ),
          ),
        );
      },
    );
  }

  void _onScroll() {
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    if (maxScroll - currentScroll <= _scrollThreshold) {
      _cubit.fetchNextList(keyword: _searchController.text);
    }
  }

  Widget _titleListView({String title}) {
    return Container(
      width: MediaQuery.of(context).size.width,
      color: AppColors.grayBackgroundRoadmap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        child: Text(
          title,
          style: AppTextStyle.greyTextRoadmapColorS14W500,
        ),
      ),
    );
  }

  Widget _searchTextBox({
    Function(dynamic value) onChange,
    String hintText,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: Container(
        height: 42,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(60),
          border: Border.all(width: 1, color: AppColors.greyRoadmap),
        ),
        child: Row(
          children: [
            SizedBox(width: 10),
            SvgPicture.asset(
              AppVectors.icSearch,
            ),
            Expanded(
              child: TextField(
                cursorRadius: Radius.circular(2),
                controller: _searchController,
                onChanged: (value) {
                  onChange.call(value);
                },
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding:
                      EdgeInsets.only(left: 8, right: 8, bottom: 6, top: 0),
                  hintText: hintText,
                  hintStyle: AppTextStyle.hintTextFieldS14W500,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _listView({
    Function(dynamic item) onTap,
    List<Organization> listOrganization,
    List<PositionStandardDto> listStandard,
  }) {
    return Expanded(
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 24),
            child: ListView.separated(
              shrinkWrap: true,
              controller: _scrollController,
              itemCount: listOrganization != null
                  ? listOrganization?.length ?? 0
                  : listStandard?.length ?? 0,
              separatorBuilder: (_, index) => SizedBox(),
              itemBuilder: (context, index) {
                if (listOrganization != null) {
                  var item = listOrganization[index];
                  if (_cubit.state.step !=
                      StepSelectTitle.step_select_department) {
                    return Column(children: <Widget>[
                      InkWell(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          child: Text(item?.name ?? '',
                              style: TextStyle(fontSize: 16)),
                        ),
                        onTap: () {
                          onTap.call(item);
                        },
                      ),
                      Divider(),
                    ]);
                  } else {
                    return item.name
                            .toLowerCase()
                            .contains(_searchController.text.toLowerCase())
                        ? Column(children: <Widget>[
                            InkWell(
                              child: Container(
                                width: double.infinity,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                child: Text(item?.name ?? '',
                                    style: TextStyle(fontSize: 16)),
                              ),
                              onTap: () {
                                onTap.call(item);
                              },
                            ),
                            Divider(),
                          ])
                        : SizedBox();
                  }
                } else {
                  var item = listStandard[index];
                  return Column(children: <Widget>[
                    InkWell(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 10),
                        child: Text(item?.positionStandardName ?? '',
                            style: TextStyle(fontSize: 16)),
                      ),
                      onTap: () {
                        onTap.call(item);
                      },
                    ),
                    Divider(),
                  ]);
                }
              },
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _loadMoreWidget,
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _roadmapController.dispose();
    _titleController.dispose();
    _searchController.dispose();
    _cubit.close();
    super.dispose();
  }

  void _openCustomDialog({
    BuildContext context,
    Widget dialog,
  }) {
    showGeneralDialog(
        barrierColor: Colors.black.withOpacity(0.5),
        transitionBuilder: (context, a1, a2, widget) {
          return ScaleTransition(
              scale: Tween<double>(begin: 0.5, end: 1.0).animate(a1),
              child: FadeTransition(
                opacity: Tween<double>(begin: 0.5, end: 1.0).animate(a1),
                child: dialog,
              ));
        },
        transitionDuration: const Duration(milliseconds: 300),
        barrierDismissible: false,
        barrierLabel: '',
        context: context,
        pageBuilder: (context, animation1, animation2) {
          return SizedBox();
        });
  }

  void _setDataIfRegistered() {
    _titleController.text =
        _cubit.state.listRegisteredRoadmap.first.positionStandardNameTarget;

    if (_cubit.state.registeredRoadmap.details.length > 0) {
      showRoadmap = true;
      _roadmapController.text =
          _cubit.createRoadmap(_cubit.state.registeredRoadmap.details);
      _cubit.setShowButtonRegister(true);
    }
  }

  void _setDataIfFromStandards() {
    _titleController.text =
        _cubit.state.standard.positionStandardName;
    showRoadmap = true;
  }

  Future<T> pushWithoutAnimation<T extends Object>(Widget page) {
    Route route = NoAnimationPageRoute(builder: (BuildContext context) => page);
    return Navigator.push(context, route);
  }
}
