part of 'register_roadmap_cubit.dart';

class RegisterRoadmapState extends Equatable {
  final LoadStatus loadDataStatus;
  final LoadStatus loadRoadmapStatus;
  final LoadStatus loadUnitStatus;
  final LoadStatus confirmStatus;
  final LoadStatus loadGetUnitUserStatus;
  final StepSelectTitle step;
  final PositionStandardDto positionStandardNow;
  final bool listenChange;
  final List<Organization> listUnit;
  final List<Organization> listDepartment;
  final List<PositionStandardDto> listStandard;
  final List<ProcessCareerDto> listProcessCareer;
  final List<ProcessCareerDto> listRoadmap;
  final List<RegisterRoadmapDto> listRegisteredRoadmap;
  final ProcessCareerDto processCareer;
  final SearchStandardRequest searchStandardRequest;
  final Organization unit;
  final Organization department;
  final PositionStandardDto standard;
  final PositionStandardDto standardTransient;
  final int page, totalPages, pageSize;
  final bool showButtonRegister;
  final String message;
  final RegisterRoadmapDto registeredRoadmap;

  const RegisterRoadmapState({
    this.loadDataStatus = LoadStatus.initial,
    this.loadRoadmapStatus = LoadStatus.initial,
    this.loadUnitStatus = LoadStatus.initial,
    this.confirmStatus = LoadStatus.initial,
    this.loadGetUnitUserStatus = LoadStatus.initial,
    this.step = StepSelectTitle.step_select_unit,
    this.positionStandardNow,
    this.listenChange,
    this.listUnit,
    this.listDepartment,
    this.page = 0,
    this.totalPages,
    this.pageSize = 20,
    this.unit,
    this.department,
    this.listStandard,
    this.searchStandardRequest,
    this.standard,
    this.standardTransient,
    this.listProcessCareer,
    this.processCareer,
    this.showButtonRegister = false,
    this.message,
    this.listRoadmap,
    this.listRegisteredRoadmap,
    this.registeredRoadmap,
  });

  @override
  List<Object> get props =>
      [
        loadDataStatus,
        loadRoadmapStatus,
        loadGetUnitUserStatus,
        step,
        positionStandardNow,
        listenChange,
        loadUnitStatus,
        listUnit,
        listDepartment,
        page,
        totalPages,
        pageSize,
        unit,
        department,
        listStandard,
        searchStandardRequest,
        standard,
        listProcessCareer,
        processCareer,
        showButtonRegister,
        confirmStatus,
        message,
        standardTransient,
        listRoadmap,
        listRegisteredRoadmap,
        registeredRoadmap,
      ];

  RegisterRoadmapState copyWith({
    LoadStatus loadDataStatus,
    LoadStatus loadRoadmapStatus,
    LoadStatus confirmStatus,
    LoadStatus loadGetUnitUserStatus,
    StepSelectTitle step,
    PositionStandardDto positionStandardNow,
    bool listenChange,
    LoadStatus loadUnitStatus,
    List<Organization> listUnit,
    List<Organization> listDepartment,
    List<ProcessCareerDto> listProcessCareer,
    List<ProcessCareerDto> listRoadmap,
    ProcessCareerDto processCareer,
    int page,
    int totalPages,
    int pageSize,
    Organization unit,
    Organization department,
    List<PositionStandardDto> listStandard,
    SearchStandardRequest searchStandardRequest,
    PositionStandardDto standard,
    PositionStandardDto standardTransient,
    bool showButtonRegister,
    String message,
    List<RegisterRoadmapDto> listRegisteredRoadmap,
    RegisterRoadmapDto registeredRoadmap,
  }) {
    return RegisterRoadmapState(
      loadDataStatus: loadDataStatus ?? this.loadDataStatus,
      loadRoadmapStatus: loadRoadmapStatus ?? this.loadRoadmapStatus,
      loadGetUnitUserStatus: loadGetUnitUserStatus ??
          this.loadGetUnitUserStatus,
      confirmStatus: confirmStatus ?? this.confirmStatus,
      step: step ?? this.step,
      positionStandardNow: positionStandardNow ?? this.positionStandardNow,
      listenChange: listenChange ?? this.listenChange,
      loadUnitStatus: loadUnitStatus ?? this.loadUnitStatus,
      listUnit: listUnit ?? this.listUnit,
      listDepartment: listDepartment ?? this.listDepartment,
      page: page ?? this.page,
      totalPages: totalPages ?? this.totalPages,
      pageSize: pageSize ?? this.pageSize,
      unit: unit ?? this.unit,
      department: department ?? this.department,
      listStandard: listStandard ?? this.listStandard,
      searchStandardRequest:
      searchStandardRequest ?? this.searchStandardRequest,
      standard: standard ?? this.standard,
      standardTransient: standardTransient ?? this.standardTransient,
      listProcessCareer: listProcessCareer ?? this.listProcessCareer,
      processCareer: processCareer ?? this.processCareer,
      showButtonRegister: showButtonRegister ?? this.showButtonRegister,
      message: message ?? this.message,
      listRoadmap: listRoadmap ?? this.listRoadmap,
      listRegisteredRoadmap: listRegisteredRoadmap ?? this.listRegisteredRoadmap,
      registeredRoadmap: registeredRoadmap ?? this.registeredRoadmap,
    );
  }
}
