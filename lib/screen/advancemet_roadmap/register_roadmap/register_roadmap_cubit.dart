import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/advancement_roadmap/position_standard_dto.dart';
import 'package:hstd/models/advancement_roadmap/process_career_detail_dto.dart';
import 'package:hstd/models/advancement_roadmap/process_career_dto.dart';
import 'package:hstd/models/advancement_roadmap/register_roadmap_dto.dart';
import 'package:hstd/models/advancement_roadmap/register_roadmap_request.dart';
import 'package:hstd/models/advancement_roadmap/search_standard_request.dart';
import 'package:hstd/models/organization.dart';
import 'package:hstd/models/update_resutl.dart';
import 'package:hstd/repositories/advancement_roadmap_service.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'register_roadmap_state.dart';

class RegisterRoadmapCubit extends Cubit<RegisterRoadmapState> {
  AdvancementRoadmapService _service = AdvancementRoadmapServiceImp();

  RegisterRoadmapCubit() : super(const RegisterRoadmapState());

  Future<void> loadInitialData() async {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));

    try {
      final positionStandardNow = await _service.getPositionStandardNow();

      final checkRegisteredRoadmap = await _service.checkRegisteredRoadmap();

      if (checkRegisteredRoadmap != null) {
        if (checkRegisteredRoadmap == 1) {
          final lstRegisteredRoadmap = await _service.getRegisteredRoadmap();

          if (positionStandardNow != null) {
            RegisterRoadmapDto roadMapFirst = lstRegisteredRoadmap.data.first;
            RegisterRoadmapDto registeredRoadmap;

            for (int i = 0; i < lstRegisteredRoadmap.data.length; i++) {
              var item = lstRegisteredRoadmap.data[i];
              if (item.isRegisted) {
                registeredRoadmap = item;
                break;
              }
            }

            PositionStandardDto standardTransient = PositionStandardDto(
              id: roadMapFirst.positionStandardIdTarget,
              positionStandardName: roadMapFirst.positionStandardNameTarget,
            );

            ProcessCareerDto processCareer = ProcessCareerDto();

            var listRoadmap = [];
            var listProcessCareer = [];
            final listStandard = await _service.getListStandard(
              nowId: roadMapFirst.positionStandardIdNow,
              targetId: roadMapFirst.positionStandardIdTarget,
            );

            if (listStandard != null) {
              listRoadmap = listStandard.data ?? [];
              listProcessCareer = listStandard.data ?? [];
            }

            for (int i=0;i<listProcessCareer.length;i++) {
              if (listProcessCareer[i].id==registeredRoadmap.id) {
                processCareer = listProcessCareer[i];
              }
            }

            emit(state.copyWith(
              loadDataStatus: LoadStatus.success,
              positionStandardNow: positionStandardNow,
              listRegisteredRoadmap: lstRegisteredRoadmap.data,
              standardTransient: standardTransient,
              standard: standardTransient,
              processCareer: processCareer,
              registeredRoadmap: registeredRoadmap,
              listRoadmap: listRoadmap,
              listProcessCareer: listProcessCareer,
            ));

          } else {
            emit(state.copyWith(loadDataStatus: LoadStatus.failure));
          }
        } else if (positionStandardNow != null) {
          emit(state.copyWith(
            loadDataStatus: LoadStatus.success,
            positionStandardNow: positionStandardNow,
          ));
        } else {
          emit(state.copyWith(loadDataStatus: LoadStatus.failure));
        }
      }
    } catch (e) {
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }

  Future<void> loadInitialDataFromStandards(ProcessCareerDetailDto processCareerDetailDto) async {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));

    try {

      final positionStandardNow = await _service.getPositionStandardNow();

      if (positionStandardNow != null) {

        PositionStandardDto standardTransient = PositionStandardDto(
          id: processCareerDetailDto.positionStandardId,
          positionStandardName: processCareerDetailDto.positionStandardName,
        );

        var listRoadmap = [];
        var listProcessCareer = [];
        final listStandard = await _service.getListStandard(
          nowId: positionStandardNow.positionStandardNowId,
          targetId: processCareerDetailDto.positionStandardId,
        );

        if (listStandard != null) {
          listRoadmap = listStandard.data ?? [];
          listProcessCareer = listStandard.data ?? [];
        }

        emit(state.copyWith(
          loadDataStatus: LoadStatus.success,
          positionStandardNow: positionStandardNow,
          standardTransient: standardTransient,
          standard: standardTransient,
          listRoadmap: listRoadmap,
          listProcessCareer: listProcessCareer,
        ));

      } else {
        emit(state.copyWith(loadDataStatus: LoadStatus.failure));
      }

    } catch (e) {
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }

  Future<void> getListUnit({String keyword}) async {
    emit(state.copyWith(loadUnitStatus: LoadStatus.loading));
    try {
      if (keyword != null && keyword.isEmpty) {
        keyword = null;
      }

      var page = 0;

      final result = await _service.getListUnit(
          code: keyword, name: keyword, page: state.page, size: state.pageSize);

      if (result == null) {
        emit(state.copyWith(
          loadUnitStatus: LoadStatus.failure,
        ));
      }

      final totalPage = (result.total ?? 0) ~/ state.pageSize;

      emit(state.copyWith(
        loadUnitStatus: LoadStatus.success,
        listUnit: result.data,
        totalPages: totalPage,
        page: page,
      ));
    } catch (ex) {
      emit(state.copyWith(
        loadUnitStatus: LoadStatus.failure,
      ));
    }
  }

  Future<void> fetchNextListUnit({String keyword}) async {
    if (state.page >= state.totalPages) {
      return;
    }

    if (state.loadUnitStatus != LoadStatus.success) {
      return;
    }

    emit(state.copyWith(
      loadUnitStatus: LoadStatus.loadMore,
    ));

    try {
      var page = state.page + 1;
      var size = state.pageSize;

      final result = await _service.getListUnit(
        code: keyword,
        name: keyword,
        page: page,
        size: size,
      );

      if (result == null) {
        emit(state.copyWith(
          loadUnitStatus: LoadStatus.failure,
        ));
      }

      final totalPage = (result.total ?? 0) ~/ state.pageSize;

      emit(state.copyWith(
        loadUnitStatus: LoadStatus.success,
        listUnit: state.listUnit + (result.data ?? []),
        totalPages: totalPage,
        page: page,
        pageSize: size,
      ));
    } catch (e) {
      emit(state.copyWith(
        loadUnitStatus: LoadStatus.failure,
      ));
    }
  }

  Future<void> getListDepartment() async {
    emit(state.copyWith(loadUnitStatus: LoadStatus.loading));
    try {
      final result = await _service.getListDepartment(
        orgParentId: state.unit.organizationId,
      );

      if (result == null) {
        emit(state.copyWith(
          loadUnitStatus: LoadStatus.failure,
        ));
      }

      emit(state.copyWith(
        loadUnitStatus: LoadStatus.success,
        listDepartment: result.data,
      ));
    } catch (ex) {
      emit(state.copyWith(
        loadUnitStatus: LoadStatus.failure,
      ));
    }
  }

  // Future<void> fetchNextListDepartment({String keyword}) async {
  //   if (state.page >= state.totalPages) {
  //     return;
  //   }
  //
  //   if (state.loadUnitStatus != LoadStatus.success) {
  //     return;
  //   }
  //
  //   emit(state.copyWith(
  //     loadUnitStatus: LoadStatus.loadMore,
  //   ));
  //
  //   try {
  //     var page = state.page + 1;
  //     var size = state.pageSize;
  //
  //     final result = await _service.getListDepartment(
  //       orgParentId: state.unit.organizationId,
  //       code: keyword,
  //       name: keyword,
  //       page: page,
  //       size: size,
  //     );
  //
  //     if (result == null) {
  //       emit(state.copyWith(
  //         loadUnitStatus: LoadStatus.failure,
  //       ));
  //     }
  //
  //     final totalPage = (result.total ?? 0) ~/ state.pageSize;
  //
  //     emit(state.copyWith(
  //       loadUnitStatus: LoadStatus.success,
  //       listDepartment: state.listDepartment + (result.data ?? []),
  //       totalPages: totalPage,
  //       page: page,
  //       pageSize: size,
  //     ));
  //
  //   } catch (e) {
  //     emit(state.copyWith(
  //       loadUnitStatus: LoadStatus.failure,
  //     ));
  //   }
  // }

  Future<void> getListTitle(
      {SearchStandardRequest searchStandardRequest}) async {
    emit(state.copyWith(loadUnitStatus: LoadStatus.loading));
    try {
      SearchStandardRequest request;

      if (searchStandardRequest != null) {
        request = searchStandardRequest;
      } else {
        request = SearchStandardRequest(
          size: state.pageSize,
          page: 0,
        );
      }

      final result = await _service.getListTitle(
        request: request,
      );

      if (result == null) {
        emit(state.copyWith(
          loadUnitStatus: LoadStatus.failure,
        ));
      }

      final totalPage = (result.total ?? 0) ~/ request.size;

      emit(state.copyWith(
        loadUnitStatus: LoadStatus.success,
        listStandard: result.data,
        totalPages: totalPage,
        page: 0,
        searchStandardRequest: request,
      ));
    } catch (ex) {
      emit(state.copyWith(
        loadUnitStatus: LoadStatus.failure,
      ));
    }
  }

  Future<void> fetchNextListTitle({String keyword}) async {
    if (state.page >= state.totalPages) {
      return;
    }

    if (state.loadUnitStatus != LoadStatus.success) {
      return;
    }

    emit(state.copyWith(
      loadUnitStatus: LoadStatus.loadMore,
    ));

    try {
      var page = state.page + 1;

      final request = state.searchStandardRequest;

      request.page = page;
      request.positionStandard = keyword;

      final result = await _service.getListTitle(
        request: request,
      );

      if (result != null) {
        final totalPage = (result.total ?? 0) ~/ state.pageSize;

        emit(state.copyWith(
          loadUnitStatus: LoadStatus.success,
          listStandard: state.listStandard + (result.data ?? []),
          totalPages: totalPage,
          page: page,
          searchStandardRequest: request,
        ));
      } else {
        emit(state.copyWith(
          loadUnitStatus: LoadStatus.failure,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        loadUnitStatus: LoadStatus.failure,
      ));
    }
  }

  void selectUnit(Organization unit) {
    var step;
    if (unit.unitBlock != 3) {
      step = StepSelectTitle.step_select_department;
    } else {
      step = StepSelectTitle.step_select_title;
    }
    emit(state.copyWith(
      unit: unit,
      step: step,
    ));
  }

  void selectDepartment(Organization department) {
    emit(state.copyWith(
      department: department,
      step: StepSelectTitle.step_select_title,
    ));
  }

  void selectStandard(PositionStandardDto standard) {
    emit(state.copyWith(
      standard: standard,
    ));
  }

  void setStandardTransient(PositionStandardDto standard) {
    emit(state.copyWith(
      standardTransient: standard,
    ));
  }

  void fetchNextList({String keyword}) {
    if (state.step == StepSelectTitle.step_select_unit) {
      fetchNextListUnit(keyword: keyword);
    }
    // if (state.step==StepSelectTitle.step_select_department) {
    //   fetchNextListDepartment(keyword: keyword);
    // }
    if (state.step == StepSelectTitle.step_select_title) {
      fetchNextListTitle();
    }
  }

  Future<void> getRoadmap({int nowId, int targetId}) async {
    emit(state.copyWith(loadRoadmapStatus: LoadStatus.loading));
    try {
      final result = await _service.getListStandard(
        nowId: nowId,
        targetId: targetId,
      );

      if (result == null) {
        emit(state.copyWith(
          loadRoadmapStatus: LoadStatus.failure,
        ));
      }

      emit(state.copyWith(
          loadRoadmapStatus: LoadStatus.success,
          listRoadmap: result.data ?? [],
          listProcessCareer:
              result.data ?? (state.listProcessCareer ?? result.data)));
    } catch (ex) {
      emit(state.copyWith(
        loadRoadmapStatus: LoadStatus.failure,
      ));
    }
  }

  void clearData() {
    emit(state.copyWith(
      loadUnitStatus: LoadStatus.initial,
      step: StepSelectTitle.step_select_unit,
    ));
  }

  void backStep() {
    if (state.step == StepSelectTitle.step_select_title) {
      if (state.unit.unitBlock != 3) {
        emit(state.copyWith(
          loadUnitStatus: LoadStatus.initial,
          step: StepSelectTitle.step_select_department,
        ));
      } else {
        emit(state.copyWith(
          loadUnitStatus: LoadStatus.initial,
          step: StepSelectTitle.step_select_unit,
        ));
        getListUnit();
      }
    } else if (state.step == StepSelectTitle.step_select_department) {
      emit(state.copyWith(
        loadUnitStatus: LoadStatus.initial,
        step: StepSelectTitle.step_select_unit,
      ));
      getListUnit();
    }
  }

  String createRoadmap(List<ProcessCareerDetailDto> list) {
    String result = '';
    for (int i = 0; i < list.length; i++) {
      if (result.isEmpty) {
        result += list[i].positionStandardName;
      } else {
        result += ' > ' + list[i].positionStandardName;
      }
    }
    return result;
  }

  void setProcessCareer(ProcessCareerDto data) {
    emit(state.copyWith(
      processCareer: data,
    ));
  }

  void setShowButtonRegister(bool value) {
    emit(state.copyWith(
      showButtonRegister: value,
    ));
  }

  void resetLoadRoadmapStatus() {
    emit(state.copyWith(
      loadRoadmapStatus: LoadStatus.initial,
    ));
  }

  Future<String> validateRegister(BuildContext context) async {
    LoadingDialogTransparent.show(context);
    try {
      final result = await _service.validateRegister();
      LoadingDialogTransparent.hide(context);
      return result;
    } catch (ex) {
      LoadingDialogTransparent.hide(context);
     return null;
    }
  }

  Future<void> registerRoadmap(BuildContext context) async {
    emit(state.copyWith(
      confirmStatus: LoadStatus.loading,
    ));
    LoadingDialogTransparent.show(context);
    try {
      RegisterRoadmapRequest request = RegisterRoadmapRequest(
        positionStandardIdNow: state.positionStandardNow.positionStandardNowId,
        positionStandardIdTarget: state.standard.id,
        processCareerId: state.processCareer.id,
        details: state.processCareer.details,
      );

      UpdateResult updateResult = await _service.registerRoadmap(request);

      if (updateResult == null || updateResult.status != 1) {
        emit(state.copyWith(
          confirmStatus: LoadStatus.failure,
          message: updateResult.message,
        ));
      }

      LoadingDialogTransparent.hide(context);
      emit(state.copyWith(
        confirmStatus: LoadStatus.success,
      ));
    } catch (ex) {
      print(ex);
      LoadingDialogTransparent.hide(context);
      emit(state.copyWith(
          confirmStatus: LoadStatus.failure, message: 'Lỗi xử lý ứng dụng'));
    }
  }

  void setUnit() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    int unitId = sharedPreferences.getInt(AppConstant.KEY_UNIT_ID_VCC);
    Organization item = await getUnit(unitId: unitId);
    selectUnit(item);
    if (item.unitBlock != 3) {
      getListDepartment();
    } else {
      getListTitle(
        searchStandardRequest: SearchStandardRequest(
          page: 0,
          size: 10,
          unitId: -1,
          unitBlock: item.unitBlock,
        ),
      );
    }
  }

  Future<Organization> getUnit({int unitId}) async {
    emit(state.copyWith(loadGetUnitUserStatus: LoadStatus.loading));
    try {
      final result = await _service.getListUnit(id: unitId, page: 0, size: 1);

      if (result?.data == null) {
        emit(state.copyWith(
          loadGetUnitUserStatus: LoadStatus.failure,
        ));
      }

      if (result.data.length > 0) {
        emit(state.copyWith(
          loadGetUnitUserStatus: LoadStatus.success,
        ));
        return result.data.first;
      } else {
        emit(state.copyWith(
          loadGetUnitUserStatus: LoadStatus.failure,
        ));
        return null;
      }
    } catch (ex) {
      emit(state.copyWith(
        loadGetUnitUserStatus: LoadStatus.failure,
      ));
      return null;
    }
  }
}
