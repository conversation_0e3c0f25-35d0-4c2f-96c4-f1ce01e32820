import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/common/app_vectors.dart';

class CustomCheckbox extends StatelessWidget {
  const CustomCheckbox({
    Key key,
    this.value = false,
    this.hintText,
    this.onChanged,
    this.enabled = true,
    this.required = false,
    this.validator,
  }) : super(key: key);

  final bool value;
  final String hintText;
  final ValueChanged<bool> onChanged;
  final bool enabled;
  final bool required;
  final String Function(bool value) validator;

  @override
  Widget build(BuildContext context) {
    return FormField<bool>(
      validator: validator ??
          (required
              ? (value) {
                  return !value ? 'Vui lòng chọn $hintText' : null;
                }
              : null),
      autovalidateMode: AutovalidateMode.onUserInteraction,
      enabled: enabled,
      initialValue: value,
      builder: (field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CupertinoButton(
                  minSize: 0,
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    field.didChange(!field.value);
                    onChanged?.call(field.value);
                  },
                  child: AnimatedCrossFade(
                    firstChild: SvgPicture.asset(AppVectors.icContainer),
                    secondChild: SvgPicture.asset(AppVectors.icContainerActive),
                    crossFadeState: field?.value ?? value ? CrossFadeState.showSecond : CrossFadeState.showFirst,
                    duration: const Duration(milliseconds: 100),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 3),
                    child: Text(
                      hintText,
                      style: TextStyle(fontSize: 16, color: Color(0xff040404)),
                    ),
                  ),
                ),
              ],
            ),
            if (field.hasError) SizedBox(height: 8),
            if (field.hasError)
              Text(
                field.errorText,
                style: TextStyle(color: Color(0xffe81c2d), fontSize: 12),
              ),
          ],
        );
      },
    );
  }
}
