import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/screen/dependant/cancel_registration/cancel_registration_page.dart';
import 'package:hstd/screen/dependant/dependant_list/dependant_list_controller.dart';
import 'package:hstd/screen/dependant/dependant_registration/dependant_registration_controller.dart';
import 'package:hstd/screen/dependant/dependant_registration/dependant_registration_request.dart';
import 'package:hstd/screen/dependant/otp/otp_page.dart';
import 'package:hstd/screen/dependant/otp/otp_request.dart';
import 'package:hstd/screen/dependant/sign.dart';
import 'package:hstd/screen/dependant/sign_file/get_sign_file_controller.dart';
import 'package:hstd/screen/dependant/sign_file/sign_file_controller.dart';
import 'package:hstd/screen/dependant/sign_file/sign_file_request.dart';
import 'package:hstd/screen/dependant/sign_file/sign_file_service.dart';
import 'package:hstd/screen/income_tax/register_income_tax/widgets/custom_widget_income.dart';
import 'package:hstd/widget/dialog_auto_close.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class SignFilePage extends StatefulWidget {
  const SignFilePage({
    Key key,
    this.request,
    this.fromScreen
  }) : super(key: key);

  final GetSignFileRequest request;
  final FromScreen fromScreen;

  @override
  _SignFilePage createState() => _SignFilePage();
}

class _SignFilePage extends State<SignFilePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Ký điện tử'),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 1,
      ),
      body: MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (_) => GetSignFileController()..getFile(widget.request),
          ),
          ChangeNotifierProvider(
            create: (_) => SignFileController(),
          ),
          ChangeNotifierProvider(
            create: (_) => DependantRegistrationController(),
          ),
        ],
        child: _Body(request: widget.request, fromScreen: widget.fromScreen),
      ),
    );
  }
}

class _Body extends StatefulWidget {
  const _Body({Key key, this.request, this.fromScreen}) : super(key: key);

  final GetSignFileRequest request;
  final FromScreen fromScreen;

  @override
  State<_Body> createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  final _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    context.read<SignFileController>().listen(
      onLoading: (isLoading) {
        if (isLoading) {
          LoadingDialogTransparent.show(context);
        } else {
          LoadingDialogTransparent.hide(context);
        }
      },
      onError: (error) {
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: error?.toString()?.replaceAll('Exception: ', ''),
          ),
        );
      },
    );

    context.read<DependantRegistrationController>().listen(
      onLoading: (isLoading) {
        if (isLoading) {
          LoadingDialogTransparent.show(context);
        } else {
          LoadingDialogTransparent.hide(context);
        }
      },
      onError: (error) {
        Navigator.pop(context);
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: error?.toString()?.replaceAll('Exception: ', ''),
          ),
        );
      },
      onSuccess: (data) {
        context.read<DependantListController>().getDependantList();

        Navigator.pop(context);
        Navigator.pop(context);
        Navigator.pop(context);
        if (widget.fromScreen == FromScreen.dependantDetails) {
          Navigator.pop(context);
        }
        final dependantRegistrationRequest = context.read<DependantRegistrationRequest>();
        if (dependantRegistrationRequest.dependentRegisterId != null) {
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: 'Sửa đăng ký thành công',
              showIcon: true,
            ),
          );
        } else {
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: 'Đăng ký thành công',
              showIcon: true,
            ),
          );
        }

      },
    );

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final controller = context.watch<GetSignFileController>();

    if (controller.isLoading) {
      return LoadingDialog();
    } else if (controller.hasError) {
      return EmptyListWidget(
        onRefresh: () => controller.getFile(widget.request),
        content: controller.failure.toString(),
      );
    } else if (controller.success != null) {
      final datas = controller.success;

      if (datas.isEmpty) {
        return EmptyListWidget(
          onRefresh: () => controller.getFile(widget.request),
          content: 'Không có dữ liệu',
        );
      }

      return Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            itemCount: datas.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemBuilder: (context, index) {
              return AspectRatio(
                aspectRatio: 8.5 / 11,
                child: SfPdfViewer.memory(
                  datas[_currentPage],
                  key: Key('pdf_$index'),
                ),
              );
            },
          ),
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: SafeArea(
              child: Container(
                height: 64,
                padding: EdgeInsets.symmetric(horizontal: 16),
                alignment: Alignment.center,
                child: CupertinoButton(
                  onPressed: () async {
                    if (_currentPage + 1 < datas.length) {
                      _pageController.nextPage(
                        duration: Duration(milliseconds: 50),
                        curve: Curves.easeInOut,
                      );
                    } else {
                      await _sign(context);
                    }
                  },
                  padding: EdgeInsets.zero,
                  minSize: 0,
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      color: Color(0xffe81c2d),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0xff000000).withOpacity(0.2),
                          blurRadius: 10,
                          offset: Offset(2, 4),
                        ),
                      ],
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      _currentPage + 1 < datas.length ? 'Tiếp tục' : (_currentPage + 1 > 1 ? 'Ký cả 2' : 'Ký'),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      return SizedBox.shrink();
    }
  }

  Future<void> _sign(BuildContext context) async {
    showDialog<Uint8List>(
      context: context,
      builder: (BuildContext context) {
        return SignatureInput();
      },
    ).then((signValue) async {
      if (signValue != null) {
        // Create a temporary file from Uint8List
        final tempDir = await getTemporaryDirectory();
        final tempFile = File(path.join(tempDir.path, 'signature.png'));
        await tempFile.writeAsBytes(signValue);

        final dependantRegistrationRequest = context.read<DependantRegistrationRequest>();
        dependantRegistrationRequest.imageSign = tempFile;
        dependantRegistrationRequest.exportFileList =
            SignFileService.getExportFileList(widget.request.relationTypeName, widget.request.regTypeCode).map((e) {
          List<String> arr = e.split('/');
          return arr[arr.length - 1];
        }).toList();

        // Call generate OTP api
        await context.read<SignFileController>().signOTP();

        final data = context.read<SignFileController>().success;

        SharedPreferences sharedPreferences = await SharedPreferences.getInstance();

        String phoneNumber = sharedPreferences.getString(AppConstant.KEY_PHONE_NUMBER_VCC);

        Navigator.push(
          context,
          OtpPage.route(
            arguments: OtpArguments(
              otpRequest: OtpRequest(
                phoneNumber: phoneNumber,
                otpId: data.otpId,
                duration: data.duration,
              ),
              onCall: _submit,
            ),
          ),
        );
      }
    });

  }

  Future<void> _submit({OtpRequest request}) async {
    log(request.toString());

    // Submit the form
    final dependantRegistrationRequest = context.read<DependantRegistrationRequest>();

    dependantRegistrationRequest.otpId = request.otpId;
    dependantRegistrationRequest.otpCode = request.otpCode;
    dependantRegistrationRequest.isSmartOtp = request.isSmartOtp;

    context.read<DependantRegistrationController>().registerDependant(
          request: dependantRegistrationRequest,
        );
  }
}
