import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hstd/extension/date_time.dart';
import 'package:hstd/widget/custom_dialog_date_picker.dart';

import '../../common/app_colors.dart';

class CustomDateTimePicker extends StatefulWidget {
  const CustomDateTimePicker({
    Key key,
    this.initialValue,
    this.minTime,
    this.maxTime,
    this.enabled = true,
    this.required = false,
    this.format = ONLY_DATE,
    this.onSelectDate,
    this.hintText,
    this.validator,
    this.isDisplayDate = true,
    this.isDisplayMonth = true,
    this.isDisplayYear = true,
  }) : super(key: key);

  final DateTime initialValue;
  final bool enabled;
  final bool required;
  final DateTime minTime;
  final DateTime maxTime;
  final String format;
  final ValueChanged<DateTime> onSelectDate;
  final String hintText;
  final FormFieldValidator<DateTime> validator;
  final bool isDisplayDate;
  final bool isDisplayMonth;
  final bool isDisplayYear;

  @override
  State<CustomDateTimePicker> createState() => _CustomDateTimePickerState();
}

class _CustomDateTimePickerState extends State<CustomDateTimePicker> {

  DateTime _value;

  @override
  void initState() {
    _value = widget?.initialValue;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FormField<DateTime>(
      initialValue: _value,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: widget.validator,
      builder: (field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: widget.enabled
                  ? () {
                      DatePicker.showPicker(
                        context,
                        showTitleActions: true,
                        onConfirm: (date) async {
                          date = DateTime(date.year, widget.isDisplayMonth ? date.month : 1, widget.isDisplayDate ? date.day : 1);
                          field.didChange(date);
                          widget.onSelectDate?.call(date);
                        },
                        // onChanged: (date) {
                        //   field.didChange(date);
                        // },
                        pickerModel: CustomDatePicker(
                          minTime: widget.minTime,
                          maxTime: widget.maxTime,
                          currentTime: field.value ?? _value,
                          locale: LocaleType.vi,
                          isDisplayDay: widget.isDisplayDate,
                          isDisplayMonth: widget.isDisplayMonth,
                          isDisplayYear: widget.isDisplayYear,
                        ),
                        locale: LocaleType.vi,
                      );
                    }
                  : null,
              child: Container(
                height: 58,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: !field.hasError ? Color(0xffd4d4d4) : Color(0xffe81c2d),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.fromLTRB(14, 10, 0, 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      field.value != null
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text.rich(
                                  TextSpan(
                                    children: [
                                      TextSpan(text: widget.hintText),
                                      if (widget.required) ...[
                                        TextSpan(text: ' *', style: TextStyle(color: Colors.red)),
                                      ]
                                    ],
                                  ),
                                  style: TextStyle(
                                    color: AppColors.greyTextTitle,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                SizedBox(height: 2),
                                Text(
                                  field.value?.getFormat(widget.format) ?? '',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Color(0xff040404),
                                  ),
                                ),
                              ],
                            )
                          : Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(text: widget.hintText),
                                  if (widget.required) ...[
                                    TextSpan(text: ' *', style: TextStyle(color: Colors.red)),
                                  ]
                                ],
                              ),
                              style: TextStyle(
                                color: Color(0xff525252),
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                      field.value != null && !widget.required
                          ? CupertinoButton(
                              minSize: 44,
                              padding: EdgeInsets.zero,
                              onPressed: () {
                                field.didChange(null);
                                widget.onSelectDate?.call(null);
                              },
                              child: Icon(
                                Icons.close_rounded,
                                color: Color(0xff7f7f7f),
                                size: 20,
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.only(right: 16),
                              child: SvgPicture.asset(
                                'assets/calendar.svg',
                                color: Color(0xff7f7f7f),
                              ),
                            )
                    ],
                  ),
                ),
              ),
            ),
            if (field.hasError) const SizedBox(height: 8),
            if (field.hasError)
              Padding(
                padding: const EdgeInsets.only(left: 12),
                child: Text(
                  field.errorText ?? '',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xffe81c2d),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
