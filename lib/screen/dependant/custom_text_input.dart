import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/screen/dependant/text_input_border.dart';

class CustomTextInput extends StatefulWidget {
  const CustomTextInput({
    Key key,
    this.controller,
    this.onTap,
    this.value,
    this.hintText,
    this.keyboardType,
    this.obscureText,
    this.enabled = true,
    this.readOnly = false,
    this.required = false,
    this.maxLines,
    this.inputFormatters,
    this.onChanged,
    this.validator,
  }) : super(key: key);

  @override
  State<CustomTextInput> createState() => _CustomTextInputState();

  final TextEditingController controller;
  final VoidCallback onTap;
  final String value;
  final String hintText;
  final TextInputType keyboardType;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final bool required;
  final int maxLines;
  final List<TextInputFormatter> inputFormatters;
  final ValueChanged<String> onChanged;
  final String Function(String) validator;
}

class _CustomTextInputState extends State<CustomTextInput> {
  TextEditingController _controller;

  @override
  void initState() {
    _controller = widget.controller ?? TextEditingController();
    _controller.text = widget.value;
    super.initState();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: TextFormField(
        enabled: !widget.readOnly,
        // onTap: widget.onTap,
        controller: _controller,
        cursorColor: Color(0xff2a2a2a),
        autovalidateMode: AutovalidateMode.onUserInteraction,
        inputFormatters: widget.inputFormatters,
        style: AppTextStyle.blackTextS16NormalIncome,
        maxLines: widget.maxLines,
        validator: widget.validator,
        keyboardType: widget.keyboardType,
        decoration: InputDecoration(
          contentPadding: EdgeInsets.fromLTRB(14, 10, 20.0, 10),
          counterText: "",
          alignLabelWithHint: widget.maxLines != null && widget.maxLines > 1,
          label: Text.rich(
            TextSpan(
              children: [
                TextSpan(text: widget.hintText),
                if (widget.required) ...[
                  TextSpan(text: ' *', style: TextStyle(color: Colors.red)),
                ]
              ],
            ),
          ),
          labelStyle: AppTextStyle.greyTextFieldLableS16W400,
          errorStyle: TextStyle(
            color: Color(0xffe81c2d),
            fontSize: 12,
          ),
          enabledBorder: OutlinedInputBorder(
            borderSide: BorderSide(
              color: Color(0xffd4d4d4),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          focusedBorder: OutlinedInputBorder(
            borderSide: BorderSide(
              color: Color(0xff2a2a2a),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          errorBorder: OutlinedInputBorder(
            borderSide: BorderSide(
              color: Color(0xffe81c2d),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          border: OutlinedInputBorder(
            borderSide: BorderSide(
              color: Color(0xffd4d4d4),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}
