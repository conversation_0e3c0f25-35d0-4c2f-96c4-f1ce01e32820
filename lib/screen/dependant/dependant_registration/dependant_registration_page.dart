import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hstd/configs/app_configs.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/extension/date_time.dart';
import 'package:hstd/models/common_dto.dart';
import 'package:hstd/models/ethnic_dto.dart';
import 'package:hstd/models/gifts/current_address_info.dart';
import 'package:hstd/models/gifts/gift_person_reg.dart';
import 'package:hstd/network/api_request.dart';
import 'package:hstd/repositories/ethnic_service.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/screen/dependant/cancel_registration/cancel_registration_page.dart';
import 'package:hstd/screen/dependant/custom_checkbox.dart';
import 'package:hstd/screen/dependant/custom_date_time_picker.dart';
import 'package:hstd/screen/dependant/custom_dropdown.dart';
import 'package:hstd/screen/dependant/custom_file_picker.dart';
import 'package:hstd/screen/dependant/custom_images_picker.dart';
import 'package:hstd/screen/dependant/custom_multi_file_picker.dart';
import 'package:hstd/screen/dependant/custom_text_input.dart';
import 'package:hstd/screen/dependant/dependant_details/dependant_details_controller.dart';
import 'package:hstd/screen/dependant/dependant_details/dependant_details_entity.dart';
import 'package:hstd/screen/dependant/dependant_list/dependant_list_controller.dart';
import 'package:hstd/screen/dependant/dependant_registration/dependant_registration_controller.dart';
import 'package:hstd/screen/dependant/dependant_registration/dependant_registration_otp_controller.dart';
import 'package:hstd/screen/dependant/dependant_registration/dependant_registration_request.dart';
import 'package:hstd/screen/dependant/dependant_registration/dependant_registration_service.dart';
import 'package:hstd/screen/dependant/id_picker.dart';
import 'package:hstd/screen/dependant/models/dependant_select.dart';
import 'package:hstd/screen/dependant/models/dependant_situation.dart';
import 'package:hstd/screen/dependant/models/disability_level.dart';
import 'package:hstd/screen/dependant/models/file_response.dart';
import 'package:hstd/screen/dependant/models/member_of.dart';
import 'package:hstd/screen/dependant/otp/otp_page.dart';
import 'package:hstd/screen/dependant/otp/otp_request.dart';
import 'package:hstd/screen/dependant/sign_file/sign_file_page.dart';
import 'package:hstd/screen/dependant/sign_file/sign_file_request.dart';
import 'package:hstd/screen/gifts/address_registration/address_registration_page.dart';
import 'package:hstd/widget/dialog_auto_close.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';

import '../../../widget/dropdown/dropdown_search.dart';

class DependantRegistrationPage extends StatelessWidget {
  const DependantRegistrationPage({Key key, this.id, this.fromScreen}) : super(key: key);

  final int id;
  final FromScreen fromScreen;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      extendBody: true,
      appBar: AppBar(
        title: Text(
          '${id == null ? 'Đăng' : 'Sửa đăng'} ký người phụ thuộc',
          style: TextStyle(
            color: Colors.black,
          ),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(0),
          child: Divider(
            height: 1,
            thickness: 1,
            color: Color(0xffe1e3e5),
          ),
        ),
        elevation: 0,
      ),
      body: MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (_) => DependantDetailsController(id)..getDependantDetails(),
          ),
          ChangeNotifierProvider(
            create: (_) => DependantRegistrationController(),
          ),
          ChangeNotifierProvider(
            create: (_) => DependantRegistrationOtpController(),
          ),
        ],
        child: _Body(id: id, fromScreen: fromScreen),
      ),
    );
  }
}

class _Body extends StatefulWidget {
  const _Body({Key key, this.id, this.fromScreen}) : super(key: key);
  final int id;
  final FromScreen fromScreen;

  @override
  State<_Body> createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  @override
  void initState() {
    if (widget.id != null) {
      context.read<DependantDetailsController>().listen(
        onError: (error) {
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: error?.toString()?.replaceAll('Exception: ', ''),
            ),
          );
        },
        onSuccess: (value) {
          _setValue(value);
        },
      );
    }

    context.read<DependantRegistrationOtpController>().listen(
      onLoading: (isLoading) {
        if (isLoading) {
          LoadingDialogTransparent.show(context);
        } else {
          LoadingDialogTransparent.hide(context);
        }
      },
      onError: (error) {
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: error?.toString()?.replaceAll('Exception: ', ''),
          ),
        );
      },
    );

    context.read<DependantRegistrationController>().listen(
      onLoading: (isLoading) {
        if (isLoading) {
          LoadingDialogTransparent.show(context);
        } else {
          LoadingDialogTransparent.hide(context);
        }
      },
      onError: (error) {
        Navigator.pop(context);
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: error?.toString()?.replaceAll('Exception: ', ''),
          ),
        );
      },
      onSuccess: (data) {
        context.read<DependantListController>().getDependantList();

        Navigator.pop(context);
        Navigator.pop(context);
        if (widget.fromScreen == FromScreen.dependantDetails) {
          Navigator.pop(context);
        }
        if (_dependentRequest.dependentRegisterId != null) {
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: 'Sửa đăng ký thành công',
              showIcon: true,
            ),
          );
        } else {
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: 'Đăng ký thành công',
              showIcon: true,
            ),
          );
        }
      },
    );

    super.initState();
  }

  final _formKey = GlobalKey<FormState>();

  final _bookNoFieldKey = UniqueKey();
  final _addressFieldKey = UniqueKey();
  final _dependantSituationKey = UniqueKey();

  var _hasId = false;

  CurrentAddressInfo _addressInfo;
  DependantSelectItem _selectedDependant;
  DependantRegistrationRequest _dependentRequest;

  DateTime _dob;
  int _age = 0;

  final _idController = TextEditingController();
  final _taxNumberController = TextEditingController();
  final _codeNoController = TextEditingController();
  final _bookNoController = TextEditingController();
  final _addressController = TextEditingController();

  var _ethnic = Ethnic(code: '1', ethnicId: 113, name: 'Kinh');
  MemberOf _memberOf;
  DependantSituation _dependantSituation;

  DateTime _yearInjured;
  DisabilityLevel _disabitlityLevel;
  final _tyLeThuongTatController = TextEditingController();

  DateTime _yearOfDead;

  var _hasGKS = false;
  File _fileBirthCertificate;
  FileEntity _initFileBirthCertificate;

  var _hasCCCD = false;
  List<File> _fileIdentityCards = List<File>.generate(2, (index) => null);

  var _hasTheHSSV = false;
  List<File> _fileStudentCards = List<File>.generate(2, (index) => null);

  var _hasDKKH = false;
  File _fileMarriageCertificate;
  FileEntity _initFileMarriageCertificate;

  var _hasSHK = false;
  List<File> _fileHouseholdRegistrationBooks;
  List<FileEntity> _initFileHouseholdRegistrationBooks;

  var _hasQDND = false;
  File _fileWardConfirm;
  FileEntity _initFileWardConfirm;

  var _hasGTK = false;
  List<File> _attachList = [];
  List<int> _ids = [];

  DateTime _fromTime;
  DateTime _toTime;

  DateTime _studentToTime;
  DateTime _studentFromTime;

  final _noteController = TextEditingController();

  @override
  void dispose() {
    _idController.dispose();
    _addressController.dispose();
    _codeNoController.dispose();
    _bookNoController.dispose();
    _taxNumberController.dispose();
    _tyLeThuongTatController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DependantDetailsController>(builder: (context, value, child) {
      if (widget.id != null && value.isLoading) {
        return LoadingDialog();
      } else if (widget.id != null && value.hasError) {
        return EmptyListWidget(
          onRefresh: context.read<DependantDetailsController>().getDependantDetails,
          content: 'Đã có lỗi xảy ra. Vui lòng thử lại.',
        );
      } else if (widget.id == null || value.success != null) {
        return GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: Stack(
            children: [
              SingleChildScrollView(
                padding: EdgeInsets.fromLTRB(16, 16, 16, 120),
                child: Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: Column(
                    children: [
                      // Has ID checkbox
                      CustomCheckbox(
                        key: ValueKey('hasId${_hasId ? 1 : 0}'),
                        value: _hasId,
                        hintText: 'Người phụ thuộc đã có MST/CCCD/Hộ chiếu/Số định danh cá nhân',
                        onChanged: (value) {
                          setState(() {
                            _hasId = value;
                            if (!_hasId) _addressController.clear();
                          });
                        },
                      ),
                      SizedBox(height: 16),

                      // Name
                      CustomDropdown<DependantSelectItem>(
                        key: ValueKey('dependantSelectItem${_selectedDependant?.familyRelationshipId ?? ''}'),
                        hintText: 'Họ và tên',
                        mode: Mode.BOTTOM_SHEET,
                        selectedItem: _selectedDependant,
                        getDataFn: _getDependantSelectItems,
                        required: true,
                        itemAsString: (item) {
                          return '${item.relationTypeName} - ${item.fullName}';
                        },
                        onChanged: _onSelectDependant,
                      ),
                      SizedBox(height: 16),

                      CustomDateTimePicker(
                        key: ValueKey('_dobFieldKey${_selectedDependant?.familyRelationshipId ?? ''}'),
                        initialValue: _dob,
                        required: true,
                        hintText: 'Ngày sinh',
                        format: 'dd/MM/yyyy',
                        onSelectDate: (date) {
                          setState(() {
                            _dob = date;

                            if (_dob != null) {
                              _age = DateTime.now().difference(_dob).inDays ~/ 365;
                            } else {
                              _age = 0;
                            }
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Vui lòng chọn ngày sinh';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16),

                      if (_hasId)
                        CustomTextInput(
                          key: ValueKey('_idFieldKey${_selectedDependant?.familyRelationshipId ?? ''}'),
                          controller: _idController,
                          required: _taxNumberController.text.isEmpty,
                          hintText: 'Số CMT/CCCD/Định danh NPT',
                          inputFormatters: [LengthLimitingTextInputFormatter(12)],
                          // keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value.isEmpty && _taxNumberController.text.isEmpty) {
                              return 'Vui lòng nhập Số CMT/CCCD/Định danh NPT';
                            }

                            if (value.isNotEmpty && value.length > 13) {
                              return 'Số CMT/CCCD/Định danh NPT tối đa 12 ký tự';
                            }

                            return null;
                          },
                          onChanged: (value) {
                            setState(() {
                              _idController.text = value;
                            });
                          },
                        ),
                      if (_hasId) SizedBox(height: 16),

                      if (_hasId)
                        CustomTextInput(
                          key: ValueKey('_taxCodeFieldKey${_selectedDependant?.familyRelationshipId ?? ''}'),
                          controller: _taxNumberController,
                          required: _idController.text.isEmpty,
                          hintText: 'MST thu nhập cá nhân',
                          keyboardType: TextInputType.number,
                          inputFormatters: [LengthLimitingTextInputFormatter(10)],
                          validator: (value) {
                            if (value.isEmpty && _idController.text.isEmpty) {
                              return 'Vui lòng nhập MST thu nhập cá nhân';
                            }

                            if (value.isNotEmpty) {
                              if (int.tryParse(value) == null) {
                                return 'MST phải là số';
                              }
                              if (value.length < 10) {
                                return 'MST phải có 10 ký tự';
                              }
                            }

                            return null;
                          },
                          onChanged: (value) {
                            setState(() {
                              _taxNumberController.text = value;
                            });
                          },
                        ),
                      if (_hasId) SizedBox(height: 16),

                      // if (!_hasId ?? false)
                      //   TextInput(
                      //     key: _codeNoFieldKey,
                      //     controller: _codeNoController,
                      //     required: true,
                      //     hintText: 'Số giấy khai sinh',
                      //   ),
                      // if (!_hasId) SizedBox(height: 16),

                      // if (!_hasId ?? false)
                      //   TextInput(
                      //     key: _bookNoFieldKey,
                      //     controller: _bookNoController,
                      //     required: true,
                      //     hintText: 'Quyển số',
                      //   ),
                      // if (!_hasId) SizedBox(height: 16),

                      // NATIONALITY
                      CustomTextInput(
                        hintText: 'Quốc tịch',
                        value: 'Việt Nam',
                        enabled: false,
                        keyboardType: TextInputType.text,
                      ),
                      SizedBox(height: 16),

                      CustomDropdown<Ethnic>(
                        hintText: 'Dân tộc',
                        getDataFn: _getEthnicity,
                        compareFn: (item, value) => value != null ? item.code == value.code : false,
                        selectedItem: _ethnic,
                        itemAsString: (item) => item.name,
                        required: true,
                        onChanged: (item) {
                          setState(() {
                            _ethnic = item;
                          });
                        },
                      ),
                      SizedBox(height: 16),

                      if (!_hasId ?? false)
                        CustomTextInput(
                          key: _addressFieldKey,
                          controller: _addressController,
                          required: true,
                          hintText: 'Địa chỉ',
                          readOnly: true,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Vui lòng chọn địa chỉ';
                            }
                            return null;
                          },
                          onTap: () async {
                            final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AddressRegistrationPage(
                                  arguments: AddressRegistrationArguments(item: GiftPersonReg()),
                                ),
                              ),
                            );
                            setState(() {
                              if (result != null && result is CurrentAddressInfo) {
                                _addressInfo = result;
                                _addressController.text = result.fullAddress;
                              } else {
                                _addressInfo = CurrentAddressInfo();
                                _addressController.text = null;
                              }
                            });
                          },
                        ),
                      if (!_hasId ?? false) SizedBox(height: 16),

                      // Member of
                      if (_hasId ?? false)
                      CustomDropdown<MemberOf>(
                        key: ValueKey('memberOf${_memberOf?.value ?? ''}'),
                        hintText: 'Thành viên của',
                        required: false,
                        selectedItem: _memberOf,
                        getDataFn: () async => [
                          MemberOf('KHONG', 'Không'),
                          MemberOf('DOAN_VIEN', 'Đoàn viên'),
                          MemberOf('DANG_VIEN', 'Đảng viên'),
                        ],
                        itemAsString: (item) => item.name,
                        onChanged: (item) {
                          setState(() => _memberOf = item);
                        },
                        onClear: () {
                          setState(() => _memberOf = null);
                        },
                      ),
                      if (_hasId ?? false)
                      SizedBox(height: 16),

                      CustomDropdown<DependantSituation>(
                        key: _dependantSituationKey,
                        hintText: 'Tình trạng',
                        getDataFn: _getDependantType,
                        mode: Mode.BOTTOM_SHEET,
                        selectedItem: _dependantSituation,
                        itemAsString: (item) => item.name,
                        onChanged: (item) {
                          log(item.toString());
                          setState(() {
                            _dependantSituation = item;
                          });
                        },
                      ),
                      SizedBox(height: 16),

                      /*
                  • NLĐ chọn Trạng thái = Thương bệnh binh thì hiển thị nhập các trường thông tin sau
                  + Năm bị thương/Năm bị nhiễm
                  + Hạng thương tật
                  + Tỷ lệ thương tật
                  */
                      if (_dependantSituation?.isThuongBinh ?? false)
                        Column(
                          children: [
                            CustomDateTimePicker(
                              hintText: 'Năm bị thương/Năm bị nhiễm',
                              format: ONLY_YEAR,
                              initialValue: _yearInjured,
                              isDisplayDate: false,
                              isDisplayMonth: false,
                              onSelectDate: (date) {
                                _yearInjured = date;
                              },
                            ),
                            SizedBox(height: 16),
                            CustomDropdown<DisabilityLevel>(
                              hintText: 'Hạng thương tật',
                              selectedItem: _disabitlityLevel,
                              getDataFn: _getHangThuongTat,
                              itemAsString: (item) => item.name,
                              required: true,
                              // keyboardType: TextInputType.number,
                              onChanged: (value) {
                                _disabitlityLevel = value;
                              },
                            ),
                            SizedBox(height: 16),
                            CustomTextInput(
                              hintText: 'Tỷ lệ thương tật',
                              controller: _tyLeThuongTatController,
                              required: true,
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value.isEmpty) {
                                  return 'Vui lòng nhập tỷ lệ thương tật';
                                } else if (int.tryParse(value) == null) {
                                  return 'Tỷ lệ thương tật phải là số';
                                } else if (int.tryParse(value) < 0 || int.tryParse(value) > 100) {
                                  return 'Tỷ lệ thương tật phải từ 1 đến 100';
                                }
                                return null;
                              },
                              onChanged: (value) {},
                            ),
                            SizedBox(height: 16),
                          ],
                        ),

                      /*
                  • NLĐ chọn Trạng thái = Đã mất thì hiển thị nhập các trường thông tin sau:
                  + Năm/ Ghi chú
                  */
                      if (_dependantSituation?.isDaMat ?? false)
                        Column(
                          children: [
                            CustomDateTimePicker(
                              hintText: 'Năm mất',
                              format: ONLY_YEAR,
                              initialValue: _yearOfDead,
                              isDisplayDate: false,
                              isDisplayMonth: false,
                              onSelectDate: (date) {
                                _yearOfDead = date;
                              },
                            ),
                            SizedBox(height: 16),
                          ],
                        ),
                      if ((_selectedDependant?.isConOrChauOrEm ?? false) && _age >= 14
                          && ((_dependantSituation?.isDangDiHoc ?? false) || _age >= 18))
                        Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  'Thông tin thẻ HSSV',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff040404),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 16),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: CustomDateTimePicker(
                                    hintText: 'Từ ngày',
                                    initialValue: _studentFromTime,
                                    required: true,
                                    validator: (value) {
                                      if (value == null) {
                                        return 'Vui lòng chọn từ ngày';
                                      }
                                      return null;
                                    },
                                    onSelectDate: (date) {
                                      setState(() {
                                        _studentFromTime = date;
                                      });
                                    },
                                  ),
                                ),
                                SizedBox(width: 16),
                                  Expanded(
                                    child: CustomDateTimePicker(
                                      hintText: 'Đến ngày',
                                      initialValue: _studentToTime,
                                      required: true,
                                      onSelectDate: (date) {
                                        setState(() {
                                          _studentToTime = date;
                                        });
                                      },
                                      validator: (value) {
                                        if (value == null) {
                                          return 'Vui lòng chọn đến ngày';
                                        }
                                        if (value != null && _studentFromTime != null && value.isBefore(_studentFromTime)) {
                                          return 'Đến ngày phải lớn hơn hoặc bằng từ ngày';
                                        }

                                        return null;
                                      },
                                    ),
                                  ),
                              ],
                            ),
                            SizedBox(height: 16)
                          ],
                        ),
                      /*
                  • NLĐ chọn mối quan hệ = Con đẻ/Con nuôi và Trạng thái = Còn nhỏ thì hiển thị nhập các trường thông tin sau:
                  + Số giấy khai sinh/  Số sổ giấy khai sinh
                  */
                      if (((_dependantSituation?.isDangDiHoc ?? false) || (_dependantSituation?.isConNho ?? false)) && !_hasId)
                        Column(
                          children: [
                            CustomTextInput(
                              hintText: 'Số giấy khai sinh',
                              required: true,
                              controller: _codeNoController,
                              validator: (value) {
                                if (value.isEmpty) {
                                  return 'Vui lòng nhập số giấy khai sinh';
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),
                            CustomTextInput(
                              key: _bookNoFieldKey,
                              controller: _bookNoController,
                              required: true,
                              hintText: 'Số sổ giấy khai sinh',
                              validator: (value) {
                                if (value.isEmpty) {
                                  return 'Vui lòng nhập số sổ giấy khai sinh';
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),
                          ],
                        ),
                      SizedBox(height: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                'Hồ sơ đính kèm',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xff040404),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(width: 4),
                              Text(
                                '*',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.red,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),

                          // GKS
                          /*NPT là con ruột/ con nuôi*/
                          if ((_selectedDependant?.isConOrChauOrEm ?? false) ||
                              (_selectedDependant?.isBM ?? false) ||
                              (_selectedDependant?.isBMVC ?? false))
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomCheckbox(
                                  key: ValueKey('_hasGKSField${_hasGKS ? 1 : 0}'),
                                  value: _hasGKS,
                                  required: true,
                                  hintText: 'Giấy khai sinh' + _selectedDependant.titleFileBirthCertificate,
                                  onChanged: (value) {
                                    setState(() {
                                      _hasGKS = value;
                                    });
                                  },
                                ),
                                SizedBox(height: 12),
                                if (_hasGKS)
                                  CustomFilePicker(
                                    hintText: 'Giấy khai sinh' + _selectedDependant.titleFileBirthCertificate,
                                    required: true,
                                    initFile: _initFileBirthCertificate,
                                    onRemoveFile: (value) {
                                      setState(() {
                                        _ids.add(value);
                                        _initFileBirthCertificate = null;
                                      });
                                    },
                                    validator: (value) {
                                      if (value == null && _initFileBirthCertificate == null) {
                                        return 'Vui lòng tải lên giấy khai sinh' +
                                            _selectedDependant.titleFileBirthCertificate;
                                      }
                                      return null;
                                    },
                                    onChanged: (file) {
                                      _fileBirthCertificate = file;
                                    },
                                  ),
                                if (_hasGKS) SizedBox(height: 16),
                              ],
                            ),

                          // CCCD
                          if (!((_selectedDependant?.isConOrChauOrEm ?? false) && _age < 14))
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomCheckbox(
                                  key: ValueKey('_hasCCCDField${_hasCCCD ? 1 : 0}'),
                                  value: _hasCCCD,
                                  hintText: 'CCCD/Số định danh cá nhân',
                                  required: true,
                                  onChanged: (value) {
                                    setState(() {
                                      _hasCCCD = value;
                                    });
                                  },
                                ),
                                const SizedBox(height: 12),
                                if (_hasCCCD)
                                  IDPicker(
                                    key: ValueKey(
                                        'idPicker${_fileIdentityCards.firstOrNull?.path != null ? 1 : 0}-${_fileIdentityCards.lastOrNull?.path != null ? 1 : 0}'),
                                    hintText: 'CCCD/Số định danh cá nhân',
                                    required: true,
                                    initFiles: _fileIdentityCards,
                                    validator: (value) {
                                      return value == null || value.firstOrNull == null || value.lastOrNull == null
                                          ? 'Vui lòng tải lên CCCD/Số định danh cá nhân'
                                          : null;
                                    },
                                    onChanged: (files) {
                                      setState(() {
                                        _fileIdentityCards = files;
                                      });
                                    },
                                  ),
                                if (_hasCCCD) SizedBox(height: 16),
                              ],
                            ),

                          // THE HSSV
                          if ((_selectedDependant?.isConOrChauOrEm ?? false) && _age >= 14
                              && ((_dependantSituation?.isDangDiHoc ?? false) || _age >= 18))
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomCheckbox(
                                  key: ValueKey('_hasTheHSSVField${_hasTheHSSV ? 1 : 0}'),
                                  value: _hasTheHSSV,
                                  hintText: 'Thẻ học sinh/sinh viên',
                                  required: true,
                                  onChanged: (value) {
                                    setState(() {
                                      _hasTheHSSV = value;
                                    });
                                  },
                                ),
                                SizedBox(height: 12),
                                if (_hasTheHSSV)
                                  IDPicker(
                                    key: ValueKey(
                                        '_fileStudentCards${_fileStudentCards.firstOrNull?.path != null ? 1 : 0}-${_fileStudentCards.lastOrNull?.path != null ? 1 : 0}'),
                                    hintText: 'Thẻ học sinh/sinh viên',
                                    required: true,
                                    initFiles: _fileStudentCards,
                                    validator: (value) {
                                      return value == null || value.firstOrNull == null || value.lastOrNull == null
                                          ? 'Vui lòng tải lên thẻ học sinh/sinh viên'
                                          : null;
                                    },
                                    onChanged: (files) {
                                      setState(() {
                                        _fileStudentCards = files;
                                      });
                                    },
                                  ),
                                if (_hasTheHSSV) SizedBox(height: 16),
                              ],
                            ),

                          // Giấy đăng ký kết hôn
                          if (_selectedDependant?.isBMVC ?? false)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomCheckbox(
                                  key: ValueKey('_hasDKKHField${_hasDKKH ? 1 : 0}'),
                                  value: _hasDKKH,
                                  required: true,
                                  hintText: 'Giấy đăng ký kết hôn',
                                  onChanged: (value) {
                                    setState(() {
                                      _hasDKKH = value;
                                    });
                                  },
                                ),
                                SizedBox(height: 12),
                                if (_hasDKKH)
                                  CustomFilePicker(
                                    hintText: 'Giấy đăng ký kết hôn',
                                    required: true,
                                    initFile: _initFileMarriageCertificate,
                                    onRemoveFile: (value) {
                                      setState(() {
                                        _ids.add(value);
                                        _initFileMarriageCertificate = null;
                                      });
                                    },
                                    validator: (value) {
                                      if (value == null && _initFileMarriageCertificate == null) {
                                        return 'Vui lòng tải lên giấy đăng ký kết hôn';
                                      }
                                      return null;
                                    },
                                    onChanged: (file) {
                                      setState(() {
                                        _fileMarriageCertificate = file;
                                      });
                                    },
                                  ),
                                if (_hasDKKH) SizedBox(height: 16),
                              ],
                            ),

                          // Sổ hộ khẩu
                          if (!(_selectedDependant?.isConOrChauOrEm ?? false) &&
                              !(_selectedDependant?.isBM ?? false) &&
                              !(_selectedDependant?.isBMVC ?? false))
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomCheckbox(
                                  key: ValueKey('_hasSHKField${_hasSHK ? 1 : 0}'),
                                  value: _hasSHK,
                                  required: true,
                                  hintText: 'Sổ hộ khẩu',
                                  onChanged: (value) {
                                    setState(() {
                                      _hasSHK = value;
                                    });
                                  },
                                ),
                                SizedBox(height: 12),
                                if (_hasSHK)
                                  CustomMultiFilePicker(
                                    hintText: 'Sổ hộ khẩu',
                                    required: true,
                                    initFiles: _initFileHouseholdRegistrationBooks,
                                    onRemoveFile: (value) {
                                      setState(() {
                                        _ids.add(value.id);
                                        _initFileHouseholdRegistrationBooks.removeAt(value.index);
                                      });
                                    },
                                    validator: (value) {
                                      if (value == null && (_initFileHouseholdRegistrationBooks?.length ?? 0) == 0) {
                                        return 'Vui lòng tải lên sổ hộ khẩu';
                                      }
                                      return null;
                                    },
                                    onChanged: (files) {
                                      setState(() {
                                        _fileHouseholdRegistrationBooks = files;
                                      });
                                    },
                                  ),
                                if (_hasSHK) SizedBox(height: 16),
                              ],
                            ),

                          // Quyết định nuôi dưỡng
                          if (!(_selectedDependant?.isConOrChauOrEm ?? false) &&
                              !(_selectedDependant?.isBM ?? false) &&
                              !(_selectedDependant?.isBMVC ?? false))
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomCheckbox(
                                  key: ValueKey('_hasQDNDField${_hasQDND ? 1 : 0}'),
                                  value: _hasQDND,
                                  required: true,
                                  hintText: 'Quyết định nuôi dưỡng',
                                  onChanged: (value) {
                                    setState(() {
                                      _hasQDND = value;
                                    });
                                  },
                                ),
                                SizedBox(height: 12),
                                if (_hasQDND)
                                  CustomFilePicker(
                                    hintText: 'Quyết định nuôi dưỡng',
                                    required: true,
                                    initFile: _initFileWardConfirm,
                                    onRemoveFile: (value) {
                                      setState(() {
                                        _ids.add(value);
                                        _initFileWardConfirm = null;
                                      });
                                    },
                                    validator: (value) {
                                      if (value == null && _initFileWardConfirm == null) {
                                        return 'Vui lòng tải lên quyết định nuôi dưỡng';
                                      }
                                      return null;
                                    },
                                    onChanged: (file) {
                                      setState(() {
                                        _fileWardConfirm = file;
                                      });
                                    },
                                  ),
                                if (_hasQDND) SizedBox(height: 16),
                              ],
                            ),

                          // Giấy tờ khác
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomCheckbox(
                                key: ValueKey('_hasGTKField${_hasGTK ? 1 : 0}'),
                                value: _hasGTK,
                                hintText: 'Giấy tờ khác',
                                onChanged: (value) {
                                  setState(() {
                                    _hasGTK = value;
                                  });
                                },
                              ),
                              SizedBox(height: 12),
                              if (_hasGTK)
                                CustomImagesPicker(
                                  key: ValueKey('_attachList${_attachList.length}'),
                                  hintText: 'Giấy tờ khác',
                                  initFiles: _attachList,
                                  onChanged: (files) {
                                    setState(() {
                                      _attachList = files;
                                    });
                                  },
                                ),
                              if (_hasGTK) SizedBox(height: 16),
                            ],
                          ),
                        ],
                      ),

                      SizedBox(height: 12),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            'Thời gian giảm trừ',
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xff040404),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),

                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: CustomDateTimePicker(
                              hintText: 'Từ tháng',
                              initialValue: _fromTime,
                              format: MONTH_YEAR,
                              isDisplayDate: false,
                              required: true,
                              validator: (value) {
                                if (value == null) {
                                  return 'Vui lòng chọn thời gian bắt đầu giảm trừ gia cảnh';
                                }
                                return null;
                              },
                              onSelectDate: (date) {
                                setState(() {
                                  _fromTime = date;
                                });
                              },
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: CustomDateTimePicker(
                              key: ValueKey('_toTime${_age >= 18 && _selectedDependant.isConOrChauOrEm ? 1 : 0}'),
                              hintText: 'Tới tháng ',
                              required: _age >= 18 && _selectedDependant.isConOrChauOrEm,
                              initialValue: _toTime,
                              isDisplayDate: false,
                              format: MONTH_YEAR,
                              onSelectDate: (date) {
                                setState(() {
                                  _toTime = date;
                                });
                              },
                              validator: (value) {
                                if (value != null && _fromTime != null && value.isBefore(_fromTime)) {
                                  return 'Tới tháng phải lớn hơn hoặc bằng Từ tháng';
                                }
                                if (value == null && _age >= 18 && _selectedDependant.isConOrChauOrEm) {
                                  return 'Vui lòng chọn thời gian kết thúc giảm trừ';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),

                      CustomTextInput(
                        controller: _noteController,
                        maxLines: 5,
                        inputFormatters: [LengthLimitingTextInputFormatter(1000)],
                        hintText: 'Ghi chú',
                      ),
                    ],
                  ),
                ),
              ),

              // CTA
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                  // height: 64,
                  padding: EdgeInsets.fromLTRB(
                    16,
                    16,
                    16,
                    16,
                    // MediaQuery.of(context).padding.bottom,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white,
                        blurRadius: 20,
                        offset: Offset(0, -4),
                      ),
                    ],
                  ),
                  alignment: Alignment.center,
                  child: Opacity(
                    opacity: _formKey.currentState?.validate() ?? false ? 1 : 0.5,
                    child: CupertinoButton(
                      onPressed: () {
                        _onNext();
                      },
                      padding: EdgeInsets.zero,
                      minSize: 0,
                      child: Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: Color(0xffe81c2d),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Color(0xff000000).withOpacity(0.2),
                              blurRadius: 10,
                              offset: Offset(2, 4),
                            ),
                          ],
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          'Tiếp tục',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return EmptyListWidget(
          onRefresh: () {},
          content: 'Đã có lỗi xảy ra. Vui lòng thử lại.',
        );
      }
    });
  }

  void _onSelectDependant(DependantSelectItem item) {
    setState(() {
      _selectedDependant = item;

      if (_selectedDependant?.dateOfBirth != null) {
        _dob = DateFormat('dd/MM/yyyy').parse(_selectedDependant?.dateOfBirth);
        _age = DateTime.now().difference(_dob).inDays ~/ 365;
      }

      if (_selectedDependant?.personalIdNo != null || _selectedDependant?.taxNumber != null) {
        _hasId = true;
      } else {
        _hasId = false;
      }
      if (_selectedDependant?.personalIdNo != null) {
        _idController.text = _selectedDependant?.personalIdNo;
      } else {
        _idController.text = '';
      }

      if (_selectedDependant?.taxNumber != null) {
        _taxNumberController.text = _selectedDependant?.taxNumber;
      } else {
        _taxNumberController.text = '';
      }
    });
  }

  // METHODS
  Future<List<Ethnic>> _getEthnicity() async {
    final service = EthnicServiceImp();
    final res = await service.getList(DataSearchRequest(page: 0, size: 1000));

    // Placed "Kinh" at the top of the list
    res.sort((a, b) {
      if (a.name == 'Kinh') {
        return -1;
      }
      if (b.name == 'Kinh') {
        return 1;
      }
      return a.name.compareTo(b.name);
    });

    return res;
  }

  Future<List<DependantSituation>> _getDependantType() async {
    try {
      final url = '${AppConfigs.baseUrlIcnService}/v1/category/PTX_FAMILY_STATUS';

      http.Response response = await ApiRequest.get(url);

      log("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final error = jsonData['error'];
      final result = jsonData['result'] as List;

      if (error != null) {
        throw Exception(error);
      }

      return result.map((e) => DependantSituation.fromJson(e)).toList();
    } catch (ex) {
      log(ex.toString());
      return [];
    }
  }

  Future<List<DependantSelectItem>> _getDependantSelectItems() async {
    try {
      final empCode = sharedPreferences.get(AppConstant.KEY_USER_CODE);
      final url =
          '${AppConfigs.baseUrlSrqService}/v1/family-relationship/get-by-emp-code?empCode=$empCode&regType=TAO_MOI';

      http.Response response = await ApiRequest.get(url);

      log("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final error = jsonData['error'];
      final result = jsonData['result'] as List;

      if (error != null) {
        throw Exception(error);
      }

      return result.map((e) => DependantSelectItem.fromJson(e)).toList();
    } catch (ex) {
      log(ex.toString());
      return [];
    }
  }

  void _onConfirm() async {
    if (!_formKey.currentState.validate()) return;
    await context.read<DependantRegistrationOtpController>().signOTP();
    final data = context.read<DependantRegistrationOtpController>().success;

    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();

    String phoneNumber = sharedPreferences.getString(AppConstant.KEY_PHONE_NUMBER_VCC);

    Navigator.push(
      context,
      OtpPage.route(
        arguments: OtpArguments(
          otpRequest: OtpRequest(
            phoneNumber: phoneNumber,
            otpId: data.otpId,
            duration: data.duration,
          ),
          onCall: _submit,
        ),
      ),
    );
  }

  Future<void> _submit({OtpRequest request}) async {
    log(request.toString());

    // Submit the form
    _dependentRequest.otpId = request.otpId;
    _dependentRequest.otpCode = request.otpCode;
    _dependentRequest.isSmartOtp = request.isSmartOtp;

    context.read<DependantRegistrationController>().registerDependant(
      request: _dependentRequest,
    );
  }

  void _onNext() async {
    if (!_formKey.currentState.validate()) return;
    _dependentRequest = DependantRegistrationRequest(
      // Input data
      dependentRegisterId: widget.id,
      employeeCode: sharedPreferences.get(AppConstant.KEY_USER_CODE),
      familyRelationshipId: _selectedDependant?.familyRelationshipId ?? '',
      relationTypeName: _selectedDependant?.relationTypeName ?? '',
      hasIdCard: _hasId ? 1 : 0,
      dateOfBirth: _dob,
      nationCode: '1',
      nationName: 'Việt Nam',
      provinceId: _addressInfo?.provinceId,
      districtId: _addressInfo?.districtId,
      wardId: _addressInfo?.wardId,
      provinceCode: _addressInfo?.provinceCode ?? '',
      districtCode: _addressInfo?.districtCode ?? '',
      provinceName: _addressInfo?.provinceName ?? '',
      districtName: _addressInfo?.districtName ?? '',
      wardCode: _addressInfo?.wardCode ?? '',
      addressDetail: _addressInfo?.address ?? '',
      fullAddress: _addressInfo?.fullAddress ?? '',
      ethnicCode: _ethnic?.code ?? '1',
      ethnicName: _ethnic?.name ?? 'Kinh',
      memberCode: _memberOf?.value ?? '',
      familyStatusCode: _dependantSituation?.value ?? '',
      idNo: _hasId ? _idController.text ?? '' : '',
      taxNo: _hasId ? _taxNumberController.text ?? '' : '',
      codeNo: _codeNoController.text ?? '',
      bookNo: _bookNoController.text ?? '',
      injuryYear: _yearInjured?.year,
      disabilityLevelCode: _disabitlityLevel?.value ?? '',
      disabilityPercentage: _tyLeThuongTatController.text ?? '',
      note: _noteController.text ?? '',
      fromDate: _fromTime,
      toDate: _toTime,
      studentFromDate: _studentFromTime,
      studentToDate: _studentToTime,
      regTypeCode: 'TAO_MOI',
      yearOfDeath: _yearOfDead?.year,
      fileBirthCertificate: _hasGKS ? _fileBirthCertificate : null,
      fileStudentCards: _hasTheHSSV ? _fileStudentCards : [],
      fileMarriageCertificate: _hasDKKH ? _fileMarriageCertificate : null,
      fileIdentityCards: _hasCCCD ? _fileIdentityCards : [],
      fileHouseholdRegistrationBooks: _hasSHK ? _fileHouseholdRegistrationBooks : [],
      fileWardConfirm: _hasQDND ? _fileWardConfirm : null,
      attachList: _hasGTK ? _attachList : [],
      imageSign: null,
      ids: _ids
    );
    if (_age < 18 && _selectedDependant.isConOrChauOrEm) {
      _onConfirm();
    } else {
      final controller = context.read<DependantListController>();

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) {
            final empCode = sharedPreferences.get(AppConstant.KEY_USER_CODE);

            final request = GetSignFileRequest(
              employeeCode: empCode,
              relationTypeId: _selectedDependant?.relationTypeId ?? '',
              relationTypeName: _selectedDependant?.relationTypeName ?? '',
              familyRelationshipId: _selectedDependant?.familyRelationshipId ?? '',
              dateOfBirth: _dob != null ? DateFormat('dd/MM/yyyy').format(_dob) : '',
              fullAddress: _addressController.text ?? '',
              idNo: _idController.text ?? '',
              taxNo: _taxNumberController.text ?? '',
              codeNo: _codeNoController.text ?? '',
              bookNo: _bookNoController.text ?? '',
              fromDate: _fromTime != null ? DateFormat('dd/MM/yyyy').format(_fromTime) : '',
              toDate: _toTime != null ? DateFormat('dd/MM/yyyy').format(_toTime) : '',
              familyStatusName: _dependantSituation?.name ?? '',
              regTypeCode: _dependentRequest.regTypeCode ?? '',
            );

            return MultiProvider(
              providers: [
                Provider.value(value: _dependentRequest),
                ChangeNotifierProvider.value(
                  value: controller,
                ),
              ],
              child: SignFilePage(request: request, fromScreen: widget.fromScreen),
            );
          },
        ),
      );
    }
  }

  Future<List<DisabilityLevel>> _getHangThuongTat() async {
    try {
      final url = '${AppConfigs.baseUrlIcnService}/v1/category/PTX_DISABILITY_LEVEL';

      http.Response response = await ApiRequest.get(url);

      log("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final error = jsonData['error'];
      final result = jsonData['result'] as List;

      if (error != null) {
        throw Exception(error);
      }

      return result.map((e) => DisabilityLevel.fromJson(e)).toList();
    } catch (ex) {
      log(ex.toString());
      return [];
    }
  }

  void _setValue(DependantDetailsEntity entity) async {
    _hasId = entity.hasIdCard == 1;
    _selectedDependant = DependantSelectItem(
        dateOfBirth: entity.dateOfBirth,
        familyRelationshipId: entity.familyRelationshipId,
        taxNumber: entity.taxNo,
        relationTypeId: entity.relationTypeId,
        relationTypeName: entity.relationTypeName,
        fullName: entity.fullName);
    _dob = DateFormat("dd/MM/yyyy").parse(entity.dateOfBirth);
    _age = DateTime.now().difference(_dob).inDays ~/ 365;
    _idController.text = entity.idNo;
    _taxNumberController.text = entity.taxNo;
    _ethnic = Ethnic(code: entity.ethnicCode, ethnicId: 113, name: entity.ethnicName);
    _addressController.text = entity.fullAddress;
    _addressInfo = CurrentAddressInfo(
        provinceId: entity.provinceId,
        districtId: entity.districtId,
        wardId: entity.wardId,
        provinceCode: entity.provinceCode,
        districtCode: entity.districtCode,
        provinceName: entity.provinceName,
        districtName: entity.districtName,
        wardCode: entity.wardCode,
        wardName: entity.wardName,
        address: entity.addressDetail,
        fullAddress: entity.fullAddress);
    if (entity.memberCode != '-') {
      _memberOf = MemberOf(entity.memberCode, entity.memberName);
    }
    _dependantSituation = DependantSituation(value: entity.familyStatusCode, name: entity.familyStatusName);
    if (entity.injuryYear != null) {
      _yearInjured = DateFormat("yyyy").parse(entity.injuryYear.toString());
    }
    _disabitlityLevel = DisabilityLevel(value: entity.disabilityLevelCode, name: entity.disabilityLevelName);
    _tyLeThuongTatController.text = entity.disabilityPercentage?.toInt()?.toString() ?? '';
    if (entity.yearOfDeath != null) {
      _yearOfDead = DateFormat("yyyy").parse(entity.yearOfDeath.toString());
    }
    _codeNoController.text = entity.codeNo;
    _bookNoController.text = entity.bookNo;
    _fromTime = DateFormat("MM/yyyy").parse(entity.fromDate);
    if (entity.toDate != '-') {
      _toTime = DateFormat("MM/yyyy").parse(entity.toDate);
    }
    _noteController.text = entity.note;

    if (entity.studentFromDate != '-')
      _studentFromTime = DateFormat('dd/MM/yyyy').parse(entity.studentFromDate);
    if (entity.studentToDate != '-')
      _studentToTime = DateFormat('dd/MM/yyyy').parse(entity.studentToDate);

    if ((_selectedDependant?.isConOrChauOrEm ?? false) ||
        (_selectedDependant?.isBM ?? false) ||
        (_selectedDependant?.isBMVC ?? false)) {
      _hasGKS = entity.fileBirthCertificateList.length > 0;
      if (_hasGKS) {
        _initFileBirthCertificate = entity.fileBirthCertificateList[0];
      }
    }
    if (!((_selectedDependant?.isConOrChauOrEm ?? false) && _age < 14)) {
      List<FileEntityRequest> fileIdentityCardList = entity.fileIdentityCardList.map((item) {
        return FileEntityRequest(
            url: '${AppConfigs.baseUrlSrqService}/v1/tax/download/${item.attachmentId}?checkSum=${item.checkSum}',
            fileName: item.fileName,
            id: item.attachmentId);
      }).toList();
      _hasCCCD = fileIdentityCardList.length > 0;
      if (_hasCCCD) {
        var files = await DependantRegistrationService().getFile(fileIdentityCardList);
        _fileIdentityCards = List<File>.from(files);
        _ids.addAll(fileIdentityCardList.map((e) => e.id).toList());
      }
    }
    if ((_selectedDependant?.isConOrChauOrEm ?? false) && _age >= 18) {
      List<FileEntityRequest> fileStudentCardList = entity.fileStudentCardList.map((item) {
        return FileEntityRequest(
            url: '${AppConfigs.baseUrlSrqService}/v1/tax/download/${item.attachmentId}?checkSum=${item.checkSum}',
            fileName: item.fileName,
            id: item.attachmentId);
      }).toList();
      _hasTheHSSV = fileStudentCardList.length > 0;
      if (_hasTheHSSV) {
        var files = await DependantRegistrationService().getFile(fileStudentCardList);
        _fileStudentCards = List<File>.from(files);
        _ids.addAll(fileStudentCardList.map((e) => e.id).toList());
      }
    }
    if (_selectedDependant?.isBMVC ?? false) {
      _hasDKKH = entity.fileMarriageCertificateList.length > 0;
      if (_hasDKKH) _initFileMarriageCertificate = entity.fileMarriageCertificateList[0];
    }
    if (!(_selectedDependant?.isConOrChauOrEm ?? false) &&
        !(_selectedDependant?.isBM ?? false) &&
        !(_selectedDependant?.isBMVC ?? false)) {
      _hasSHK = entity.fileHouseholdRegistrationBookList.length > 0;
      if (_hasSHK) _initFileHouseholdRegistrationBooks = entity.fileHouseholdRegistrationBookList;
    }
    if (!(_selectedDependant?.isConOrChauOrEm ?? false) &&
        !(_selectedDependant?.isBM ?? false) &&
        !(_selectedDependant?.isBMVC ?? false)) {
      _hasQDND = entity.fileConfirmList.length > 0;
      if (_hasQDND) {
        _initFileWardConfirm = entity.fileConfirmList[0];
      }
    }

    List<FileEntityRequest> attachFileList = entity.attachFileList.map((item) {
      return FileEntityRequest(
          url: '${AppConfigs.baseUrlSrqService}/v1/tax/download/${item.attachmentId}?checkSum=${item.checkSum}',
          fileName: item.fileName,
          id: item.attachmentId);
    }).toList();
    _hasGTK = attachFileList.length > 0;
    if (_hasGTK) {
      var files = await DependantRegistrationService().getFile(attachFileList);
      _attachList = List<File>.from(files);
      _ids.addAll(attachFileList.map((e) => e.id).toList());
    }

    _formKey.currentState.validate();
    setState(() {});
  }
}
