import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/extension/date_time.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/screen/dependant/cancel_registration/cancel_registration_page.dart';
import 'package:hstd/screen/dependant/custom_date_time_picker.dart';
import 'package:hstd/screen/dependant/custom_multi_file_picker.dart';
import 'package:hstd/screen/dependant/custom_text_input.dart';
import 'package:hstd/screen/dependant/dependant_details/dependant_details_controller.dart';
import 'package:hstd/screen/dependant/dependant_details/dependant_details_entity.dart';
import 'package:hstd/screen/dependant/dependant_list/dependant_list_controller.dart';
import 'package:hstd/screen/dependant/dependant_registration/dependant_registration_controller.dart';
import 'package:hstd/screen/dependant/dependant_registration/dependant_registration_request.dart';
import 'package:hstd/screen/dependant/models/dependant_select.dart';
import 'package:hstd/screen/dependant/models/file_response.dart';
import 'package:hstd/screen/dependant/otp/otp_page.dart';
import 'package:hstd/screen/dependant/otp/otp_request.dart';
import 'package:hstd/screen/dependant/sign_file/sign_file_page.dart';
import 'package:hstd/screen/dependant/sign_file/sign_file_request.dart';
import 'package:hstd/screen/dependant/tax_reduction_register/tax_reduction_register_controller.dart';
import 'package:hstd/widget/dialog_auto_close.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:provider/provider.dart';

class TaxReductionRegisterPage extends StatelessWidget {
  const TaxReductionRegisterPage({
    Key key,
    this.entity,
    this.fromScreen,
    this.isAdd,
  }) : super(key: key);

  final DependantDetailsEntity entity;
  final FromScreen fromScreen;
  final bool isAdd;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: Colors.white,
        extendBody: true,
        appBar: AppBar(
          title: Text(
            '${isAdd != null && isAdd ? 'Đăng' : 'Sửa đăng'} ký kết thúc',
            style: TextStyle(
              color: Colors.black,
            ),
          ),
          foregroundColor: Colors.black,
          centerTitle: true,
          backgroundColor: Colors.white,
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(0),
            child: Divider(
              height: 1,
              thickness: 1,
              color: Color(0xffe1e3e5),
            ),
          ),
          elevation: 0,
        ),
        body: MultiProvider(
          providers: [
            ChangeNotifierProvider(
              create: (_) => DependantDetailsController(entity.id, isAdd)..getDependantDetails(),
            ),
            ChangeNotifierProvider(
              create: (context) => DependantRegistrationController(),
            ),
            ChangeNotifierProvider(
              create: (_) => TaxReductionController(),
            ),
          ],
          child: _Body(entity: entity, isAdd: isAdd, fromScreen: fromScreen),
        ),
      ),
    );
  }
}

class _Body extends StatefulWidget {
  const _Body({
    Key key,
    this.entity,
    this.isAdd,
    this.fromScreen,
  }) : super(key: key);

  final bool isAdd;
  final DependantDetailsEntity entity;
  final FromScreen fromScreen;

  @override
  State<_Body> createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  DependantRegistrationRequest _dependentRequest;
  final _formKey = GlobalKey<FormState>();

  DateTime _endMonth;
  final _noteController = TextEditingController();
  List<File> _files = [];
  int _dependentRegisterId;
  int _relationTypeId;
  String _relationTypeName;
  int _familyRelationshipId;
  int _age;
  String _dob;
  String _address;
  String _familyStatusName;
  String _fromTime;
  String _fromDate;
  String _toDate;
  String _idNo;
  String _taxNumber;
  String _codeNo;
  String _bookNo;
  List<FileEntity> _initFiles;
  List<int> _ids = [];
  DependantSelectItem _selectedDependant;

  @override
  void initState() {
    if(!(widget.isAdd != null && widget.isAdd)) _dependentRegisterId = widget.entity.id;
    _setValue(widget.entity);
    context.read<TaxReductionController>().listen(
      onLoading: (isLoading) {
        if (isLoading) {
          LoadingDialogTransparent.show(context);
        } else {
          LoadingDialogTransparent.hide(context);
        }
      },
      onError: (error) {
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: error?.toString()?.replaceAll('Exception: ', ''),
          ),
        );
      },
    );
    context.read<DependantDetailsController>().listen(
      onError: (error) {
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: error?.toString()?.replaceAll('Exception: ', ''),
          ),
        );
      },
      onSuccess: (value) {
        setState(() {
          _setValue(value);
        });
      },
    );
    context.read<DependantRegistrationController>().listen(
      onLoading: (isLoading) {
        if (isLoading) {
          LoadingDialogTransparent.show(context);
        } else {
          LoadingDialogTransparent.hide(context);
        }
      },
      onError: (error) {
        Navigator.pop(context);
        openCustomDialog(
          context: context,
          dialog: DialogNotify(
            content: error?.toString()?.replaceAll('Exception: ', ''),
          ),
        );
      },
      onSuccess: (data) {
        context.read<DependantListController>().getDependantList();

        Navigator.pop(context);
        Navigator.pop(context);
        if (widget.fromScreen == FromScreen.dependantDetails) {
          Navigator.pop(context);
        }
        if (_dependentRegisterId != null) {
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: 'Sửa bản ghi thành công',
              showIcon: true,
            ),
          );
        } else {
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: 'Đăng ký kết thúc thành công',
              showIcon: true,
            ),
          );
        }
      },
    );

    super.initState();
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SingleChildScrollView(
          padding: EdgeInsets.fromLTRB(16, 16, 16, 100),
          child: SizedBox(
            height: MediaQuery.of(context).size.height - 100,
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                      padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                widget.entity.fullName,
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Color(0xff040404),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 16),
                          Divider(
                            height: 1,
                            color: Color(0xFFB5B4B4),
                          ),
                          SizedBox(height: 16),
                          Row(
                            children: [
                              Text( 'Mối quan hệ',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xff7f7f7f),
                                ),
                              ),
                              SizedBox(width: 16),
                              Expanded(
                                child: Text(
                                  widget.entity.relationTypeName ?? '-',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff404040),
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 16),
                          Row(
                            children: [
                              Text(
                                'Thời gian giảm trừ',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xff7f7f7f),
                                ),
                              ),
                              SizedBox(width: 16),
                              Expanded(
                                child: Text(
                                  _fromDate + (_toDate != null && _toDate != '-' ? ' - ' + _toDate : ''),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff404040),
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                              ),
                              SizedBox(height: 16),
                            ],
                          ),
                        ],
                      )
                  ),
                  SizedBox(height: 16),
                  CustomDateTimePicker(
                    key: ValueKey('_endMonth${_endMonth?.year}'),
                    hintText: 'Tháng kết thúc giảm',
                    format: MONTH_YEAR,
                    isDisplayDate: false,
                    initialValue: _endMonth,
                    required: true,
                    validator: (value) {
                      if (value == null) {
                        return 'Vui lòng chọn tháng kết thúc giảm';
                      }
                      return null;
                    },
                    onSelectDate: (value) {
                      _endMonth = value;
                      _formKey.currentState.validate();
                    },
                  ),
                  const SizedBox(height: 16),
                  CustomTextInput(
                    controller: _noteController,
                    inputFormatters: [LengthLimitingTextInputFormatter(1000)],
                    maxLines: 4,
                    hintText: 'Ghi chú',
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        'Tệp đính kèm',
                        style: TextStyle(
                          color: Color(0xff040404),
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      // Text(
                      //   ' *',
                      //   style: TextStyle(
                      //     fontSize: 14,
                      //     color: Colors.red,
                      //   ),
                      // ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  CustomMultiFilePicker(
                    key: ValueKey('_fileFieldKey'),
                    hintText: 'Giấy tờ khác',
                    onRemoveFile: (value) {
                      setState(() {
                        _ids.add(value.id);
                        _initFiles.removeAt(value.index);
                      });
                    },
                    // validator: (value) {
                    //   if (value == null && (_initFiles?.length ?? 0) == 0) {
                    //     return 'Vui lòng tải lên giấy tờ khác';
                    //   }
                    //   return null;
                    // },
                    initFiles:_initFiles,
                    onChanged: (files) {
                      setState(() {
                        _files = files;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: SafeArea(
            child: Container(
              height: 64,
              padding: EdgeInsets.symmetric(horizontal: 16),
              alignment: Alignment.center,
              child: Opacity(
                opacity: _formKey?.currentState?.validate() ?? false ? 1 : 0.5,
                child: CupertinoButton(
                  onPressed: () {
                    _onNext();
                  },
                  padding: EdgeInsets.zero,
                  minSize: 0,
                  child: Container(
                    height: 48,
                    decoration: BoxDecoration(
                      color: Color(0xffe81c2d),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0xff000000).withOpacity(0.2),
                          blurRadius: 10,
                          offset: Offset(2, 4),
                        ),
                      ],
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      'Xác nhận',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _onConfirm() async {
    if (!_formKey.currentState.validate()) return;
    await context.read<TaxReductionController>().signOTP();
    final data = context.read<TaxReductionController>().success;

    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();

    String phoneNumber = sharedPreferences.getString(AppConstant.KEY_PHONE_NUMBER_VCC);

    Navigator.push(
      context,
      OtpPage.route(
        arguments: OtpArguments(
          otpRequest: OtpRequest(
            phoneNumber: phoneNumber,
            otpId: data.otpId,
            duration: data.duration,
          ),
          onCall: _submit,
        ),
      ),
    );
  }

  void _onNext() async {
    if (!_formKey.currentState.validate()) return;
    _dependentRequest = DependantRegistrationRequest(
      // Input data
      dependentRegisterId: _dependentRegisterId,
      dependentRegisterIdOld: widget.entity.id,
      employeeCode: sharedPreferences.get(AppConstant.KEY_USER_CODE),
      familyRelationshipId: _familyRelationshipId ?? '',
      relationTypeName: _relationTypeName ?? '',
      regTypeCode: 'DANG_KY_GIAM',
      inputType: 'MOBILE',
      toDate: _endMonth,
      note: _noteController.text,
      attachList: _files,
      ids: _ids,
      imageSign: null,
      relationTypeId: _relationTypeId ?? '',
      dateOfBirth: DateFormat('dd/MM/yyyy').parse(_dob),
      fullAddress: _address ?? '',
      idNo: _idNo ?? '',
      taxNo: _taxNumber ?? '',
      codeNo: _codeNo ?? '',
      bookNo: _bookNo ?? '',
      fromDate: DateFormat('dd/MM/yyyy').parse(_fromTime),
      familyStatusName: _familyStatusName != '-' ? _familyStatusName : (widget.entity.relationStatusName ?? ''),
    );
    if (_age < 18 && _selectedDependant.isConOrChauOrEm) {
      _onConfirm();
    } else {
      final controller = context.read<DependantListController>();

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) {
            final empCode = sharedPreferences.get(AppConstant.KEY_USER_CODE);

            final request = GetSignFileRequest(
              employeeCode: empCode,
              relationTypeId: _relationTypeId ?? '',
              relationTypeName: _relationTypeName ?? '',
              familyRelationshipId: _familyRelationshipId ?? '',
              dateOfBirth: _dob ?? '',
              fullAddress: _address ?? '',
              idNo: _idNo ?? '',
              taxNo: _taxNumber ?? '',
              codeNo: _codeNo ?? '',
              bookNo: _bookNo ?? '',
              fromDate: _fromTime ?? '',
              toDate: DateFormat('dd/MM/yyyy').format(_endMonth) ?? '',
              familyStatusName: _familyStatusName != '-' ? _familyStatusName : (widget.entity.relationStatusName ?? ''),
              regTypeCode: _dependentRequest.regTypeCode ?? '',
            );

            return MultiProvider(
              providers: [
                Provider.value(value: _dependentRequest),
                ChangeNotifierProvider.value(
                  value: controller,
                ),
              ],
              child: SignFilePage(request: request, fromScreen: widget.fromScreen),
            );
          },
        ),
      );
    }
  }

  Future<void> _submit({OtpRequest request}) {
    _dependentRequest.otpId = request.otpId;
    _dependentRequest.otpCode = request.otpCode;
    _dependentRequest.isSmartOtp = request.isSmartOtp;

    context.read<DependantRegistrationController>().registerDependant(
      request: _dependentRequest,
    );

  }

  _setValue(DependantDetailsEntity entity) {
    _relationTypeId = entity.relationTypeId;
    _relationTypeName = entity.relationTypeName;
    _familyRelationshipId = entity.familyRelationshipId;
    _selectedDependant = DependantSelectItem(relationTypeName: _relationTypeName);
    _age = DateTime.now().difference(DateFormat("dd/MM/yyyy").parse(entity.dateOfBirth)).inDays ~/ 365;
    _dob = entity.dateOfBirth;
    _address = entity.fullAddress;
    _familyStatusName = entity.familyStatusName;
    _fromTime = '01/' + entity.fromDate;
    _fromDate = entity.fromDate;
    if (entity.toDate != '-') {
      _toDate = entity.toDate;
    }
    _idNo = entity.idNo;
    _taxNumber = entity.taxNo;
    _codeNo = entity.codeNo;
    _bookNo = entity.bookNo;
    if (!(widget.isAdd != null && widget.isAdd)) {
      _endMonth = DateFormat("MM/yyyy").parse(entity.toDate);
      _noteController.text = entity.note;
      _initFiles = entity.attachFileList;
    }
    _formKey.currentState?.validate();
  }
}
