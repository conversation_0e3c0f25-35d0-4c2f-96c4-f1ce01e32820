import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/screen/dependant/otp/otp_request.dart';

part 'otp_state.dart';

class OtpCubit extends Cubit<OtpState> {
  OtpCubit() : super(const OtpState());

  Future<void> submit(BuildContext context) async {
    print('OtpCubit submit - state.onCall: ${state.onCall}');
    print('OtpCubit submit - state.otpCode: ${state.otpCode}');
    print('OtpCubit submit - state.otpId: ${state.otpId}');

    if (state.onCall != null && state.otpCode != null && state.otpCode.isNotEmpty) {
      try {
        print('OtpCubit submit - calling onCall function');
        await state.onCall(
            request: OtpRequest(
          otpId: state.otpId,
          otpCode: state.otpCode,
          isSmartOtp: "N",
        ));
        print('OtpCubit submit - onCall completed successfully');
      } catch (e) {
        print('Error in OTP submit: $e');
        rethrow;
      }
    } else {
      print('OtpCubit submit - onCall is null or otpCode is empty');
    }
  }

  void changeCurrentText(String value) {
    if (isClosed) return;

    bool showButtonSubmit = false;
    if (value != null && value.length == 6) {
      showButtonSubmit = true;
    }
    emit(
      state.copyWith(
        showButtonSubmit: showButtonSubmit,
        otpCode: value ?? '',
      ),
    );
  }

  void changeDuration({VoidCallback onCancel}) {
    if (isClosed) return;

    if (state.duration == 0) {
      onCancel?.call();
    } else {
      emit(
        state.copyWith(
          duration: state.duration - 1,
        ),
      );
    }
  }

  void setDuration({int duration}) {
    if (isClosed) return;
    emit(
      state.copyWith(
        duration: duration ?? 300,
      ),
    );
  }

  void setOnCall(Future<void> Function({OtpRequest request}) onCall) {
    if (isClosed) return;
    print('OtpCubit setOnCall - onCall: $onCall');
    emit(
      state.copyWith(
        onCall: onCall,
        updateOnCall: true,
      ),
    );
    print('OtpCubit setOnCall - state.onCall after emit: ${state.onCall}');
  }

  void setValue(int otpId) {
    if (isClosed) return;
    emit(
      state.copyWith(otpId: otpId ?? 0),
    );
  }
}
