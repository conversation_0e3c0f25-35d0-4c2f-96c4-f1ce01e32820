import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/screen/dependant/otp/otp_request.dart';

part 'otp_state.dart';

class OtpCubit extends Cubit<OtpState> {
  OtpCubit() : super(const OtpState());

  Future<void> submit(BuildContext context) async {

    await state.onCall(
        request: OtpRequest(
      otpId: state.otpId,
      otpCode: state.otpCode,
      isSmartOtp: "N",
    ));
  }

  void changeCurrentText(String value) {
    bool showButtonSubmit = false;
    if (value.length == 6) {
      showButtonSubmit = true;
    }
    emit(
      state.copyWith(
        showButtonSubmit: showButtonSubmit,
        otpCode: value,
      ),
    );
  }

  void changeDuration({VoidCallback onCancel}) {
    if (state.duration == 0) {
      onCancel();
    } else {
      emit(
        state.copyWith(
          duration: state.duration - 1,
        ),
      );
    }
  }

  void setDuration({int duration}) {
    emit(
      state.copyWith(
        duration: duration,
      ),
    );
  }

  void setOnCall(Future<void> Function({OtpRequest request}) onCall) {
    emit(
      state.copyWith(
        onCall: onCall,
      ),
    );
  }

  void setValue(int otpId) {
    emit(
      state.copyWith(otpId: otpId),
    );
  }
}
