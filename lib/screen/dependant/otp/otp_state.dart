part of 'otp_cubit.dart';

class OtpState extends Equatable {
  final bool showButtonSubmit;
  final String message;
  final int otpId;
  final String otpCode;
  final int duration;
  final Future<void> Function({OtpRequest request}) onCall;

  const OtpState({
    this.showButtonSubmit = false,
    this.otpId,
    this.otpCode,
    this.duration,
    this.message,
    this.onCall,
  });

  @override
  List<Object> get props => [
        showButtonSubmit,
        otpId,
        otpCode,
        duration,
        message,
        onCall
      ];

  OtpState copyWith(
      {
      bool showButtonSubmit,
      int otpId,
      String otpCode,
      int duration,
      String message,
      Future<void> Function({OtpRequest request}) onCall}) {
    return OtpState(
        showButtonSubmit: showButtonSubmit ?? this.showButtonSubmit,
        otpId: otpId ?? this.otpId,
        otpCode: otpCode ?? this.otpCode,
        duration: duration ?? this.duration,
        message: message ?? this.message,
        onCall: onCall ?? this.onCall);
  }
}
