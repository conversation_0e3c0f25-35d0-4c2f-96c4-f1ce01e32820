part of 'otp_cubit.dart';

class OtpState extends Equatable {
  final bool showButtonSubmit;
  final String message;
  final int otpId;
  final String otpCode;
  final int duration;
  final Future<void> Function({OtpRequest request}) onCall;

  const OtpState({
    this.showButtonSubmit = false,
    this.otpId,
    this.otpCode,
    this.duration,
    this.message,
    this.onCall,
  });

  @override
  List<Object> get props => [
        showButtonSubmit,
        otpId ?? 0,
        otpCode ?? '',
        duration ?? 0,
        message ?? '',
      ];

  OtpState copyWith({
    bool showButtonSubmit,
    int otpId,
    String otpCode,
    int duration,
    String message,
    Future<void> Function({OtpRequest request}) onCall,
  }) {
    print('OtpState copyWith - onCall parameter: $onCall');
    print('OtpState copyWith - this.onCall: ${this.onCall}');
    final newOnCall = onCall != null ? onCall : this.onCall;
    print('OtpState copyWith - newOnCall: $newOnCall');

    return OtpState(
      showButtonSubmit: showButtonSubmit ?? this.showButtonSubmit,
      otpId: otpId ?? this.otpId,
      otpCode: otpCode ?? this.otpCode,
      duration: duration ?? this.duration,
      message: message ?? this.message,
      onCall: newOnCall,
    );
  }
}
