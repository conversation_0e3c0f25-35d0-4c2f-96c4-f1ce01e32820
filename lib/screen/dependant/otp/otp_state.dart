part of 'otp_cubit.dart';

class OtpState extends Equatable {
  final bool showButtonSubmit;
  final String message;
  final int otpId;
  final String otpCode;
  final int duration;
  final Future<void> Function({OtpRequest request}) onCall;

  const OtpState({
    this.showButtonSubmit = false,
    this.otpId,
    this.otpCode,
    this.duration,
    this.message,
    this.onCall,
  });

  @override
  List<Object> get props => [
        showButtonSubmit,
        otpId ?? 0,
        otpCode ?? '',
        duration ?? 0,
        message ?? '',
      ];

  OtpState copyWith({
    bool showButtonSubmit,
    int otpId,
    String otpCode,
    int duration,
    String message,
    Future<void> Function({OtpRequest request}) onCall,
    bool updateOnCall = false,
  }) {
    return OtpState(
      showButtonSubmit: showButtonSubmit ?? this.showButtonSubmit,
      otpId: otpId ?? this.otpId,
      otpCode: otpCode ?? this.otpCode,
      duration: duration ?? this.duration,
      message: message ?? this.message,
      onCall: updateOnCall ? onCall : (onCall ?? this.onCall),
    );
  }
}
