import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/configs/app_configs.dart';
import 'package:hstd/screen/dependant/dependant_details/dependant_details_service.dart';
import 'package:hstd/screen/dependant/models/file_response.dart';
import 'package:hstd/screen/dependant/view_pdf/view_pdf_page.dart';
import 'package:image_picker/image_picker.dart';
import 'package:nb_utils/nb_utils.dart';

class OutputData {
  const OutputData({this.id, this.index});
  final int id;
  final int index;
}

class CustomMultiFilePicker extends StatelessWidget {
  const CustomMultiFilePicker({
    Key key,
    this.enabled = true,
    this.required = false,
    this.hintText,
    this.initFiles,
    this.validator,
    this.onChanged,
    this.onRemoveFile,
  }) : super(key: key);

  final bool enabled;
  final bool required;
  final String hintText;
  final List<FileEntity> initFiles;
  final FormFieldValidator<List<File>> validator;
  final ValueChanged<List<File>> onChanged;
  final ValueChanged<OutputData> onRemoveFile;

  @override
  Widget build(BuildContext context) {
    return FormField<List<File>>(
      enabled: enabled,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: validator ??
          (required
              ? (value) {
                  return value == null ? 'Vui lòng tải lên $hintText' : null;
                }
              : null),
      builder: (field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 66,
              child: DottedBorder(
                color: !field.hasError ? Color(0xffd4d4d4) : Color(0xffe81c2d),
                strokeWidth: 1,
                borderType: BorderType.RRect,
                radius: Radius.circular(8),
                dashPattern: [10, 5, 10, 5, 10, 5],
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: CupertinoButton(
                        minSize: 0,
                        padding: EdgeInsets.zero,
                        onPressed: () => _showAction(context, field),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(width: 16),
                            Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Color(0xffeaeaea),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Text(
                                    'Tải lên tệp tin',
                                    style: TextStyle(
                                      color: Color(0xff525252),
                                      fontSize: 14,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    maxLines: 1,
                                  ),
                                  SizedBox(height: 2),
                                  Text(
                                    'Định dạng PDF, JPG, PNG',
                                    style: TextStyle(
                                      color: Color(0xff7f7f7f),
                                      fontSize: 11,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            ...(field.value ?? []).asMap().entries.map((entry) {
              int index = entry.key; // Lấy index
              var file = entry.value; // Lấy phần tử
              return Column(
                children: [
                  SizedBox(height: 12),
                  SizedBox(
                    height: 66,
                    child: DottedBorder(
                      color: !field.hasError ? Color(0xffd4d4d4) : Color(0xffe81c2d),
                      strokeWidth: 1,
                      borderType: BorderType.RRect,
                      radius: Radius.circular(8),
                      dashPattern: [10, 5, 10, 5, 10, 5],
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Expanded(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(width: 16),
                                Container(
                                  width: 32,
                                  height: 32,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Color(0xffeaeaea),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      Text(
                                        file.path?.split('/')?.last ?? 'Tải lên tệp tin',
                                        style: TextStyle(
                                          color: Color(0xff2d84ff),
                                          fontSize: 14,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        maxLines: 1,
                                      ),
                                      SizedBox(height: 2),
                                      Text(
                                        file.lengthSync() == null
                                            ? 'Định dạng PDF, JPG, PNG'
                                            : file.lengthSync() < 1000000
                                                ? 'Dung lượng: ${(file.lengthSync() / 1000).toStringAsFixed(1)} KB'
                                                : 'Dung lượng: ${(file.lengthSync() / 1000 / 1000).toStringAsFixed(1)} MB',
                                        style: TextStyle(
                                          color: Color(0xff7f7f7f),
                                          fontSize: 11,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (file != null)
                            CupertinoButton(
                              onPressed: () {
                                List<File> files = field.value
                                    .asMap()
                                    .entries
                                    .where((entry) => entry.key != index)
                                    .map((entry) => entry.value)
                                    .toList();
                                field.didChange(files.length == 0 ? null : files);
                                onChanged?.call(files.length == 0 ? null : files);
                              },
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              child: SvgPicture.asset('assets/vectors/ic_trash.svg'),
                            ),
                        ],
                      ),
                    ),
                  )
                ],
              );
            }),
            ...(initFiles ?? []).asMap().entries.map((entry) {
              int index = entry.key; // Lấy index
              var file = entry.value; // Lấy phần tử
              return
                GestureDetector(
                  onTap: () {
                    if (!_isPdfOrImage(file.fileName)) return;
                    DependantDetailsService()
                        .getFile(
                        '${AppConfigs.baseUrlSrqService}/v1/tax/download/${file.attachmentId}?checkSum=${file.checkSum}')
                        .then((value) {
                      if (value != null) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) {
                              return ViewPdfPage(
                                arguments: ViewPdfArgument(
                                  title: file.fileName,
                                  fileName: file.fileName,
                                  data: value,
                                  isInvoiceFile: true,
                                  isShowButton: false,
                                ),
                              );
                            },
                          ),
                        );
                      } else {
                        toast('Có lỗi sảy ra');
                      }
                    }, onError: (error) {
                      toast(error?.toString()?.replaceAll('Exception: ', ''));
                    });
                  },
                  child: Column(
                    children: [
                      SizedBox(height: 12),
                      SizedBox(
                        height: 66,
                        child: DottedBorder(
                          color: Color(0xffd4d4d4),
                          strokeWidth: 1,
                          borderType: BorderType.RRect,
                          radius: Radius.circular(8),
                          dashPattern: [10, 5, 10, 5, 10, 5],
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Expanded(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(width: 16),
                                    Container(
                                      width: 32,
                                      height: 32,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Color(0xffeaeaea),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: <Widget>[
                                          Text(
                                            file.fileName,
                                            style: TextStyle(
                                              color: Color(0xff2d84ff),
                                              fontSize: 14,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            maxLines: 1,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (file != null)
                                CupertinoButton(
                                  onPressed: () {
                                    onRemoveFile?.call(OutputData(id: file.attachmentId, index: index));
                                  },
                                  padding: EdgeInsets.symmetric(horizontal: 16),
                                  child: SvgPicture.asset('assets/vectors/ic_trash.svg'),
                                ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                )
              ;
            }),
            if (field.hasError) SizedBox(height: 10),
            if (field.hasError)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  field.errorText,
                  style: TextStyle(color: Color(0xffe81c2d), fontSize: 12),
                ),
              ),
          ],
        );
      },
    );
  }

  Future<void> _pickFile(BuildContext context, FormFieldState<List<File>> field) async {
    var resultFile = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
    );

    final files = resultFile?.files;

    if (files?.isEmpty ?? true) return;
    bool isMaxSize = false;

    var maxFileSizeInBytes = 5 * 1048576;
    List<File> filesSuccess = [];
    for (var xfile in files) {
      File file = File(xfile.path);
      int fileSize = await file.length();
      if (fileSize <= maxFileSizeInBytes) {
        filesSuccess.add(file);
      } else {
        isMaxSize = true;
      }
    }

    if (isMaxSize) {
      toast('File không được lớn hơn 5M');
      if (filesSuccess.length == 0)
        return;
    }

    final newFiles = List<File>.from(field.value ?? []);
    newFiles.addAll(files.map((e) => File(e.path)));

    field.didChange(newFiles);
    onChanged?.call(newFiles);
  }

  Future<void> _pickImage(BuildContext context, FormFieldState<List<File>> field, ImageSource imageSource) async {
    final _picker = ImagePicker();
    final newFiles = List<File>.from(field.value ?? []);

    if (imageSource == ImageSource.gallery) {
      final results = await _picker.pickMultiImage(imageQuality: 100);

      if (results == null) return;

      bool isMaxSize = false;
      var maxFileSizeInBytes = 5 * 1048576;
      List<File> filesSuccess = [];
      for (var xfile in results) {
        File file = File(xfile.path);
        int fileSize = await file.length();
        if (fileSize <= maxFileSizeInBytes) {
          filesSuccess.add(file);
        } else {
          isMaxSize = true;
        }
      }

      if (isMaxSize) {
        toast('Ảnh không được lớn hơn 5M');
        if (filesSuccess.length == 0)
          return;
      }

      newFiles.addAll(results.map((e) => File(e.path)));
    } else {
      final result = await _picker.pickImage(source: imageSource, imageQuality: 100);

      if (result == null) return;

      var maxFileSizeInBytes = 5 * 1048576;

      var fileSize = await result.readAsBytes();

      if (fileSize.length > maxFileSizeInBytes) {
        toast('Ảnh không được lớn hơn 5M');
        return;
      }

      newFiles.add(File(result.path));
    }

    field.didChange(newFiles);
    onChanged?.call(newFiles);
  }

  void _showAction(BuildContext context, FormFieldState<List<File>> field) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            Container(
              height: 16,
              color: Colors.transparent,
            ),
            ListTile(
              leading: Icon(Icons.photo_camera_outlined),
              title: Text('Chụp ảnh'),
              onTap: () {
                _pickImage(context, field, ImageSource.camera);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.photo_library_outlined),
              title: Text('Chọn ảnh'),
              onTap: () {
                _pickImage(context, field, ImageSource.gallery);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.file_present_outlined),
              title: Text('Chọn tệp tin'),
              onTap: () {
                _pickFile(context, field);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  bool _isPdfOrImage(String fileName) {
    final lowerCaseFileName = fileName.toLowerCase(); // Chuyển thành chữ thường
    return lowerCaseFileName.endsWith('.pdf') ||
        lowerCaseFileName.endsWith('.jpeg') ||
        lowerCaseFileName.endsWith('.jpg') ||
        lowerCaseFileName.endsWith('.png');
  }
}
