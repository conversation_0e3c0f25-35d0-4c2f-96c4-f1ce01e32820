import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/configs/app_configs.dart';
import 'package:hstd/screen/dependant/dependant_details/dependant_details_service.dart';
import 'package:hstd/screen/dependant/models/file_response.dart';
import 'package:hstd/screen/dependant/view_pdf/view_pdf_page.dart';
import 'package:image_picker/image_picker.dart';
import 'package:nb_utils/nb_utils.dart';

class CustomFilePicker extends StatelessWidget {
  const CustomFilePicker({
    Key key,
    this.enabled = true,
    this.required = false,
    this.hintText,
    this.initFile,
    this.onRemoveFile,
    this.validator,
    this.onChanged,
  }) : super(key: key);

  final bool enabled;
  final bool required;
  final String hintText;
  final FileEntity initFile;
  final FormFieldValidator<File> validator;
  final ValueChanged<File> onChanged;
  final ValueChanged<int> onRemoveFile;

  @override
  Widget build(BuildContext context) {
    return FormField<File>(
      enabled: enabled,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      validator: validator ??
          (required
              ? (value) {
                  return value == null ? 'Vui lòng tải lên $hintText' : null;
                }
              : null),
      builder: (field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 66,
              child: DottedBorder(
                color: !field.hasError ? Color(0xffd4d4d4) : Color(0xffe81c2d),
                strokeWidth: 1,
                // padding: EdgeInsets.symmetric(
                //   horizontal: 16,
                //   vertical: 14,
                // ),
                borderType: BorderType.RRect,
                radius: Radius.circular(8),
                dashPattern: [10, 5, 10, 5, 10, 5],
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: CupertinoButton(
                        minSize: 0,
                        padding: EdgeInsets.zero,
                        onPressed: () async {
                          if (initFile != null) {
                            _showFile(context, initFile);
                          } else {
                            _showAction(context, field);
                          }
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(width: 16),
                            Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Color(0xffeaeaea),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Text(
                                    initFile?.fileName ?? field.value?.path?.split('/')?.last ?? 'Tải lên tệp tin',
                                    style: TextStyle(
                                      color: Color(0xff525252),
                                      fontSize: 14,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    maxLines: 1,
                                  ),
                                  SizedBox(height: 2),
                                  if (initFile == null)
                                  Text(
                                    field.value?.lengthSync() == null
                                        ? 'Định dạng PDF, JPG, PNG'
                                        : field.value.lengthSync() < 1000000
                                            ? 'Dung lượng: ${(field.value.lengthSync() / 1000).toStringAsFixed(1)} KB'
                                            : 'Dung lượng: ${(field.value.lengthSync() / 1000 / 1000).toStringAsFixed(1)} MB',
                                    style: TextStyle(
                                      color: Color(0xff7f7f7f),
                                      fontSize: 11,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (field.value != null || initFile != null)
                      CupertinoButton(
                        onPressed: () {
                          if (initFile != null) {
                            onRemoveFile.call(initFile.attachmentId);
                          } else {
                            field.didChange(null);
                            onChanged?.call(null);
                          }
                        },
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: SvgPicture.asset('assets/vectors/ic_trash.svg'),
                      ),
                  ],
                ),
              ),
            ),
            if (field.hasError) SizedBox(height: 10),
            if (field.hasError)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  field.errorText,
                  style: TextStyle(color: Color(0xffe81c2d), fontSize: 12),
                ),
              ),
          ],
        );
      },
    );
  }

  void _showFile(BuildContext context, FileEntity file) {
    if (!_isPdfOrImage(file.fileName)) return;
    DependantDetailsService()
        .getFile(
        '${AppConfigs.baseUrlSrqService}/v1/tax/download/${file.attachmentId}?checkSum=${file.checkSum}')
        .then((value) {
      if (value != null) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) {
              return ViewPdfPage(
                arguments: ViewPdfArgument(
                  title: file.fileName,
                  fileName: file.fileName,
                  data: value,
                  isInvoiceFile: true,
                  isShowButton: false,
                ),
              );
            },
          ),
        );
      } else {
        toast('Có lỗi sảy ra');
      }
    }, onError: (error) {
      toast(error?.toString()?.replaceAll('Exception: ', ''));
    });
  }

  bool _isPdfOrImage(String fileName) {
    final lowerCaseFileName = fileName.toLowerCase(); // Chuyển thành chữ thường
    return lowerCaseFileName.endsWith('.pdf') ||
        lowerCaseFileName.endsWith('.jpeg') ||
        lowerCaseFileName.endsWith('.jpg') ||
        lowerCaseFileName.endsWith('.png');
  }

  void _showAction(BuildContext context, FormFieldState<File> field) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            Container(
              height: 16,
              color: Colors.transparent,
            ),
            ListTile(
              leading: Icon(Icons.photo_camera_outlined),
              title: Text('Chụp ảnh'),
              onTap: () {
                _pickImage(context, field, ImageSource.camera);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.photo_library_outlined),
              title: Text('Chọn ảnh'),
              onTap: () {
                _pickImage(context, field, ImageSource.gallery);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.file_present_outlined),
              title: Text('Chọn tệp tin'),
              onTap: () {
                _pickFile(context, field);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(BuildContext context, FormFieldState<File> field, ImageSource imageSource) async {
    final _picker = ImagePicker();

    final result = await _picker.pickImage(source: imageSource, imageQuality: 100);

    if (result == null) return;


    var maxFileSizeInBytes = 5 * 1048576;

    var fileSize = await result.readAsBytes();

    if (fileSize.length > maxFileSizeInBytes) {
      toast('Ảnh không được lớn hơn 5M');
      return;
    }

    field.didChange(File(result.path));
    onChanged?.call(File(result.path));
  }

  Future<void> _pickFile(BuildContext context, FormFieldState<File> field) async {
    var resultFile = await FilePicker.platform.pickFiles(
      allowMultiple: false,
      type: FileType.custom,
      allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
    );

    var maxFileSizeInBytes = 5 * 1048576;

    if (resultFile.files.single.size > maxFileSizeInBytes) {
      toast('File không được lớn hơn 5M');
      return;
    }

    if (resultFile != null) {
      field.didChange(File(resultFile.files.single.path));
      onChanged?.call(File(resultFile.files.single.path));
    }
  }

}
