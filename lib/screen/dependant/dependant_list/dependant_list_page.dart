import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hstd/screen/dependant/cancel_registration/cancel_registration_page.dart';
import 'package:hstd/screen/dependant/dependant_additonal_file/dependant_additional_file_page.dart';
import 'package:hstd/screen/dependant/dependant_details/dependant_details_page.dart';
import 'package:hstd/screen/dependant/dependant_list/dependant_list_controller.dart';
import 'package:hstd/screen/dependant/dependant_list/dependant_list_item.dart';
import 'package:hstd/screen/dependant/dependant_registration/dependant_registration_page.dart';
import 'package:hstd/screen/dependant/models/dependant_enum.dart';
import 'package:hstd/screen/dependant/tax_reduction_register/tax_reduction_register_page.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:provider/provider.dart';

class DependantListPage extends StatelessWidget {
  const DependantListPage({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => DependantListController()..getDependantList(),
      child: Scaffold(
        backgroundColor: Colors.white,
        extendBody: true,
        appBar: AppBar(
          title: Text(
            'Danh sách giảm trừ gia cảnh',
            style: TextStyle(
              color: Colors.black,
            ),
          ),
          foregroundColor: Colors.black,
          centerTitle: true,
          backgroundColor: Colors.white,
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(0),
            child: Divider(
              height: 1,
              thickness: 1,
              color: Color(0xffe1e3e5),
            ),
          ),
          elevation: 0,
        ),

        // BODY
        body: _Body(),

        // BOTTOM ACTION
        bottomNavigationBar: _BottomAction(),
      ),
    );
  }
}

class _BottomAction extends StatelessWidget {
  const _BottomAction({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        height: 64,
        padding: EdgeInsets.symmetric(horizontal: 16),
        color: Colors.transparent,
        alignment: Alignment.centerRight,
        child: CupertinoButton(
          onPressed: () {
            final controller = context.read<DependantListController>();

            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) {
                  return ChangeNotifierProvider.value(
                    value: controller,
                    child: DependantRegistrationPage(),
                  );
                },
              ),
            );
          },
          padding: EdgeInsets.zero,
          minSize: 0,
          child: Container(
            width: 242,
            height: 48,
            decoration: BoxDecoration(
              color: Color(0xffe81c2d),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Color(0xff000000).withOpacity(0.2),
                  blurRadius: 10,
                  offset: Offset(2, 4),
                ),
              ],
            ),
            alignment: Alignment.center,
            child: Text(
              'Đăng ký người phụ thuộc',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _Body extends StatefulWidget {
  const _Body({Key key}) : super(key: key);

  @override
  State<_Body> createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  @override
  Widget build(BuildContext context) {
    final controller = context.watch<DependantListController>();

    if (controller.isLoading) {
      return LoadingDialog();
    } else if (controller.hasError) {
      return EmptyListWidget(
        onRefresh: controller.getDependantList,
        content: controller.failure.toString(),
      );
    } else if (controller.success != null) {
      final data = controller.success;

      if (data.isEmpty) {
        return EmptyListWidget(onRefresh: controller.getDependantList);
      }

      return ListView.separated(
        padding: EdgeInsets.fromLTRB(16, 16, 16, 120),
        itemCount: data.length,
        itemBuilder: (context, index) {
          final entity = data[index];

          final controller = context.read<DependantListController>();

          return DependantListItem(
            key: ValueKey(entity.id),
            entity: entity,
            onTaxRegister: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) {
                    return ChangeNotifierProvider.value(
                      value: controller,
                      child: TaxReductionRegisterPage(entity: entity, isAdd: true),
                    );
                  },
                ),
              );
            },
            onAdditionalFile: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) {
                    return ChangeNotifierProvider.value(
                      value: controller,
                      child: DependantAdditionalFilePage(entity: entity, isAdd: true),
                    );
                  },
                ),
              );
            },
            onEditRegister: () {
              // HUY GIAM TRU GIA CANH HOAC HUY DANG KY MOI HOAC HUY BO SUNG HO SO
              final controller = context.read<DependantListController>();
              var page;
              if (entity.regType == RegType.taxReg) {
                page = TaxReductionRegisterPage(entity: entity);
              } else if (entity.regType == RegType.additionalReg) {
                page = DependantAdditionalFilePage(entity: entity);
              } else {
                page = DependantRegistrationPage(id: entity.id);
              }
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) {
                    return ChangeNotifierProvider.value(
                      value: controller,
                      child: page,
                    );
                    // return ChangeNotifierProvider.value(
                    //   value: controller,
                    //   child: DependantRegistrationPage(id: entity.id),
                    // );
                  },
                ),
              );
            },
            onCancelRegister: () {
              // HUY GIAM TRU GIA CANH HOAC HUY DANG KY MOI HOAC HUY BO SUNG HO SO
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) {
                    return ChangeNotifierProvider.value(
                      value: controller,
                      child: CancelRegistrationPage(
                        id: entity.id,
                      ),
                    );
                  },
                ),
              );
            },
            onViewDetails: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) {
                    return ChangeNotifierProvider.value(
                      value: controller,
                      child: DependantDetailsPage(id: entity.id),
                    );
                  },
                ),
              );
            },
          );
        },
        separatorBuilder: (context, index) => const SizedBox(height: 16),
      );
    } else {
      return SizedBox.shrink();
    }
  }
}
