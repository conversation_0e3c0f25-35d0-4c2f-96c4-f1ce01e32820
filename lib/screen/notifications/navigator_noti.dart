import 'package:flutter/material.dart';
import 'package:hstd/screen/vacation/list_vacation_page/list_vacation_page.dart';
import 'package:hstd/screen/vacation/register_vacation_page/register_vacation_page.dart';
import 'package:hstd/screen/working_on_holiday/detail_result_working_on_holiday/detail_result_working_on_holiday_page.dart';
import 'package:hstd/screen/working_on_holiday/register_working_on_holiday/register_working_on_holiday_page.dart';
import 'package:hstd/screen/working_on_holiday/working_on_holiday_list_pages/working_on_holiday_list_pages.dart';
import '../../models/notificaton/notification_response_dto.dart';
import '../../models/survey_result_model.dart';
import '../advancemet_roadmap/register_roadmap/register_roadmap_page.dart';
import '../certification/ask_for_certification/ask_for_certification_page.dart';
import '../employee_profile/reward_employees/reward_employees_page.dart';
import '../employee_survey.dart';
import '../income_tax/income_tax_page.dart';
import '../list_contracts_appendix_screen.dart';
import '../notify_contract/notify_contract_page.dart';
import '../osh/list_osh_screen.dart';
import '../resignation/resignation_page.dart';

void navigateDetailNoti(BuildContext context, NotificationDTO notification) {
  String typeFunction = notification.typeFunction;
  switch (typeFunction) {
    ///Quản lý hợp đồng
    case 'PROBATION_CONTRACT_SIGN':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ListContractsAppendixScreen(
            arguments: ListContractsAppendixArguments(tabIndex: 0),
          ),
        ),
      );
      break;
    ///Quản lý hợp đồng
    case 'PROBATION_CONTRACT_RELEASE':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ListContractsAppendixScreen(
            arguments: ListContractsAppendixArguments(tabIndex: 0),
          ),
        ),
      );
      break;
    ///Quản lý hợp đồng
    case 'LABOR_CONTRACT_SIGN':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ListContractsAppendixScreen(
            arguments: ListContractsAppendixArguments(tabIndex: 0),
          ),
        ),
      );
      break;
  ///Quản lý hợp đồng
    case 'LABOR_CONTRACT_RELEASE':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ListContractsAppendixScreen(
            arguments: ListContractsAppendixArguments(tabIndex: 0),
          ),
        ),
      );
      break;
    ///Phụ lục cam kết
    case 'ANNEX_CONTRACT_COUNT':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ListContractsAppendixScreen(
            arguments: ListContractsAppendixArguments(tabIndex: 2),
          ),
        ),
      );
      break;
    ///Phụ lục cam kết
    case 'ANNEX_CONTRACT_RELEASE':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ListContractsAppendixScreen(
            arguments: ListContractsAppendixArguments(tabIndex: 2),
          ),
        ),
      );
      break;
    ///Phụ lục cam kết
    case 'COMMIT_SIGN':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ListContractsAppendixScreen(
            arguments: ListContractsAppendixArguments(tabIndex: 1),
          ),
        ),
      );
      break;
    ///Phụ lục cam kết
    case 'COMMIT_SIGN_COUNT':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ListContractsAppendixScreen(
            arguments: ListContractsAppendixArguments(tabIndex: 1),
          ),
        ),
      );
      break;

    ///lương thưởng
    case 'SALARY_SESSION':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => AskForCertificationPage(),
        ),
      );
      break;

    ///khảo sát nhân viên mới
    case 'EMPLOYEE_SURVEY':
      Navigator.of(context).push(
        EmployeeSurvey.route(
          Survey(typeId: 1, surveyId: notification?.objectId, typeName: "Khảo sát nhân viên mới"),
        ),
      );
      break;

    ///Đăng ký hình thức QT thuế
    case 'TAX_PAYMENT_REGISTER':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => IncomeTaxPage(
            arguments: IncomeTaxArguments(tabIndex: 0),
          ),
        ),
      );
      break;

    ///Lộ trình thăng tiến
    case 'EMPLOYEE_REGISTER':
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RegisterRoadmapPage(
            arguments: RegisterRoadmapArguments(),
          ),
        ),
      );
      break;

    ///Biên bản cảnh báo không hoàn thành nhiệm vụ
    case 'UNCOMPLETE_WORK':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          // builder: (_) => WarningTaskPage(),
          builder: (_) => NotifyContractPage(arguments: NotifyContractArguments(tabIndex: 0)),
        ),
      );
      break;

    ///Khen thưởng TCT
    case 'EMPLOYEE_LAUDATORY':
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RewardEmployeesPage(),
        ),
      );
      break;

    ///Xin giấy xác nhận thu nhập
    case 'CONFIRM_BUSSINESS_INCOME_APPROVE_TCT':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => AskForCertificationPage(
            arguments: AskForCertificationArguments(tabIndex: 1),
          ),
        ),
      );
      break;
    ///Xin giấy xác nhận thu nhập
    case 'CONFIRM_BUSSINESS_INCOME_REJECT_TCT':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => AskForCertificationPage(
            arguments: AskForCertificationArguments(tabIndex: 1),
          ),
        ),
      );
      break;
    ///Xin giấy xác nhận thu nhập
    case 'CONFIRM_BUSSINESS_INCOME_RELEASE':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => AskForCertificationPage(
            arguments: AskForCertificationArguments(tabIndex: 1),
          ),
        ),
      );
      break;
      ///  Ký sổ theo dõi ATVSLĐ
    case 'LABOR_SAFETY_CERTIFICATE':
      Navigator.push(
        context,
        OSHPage.route(),
      );
      break;
      ///Duyệt đơn xin nghỉ việc
    case 'TERMINATE_CONTRACT':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ResignationPage(
            arguments: ResignationArguments(tabIndex: 1),
          ),
        ),
      );
      break;
      ///Nghỉ việc
    case 'TERMINATE_CONTRACT_VO':
      Navigator.of(context).push(
        MaterialPageRoute<void>(
          builder: (_) => ResignationPage(
            arguments: ResignationArguments(tabIndex: 0),
          ),
        ),
      );
      break;
      ///trực lễ
    case 'EXTRA_WORK_RESULT':
      Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => DetailResultWorkingOnHolidayPage(
          workingOnHolidayId: notification?.objectId,
          onlyView: true,
        ),
      ));
      break;
    case 'EXTRA_WORK_RESULT_UPDATE':
      Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => DetailResultWorkingOnHolidayPage(
          workingOnHolidayId: notification?.objectId,
        ),
      ));
      break;
    case 'EXTRA_WORK_REGISTER':
      Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => RegisterWorkingOnHolidayPage(
          workingOnHolidayId: notification?.objectId,
          onlyView: true,
        ),
      ));
      break;
    case 'EXTRA_WORK_REGISTER_UPDATE':
      Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => RegisterWorkingOnHolidayPage(
          workingOnHolidayId: notification?.objectId,
        ),
      ));
      break;
  ///nghỉ dưỡng
    case 'TRAVEL_APPROVE_REJECT':
      Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => ListVacationPage(),
      ));
      break;
    case 'TRAVEL_APPROVE':
      Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => ListVacationPage(),
      ));
      break;
    case 'TRAVEL_REGISTER_LOCATION':
      Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => ListVacationPage(),
      ));
      break;
    case 'TRAVEL_REGISTER_TIME':
      Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => RegisterVacationPage(
            id: notification?.objectId,),
      ),);
      break;
    default:
      break;
  }
}
