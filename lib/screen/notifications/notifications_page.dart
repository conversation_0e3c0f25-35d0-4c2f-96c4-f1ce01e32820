import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:intl/intl.dart';

import '../../bloc/info_bloc.dart';
import '../../common/app_strings.dart';
import '../../configs/enum.dart';
import '../../models/notificaton/notification_request.dart';
import '../../models/notificaton/notification_response_dto.dart';
import '../../models/survey_result_model.dart';
import '../../preferences/data_center.dart';
import '../../widget/empty_list_widget.dart';
import '../../widget/loading_indicator.dart';
import '../../widget/loading_more_row_widget.dart';
import '../advancemet_roadmap/custom_widget.dart';
import '../advancemet_roadmap/register_roadmap/register_roadmap_page.dart';
import '../certification/ask_for_certification/ask_for_certification_page.dart';
import '../employee_profile/reward_employees/reward_employees_page.dart';
import '../employee_survey.dart';
import '../income_tax/income_tax_page.dart';
import '../list_contracts_appendix_screen.dart';
import '../notify_contract/notify_contract_page.dart';
import '../osh/list_osh_screen.dart';
import '../resignation/resignation_page.dart';
import 'navigator_noti.dart';
import 'notifications_cubit.dart';

class ListNotificationPage extends StatelessWidget {
  const ListNotificationPage({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return ListNotificationCubit();
      },
      child: ListNotificationChildPage(),
    );
  }
}

class ListNotificationChildPage extends StatefulWidget {
  const ListNotificationChildPage({Key key}) : super(key: key);

  @override
  State<ListNotificationChildPage> createState() => _ReportSalaryChildPageState();
}

class _ReportSalaryChildPageState extends State<ListNotificationChildPage> {
  ListNotificationCubit _cubit;
  final _scrollController = ScrollController();
  final _scrollThreshold = 200.0;

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _scrollController.addListener(_onScroll);
    getData();
  }

  void _onScroll() {
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    if (maxScroll - currentScroll <= _scrollThreshold) {
      final dataCenter = DataCenter.shared();
      _cubit.fetchNextListNotification(NotificationRequest(userReceive: dataCenter.getUserId(), page: 0, pageSize: 10));
    }
  }

  getData() {
    final dataCenter = DataCenter.shared();
    _cubit.getNotifications(NotificationRequest(userReceive: dataCenter.getUserId(), page: 0, pageSize: 10));
  }

  Future<void> _onRefresh() async {
    getData();
  }

  Future<void> updateNoti(NotificationDTO notification) async{
    await _cubit.updateNotifications(NotificationRequest(notificationId: notification?.notificationId, type: 1));
    _cubit.getNotifications(NotificationRequest(userReceive: DataCenter.shared().getUserId(), page: 0, pageSize: 1000));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(
          'Thông báo',
          style: TextStyle(
            color: Colors.black,
          ),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 1,
      ),
      backgroundColor: Color(0xffF2F2F2),
      body: BlocBuilder<ListNotificationCubit, ListNotificationState>(
        buildWhen: (previous, current) => (previous.loadStatus != current.loadStatus) || (previous.loadStatusUpdate != current.loadStatusUpdate),
        builder: (context, state) {
          if (state.loadStatus == LoadStatus.loading) {
            return LoadingIndicator();
          } else if (state.loadStatus == LoadStatus.failure || state.loadStatusUpdate == LoadStatus.failure) {
            return Column(
              children: [
                Container(
                  height: 1,
                  color: AppColors.greyRoadmap,
                ),
                Expanded(
                  child: EmptyListWidget(
                    onRefresh: () async {
                      final dataCenter = DataCenter.shared();
                      _cubit.getNotifications(NotificationRequest(userReceive: dataCenter.getUserId(), page: 0, pageSize: 1000));
                    },
                  ),
                ),
              ],
            );
          } else {
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 16, bottom: 8, left: 16, right: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${state.totalRecord} thông báo',
                        style: TextStyle(
                          color: Color(0xFFB5B4B4),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          if (state.readAll) {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) => ShowDialogNotifySuccess(
                                title: AppStrings.confirm,
                                firstContent: AppStrings.confirm_read_all_notification,
                                lastContent: '?',
                                onConfirm: () async {
                                  await _cubit.updateAll().whenComplete(() {
                                    _cubit.getNotifications(NotificationRequest(userReceive: DataCenter.shared().getUserId(), page: 0, pageSize: 1000));
                                    Navigator.of(context).pop();
                                  });
                                },
                              ),
                              barrierDismissible: false,
                            );
                          }
                        },
                        child: Text(
                          'Đọc tất cả',
                          style: TextStyle(
                            color: state.readAll ? AppColors.primaryColor : Color(0xFFB5B4B4),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Stack(
                    children: [
                      RefreshIndicator(
                        onRefresh: _onRefresh,
                        child: ListView.separated(
                          controller: _scrollController,
                          // padding: EdgeInsets.fromLTRB(16, 0, 16, 0),
                          itemCount: (state.listNotification ?? []).length,
                          separatorBuilder: (_, index) => SizedBox(height: 1),
                          itemBuilder: (context, index) {
                            var notification = state.listNotification[index];
                            return InkWell(
                              onTap: () {
                                updateNoti(notification).whenComplete(() => navigateDetailNoti(context, notification));
                              },
                              child: Container(
                                color: notification.type != 1 ? Colors.white : AppColors.backgroundItemListView,
                                padding: EdgeInsets.all(16),
                                child: Column(
                                  children: [
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        notification.type != 1
                                            ? SvgPicture.asset(AppVectors.icNotificationNew)
                                            : SvgPicture.asset(AppVectors.icNotificationOld),
                                        SizedBox(
                                          width: 12,
                                        ),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              RichText(
                                                text: TextSpan(
                                                  style: TextStyle(color: Colors.black),
                                                  children: [
                                                    TextSpan(
                                                      text: '${notification.content}',
                                                    ),
                                                    // TextSpan(
                                                    //   text: 'khảo sát nhân viên mới',
                                                    //   style: TextStyle(fontWeight: FontWeight.w600),
                                                    // ),
                                                    // TextSpan(
                                                    //   text: '. Kính mời đồng chí thực hiện',
                                                    // ),
                                                  ],
                                                ),
                                                textAlign: TextAlign.left,
                                              ),
                                              SizedBox(
                                                height: 8,
                                              ),
                                              Text(
                                                '${notification.createdDateStr}',
                                                style: TextStyle(
                                                  color: Color(0xFFB5B4B4),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: _loadMoreWidget,
                      ),
                    ],
                  ),
                )
              ],
            );
          }
        },
      ),
    );
  }

  Widget get _loadMoreWidget {
    return BlocBuilder<ListNotificationCubit, ListNotificationState>(
      bloc: _cubit,
      buildWhen: (previous, current) => previous.loadStatus != current.loadStatus,
      builder: (context, state) {
        return Visibility(
          visible: state.loadStatus == LoadStatus.loadMore,
          child: const SafeArea(
            top: false,
            child: SizedBox(
              height: 50,
              child: LoadingMoreRowWidget(),
            ),
          ),
        );
      },
    );
  }
}
