import 'dart:async';
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:formz/formz.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/models/device_request.dart';
import 'package:hstd/preferences/data_center.dart';
import 'package:hstd/screen/login/models/check_face_id.dart';
import 'package:hstd/utils/validator.dart';
import 'package:meta/meta.dart';
import 'package:hstd/models/auth_dto.dart';
import 'package:hstd/repositories/auth_service.dart';
import 'package:hstd/screen/login/models/password.dart';
import 'package:hstd/screen/login/models/username.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'login_event.dart';

part 'login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  dynamic initialState;

  LoginBloc({
    @required AuthService authService,
  })  : assert(authService != null),
        _authService = authService,
        super(LoginState());

  final AuthService _authService;

  @override
  Stream<LoginState> mapEventToState(
    LoginEvent event,
  ) async* {
    if (event is LoginUsernameChanged) {
      yield _mapUsernameChangedToState(event, state);
    } else if (event is LoginPasswordChanged) {
      yield _mapPasswordChangedToState(event, state);
    } else if (event is LoginSubmitted) {
      yield* _mapLoginSubmittedToState(event, state);
    }
  }

  LoginState _mapUsernameChangedToState(
    LoginUsernameChanged event,
    LoginState state,
  ) {
    bool showLoginByPhoneNumber = false;
    final username = Username.dirty(event.username);
    if (event.username.isEmpty && state.password.value.isEmpty) {
      showLoginByPhoneNumber = true;
    }
    return state.copyWith(
      username: username,
      status: Formz.validate([state.password, username]),
      showLoginByPhoneNumber: showLoginByPhoneNumber,
    );
  }

  LoginState _mapPasswordChangedToState(
    LoginPasswordChanged event,
    LoginState state,
  ) {
    bool showLoginByPhoneNumber = false;
    final password = Password.dirty(event.password);
    if (event.password.isEmpty && state.username.value.isEmpty) {
      showLoginByPhoneNumber = true;
    }
    return state.copyWith(
      password: password,
      status: Formz.validate([password, state.username]),
      showLoginByPhoneNumber: showLoginByPhoneNumber,
    );
  }

  Stream<LoginState> _mapLoginSubmittedToState(
    LoginSubmitted event,
    LoginState state,
  ) async* {
    if (state.status.isValidated) {
      yield state.copyWith(
          status: FormzStatus.submissionInProgress, message: null);
      try {
        final LoginRequest request =
            LoginRequest(state.username.value, state.password.value);
        LoginResponse response = await _authService.login(request);
        SharedPreferences sharedPreferences =
            await SharedPreferences.getInstance();
        await sharedPreferences.setString(
          AppConstant.KEY_TOKEN,
          response.token,
        );
        await sharedPreferences.setString(
          AppConstant.KEY_PASSWORD,
          state.password.value,
        );
        await updateFirebaseToken();
        event.deviceRequest.token = DataCenter.shared().getFireBaseToken() ?? "";
        await _authService.createDevice(event.deviceRequest);
        yield state.copyWith(status: FormzStatus.submissionSuccess);
      } catch (e) {
        if (event.isFaceId ?? false) {
          DataCenter.shared().setLoginFaceId(loginFaceIdStatus: false);
          yield state.copyWith(
              status: FormzStatus.submissionFailure,
              message: 'Mật khẩu SSO đã được thay đổi, vui lòng đăng nhập lại');
        } else {
          yield state.copyWith(
              status: FormzStatus.submissionFailure,
              message: 'Tên đăng nhập và mật khẩu không hợp lệ');
        }
      }
    } else {
      yield state.copyWith(
          status: FormzStatus.submissionFailure,
          message: 'Tên đăng nhập và mật khẩu không hợp lệ');
    }
  }

  updateFirebaseToken() async {
    try {
      await FirebaseMessaging.instance.getToken().then((value) async {
        print("FirebaseMessaging_____Token: $value");
        SharedPreferences sharedPreferences =
        await SharedPreferences.getInstance();
        await sharedPreferences.setString(
          AppConstant.FIREBASE_TOKEN,
          value,
        );
        _authService.updateToken(value);
      });
    } catch (e) {
      print('updateFirebaseToken err: $e');
    }
  }
}
