import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/models/register_vacation/voucher_model.dart';
import 'package:hstd/screen/vacation/image_voucher/image_voucher_page.dart';
import 'package:hstd/screen/vacation/register_vacation_page/register_vacation_page_cubit.dart';
import 'package:hstd/screen/vacation/widgets/select_field_widget.dart';
import 'package:hstd/screen/working_on_holiday/widget/text_field_working_holiday.dart';
import 'package:hstd/widget/primary_button.dart';

import 'select_children.dart';

class BedType {
  String name;
  int type;

  BedType({
    this.name,
    this.type,
  });
}

class ItemVoucher extends StatelessWidget {
  ItemVoucher({Key key, this.voucher, this.index, this.cubit})
      : super(key: key);
  final RegisterVacationPageCubit cubit;
  final VoucherModel voucher;
  final int index;
  // final _formKey = GlobalKey<FormState>();
  List<BedType> bedTypes = [
    BedType(name: "2 giường đơn", type: 1),
    BedType(name: "1 giường đôi", type: 2)
  ];
  Map<int, String> mapNameBed = {
    1: "2 giường đơn",
    2: "1 giường đôi",
    null: "",
  };

  @override
  Widget build(BuildContext context) {
    int valueSelected;
    TextEditingController bedTypeController =
        TextEditingController(text: mapNameBed[voucher.bedType]);
    return BlocBuilder<RegisterVacationPageCubit, RegisterVacationPageState>(
        buildWhen: (previous, current) =>
            previous.loadDataStatus != current.loadDataStatus,
        builder: (context, state) {
          return Container(
            color: Colors.white,
            margin: EdgeInsets.only(top: 8),
            child: Padding(
              padding: const EdgeInsets.all(
                16.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Voucher ${index + 1}",
                        style: AppTextStyle.blackS16W600
                            .copyWith(color: AppColors.textDateTimeCardColor),
                      ),
                      if(index > 0)
                        IconButton(onPressed: (){
                          cubit.deleteVoucher(
                            indexVoucher: index,);
                        }, icon: Icon(Icons.delete, color: AppColors.primaryColor,))
                    ],
                  ),
                  SizedBox(height: 10),
                  TextFieldWorkingHoliday(
                    title: "Người nhận phòng",
                    onChanged: (val) {
                      voucher.checkInName = val;
                    },
                    onTap: () {},
                    textCapitalization: TextCapitalization.words,
                    keyboardType: TextInputType.text,
                    controller: TextEditingController(
                        text: voucher.checkInName != null
                            ? "${voucher.checkInName}"
                            : ""),
                  ),
                  SizedBox(height: 10),
                  TextFieldWorkingHoliday(
                    title: "Số điện thoại người nhận phòng",
                    onChanged: (val) {
                      voucher.checkInPhone = val;
                    },
                    onTap: () {},
                    keyboardType: TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    maxLength: 10,
                    inputFormatter: [FilteringTextInputFormatter.digitsOnly],
                    controller: TextEditingController(
                        text: voucher.checkInPhone != null
                            ? "${voucher.checkInPhone}"
                            : ""),
                  ),
                  SizedBox(height: 10),
                  Focus(
                    onFocusChange: (hasFocus) {
                      if (!hasFocus && voucher.numberPerson != null) {
                        cubit.verify(index);
                      }
                    },
                    child: TextFieldWorkingHoliday(
                      title: "Số lượng người lớn",
                      onChanged: (val) {
                        if (val != null && val.isNotEmpty)
                          voucher.numberPerson = int.parse(val);
                      },
                      keyboardType: TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatter: [
                        FilteringTextInputFormatter.digitsOnly
                      ],
                      controller: TextEditingController(
                          text: voucher.numberPerson != null
                              ? "${voucher.numberPerson}"
                              : ""),
                    ),
                  ),
                  if (voucher.verifyNumberPerson != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0, left: 16),
                      child: Text(
                        voucher.verifyNumberPerson
                            ? "Đạt tiêu chuẩn voucher"
                            : voucher.numberPerson < 1
                            ? "Số lượng người lớn không được nhỏ hơn 1"
                            : "Ngoài tiêu chuẩn voucher (phụ thu thêm phí)",
                        style: AppTextStyle.primaryColorS13W500.copyWith(
                            fontWeight: FontWeight.w400,
                            color: voucher.verifyNumberPerson
                                ? AppColors.greenCharacter
                                : null),
                      ),
                    ),
                  SizedBox(height: 16),
                  Column(
                    children: List.generate(
                      (voucher.listDetail ?? []).length,
                          (indexChild) => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SelectChildren(
                            indexChild: indexChild,
                            yearSelected:
                            "${(voucher.listDetail ?? [])[indexChild].birthYear ?? ''}",
                            onChanged: (val) {
                              voucher.listDetail[indexChild].birthYear =
                                  val.year;
                              cubit.verifyBirthChildren(index);
                            },
                            onClear: () {
                              cubit.deleteChildren(
                                  indexVoucher: index,
                                  indexChild: indexChild);
                            },
                          ),
                          if (voucher
                              .listDetail[indexChild].isSatisfyVoucher !=
                              null)
                            Padding(
                              padding:
                              const EdgeInsets.only(top: 4.0, left: 16),
                              child: Text(
                                voucher.listDetail[indexChild]
                                    .isSatisfyVoucher
                                    ? "Đạt tiêu chuẩn voucher"
                                    : "Ngoài tiêu chuẩn voucher (phụ thu thêm phí)",
                                style: AppTextStyle.primaryColorS13W500
                                    .copyWith(
                                    fontWeight: FontWeight.w400,
                                    color: voucher.listDetail[indexChild]
                                        .isSatisfyVoucher
                                        ? AppColors.greenCharacter
                                        : AppColors.primaryColor),
                              ),
                            ),
                          SizedBox(height: 16),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 10),
                  InkWell(
                    onTap: () {
                      cubit.addChildren(index);
                    },
                    child: Row(
                      children: [
                        Icon(
                          Icons.add_circle,
                          color: AppColors.primaryColor,
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Text(
                          "Thêm trẻ em",
                          style: AppTextStyle.blackS14W500
                              .copyWith(color: AppColors.primaryColor),
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  TextFieldWorkingHoliday(
                    title: "Số serial",
                    onChanged: (val) {
                      voucher.codeVoucher = val;
                    },
                    onTap: () {},
                    keyboardType: TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatter: [FilteringTextInputFormatter.digitsOnly],
                    controller: TextEditingController(
                        text: voucher.codeVoucher != null
                            ? "${voucher.codeVoucher}"
                            : ""),
                  ),
                  SizedBox(height: 10),
                  SelectFieldWidget(
                    title: "Loại giường",
                    heightSheet: MediaQuery.of(context).size.height * 0.2,
                    valueSelect:
                    state.vacationDetail.listVoucher[index].bedType,
                    items: bedTypes
                        .map((item) => DropdownMenuItem<int>(
                      value: item.type,
                      child: Text(
                        item.name,
                        style: AppTextStyle.blackS16W400.copyWith(
                            color:
                            AppColors.textLocationVacationColor),
                      ),
                    ))
                        .toList(),
                    controller: bedTypeController,
                    onChanged: (value) {
                      valueSelected = bedTypes[value].type;
                      bedTypeController.text = bedTypes[value].name;
                      state.vacationDetail.listVoucher[index].bedType =
                          bedTypes[value].type;
                    },
                  ),
                  SizedBox(height: 10),
                  Row(
                    children: [
                      Text(
                        "Ảnh voucher",
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontStyle: FontStyle.normal),
                      ),
                      Text(
                        "*",
                        style: TextStyle(color: Colors.red),
                      ),
                    ],
                  ),
                  SizedBox(height: 5),
                  Text(
                    "Chụp ảnh đầy đủ mặt trước và mặt sau của voucher",
                    style: TextStyle(
                        fontWeight: FontWeight.w400,
                        color: AppColors.textInfoColor,
                        fontStyle: FontStyle.normal),
                  ),
                  SizedBox(height: 10),
                  ImageVoucherPage(
                    callBackFontImageVoucher: (res) =>
                        cubit.setFrontImageVoucher(res, index),
                    callBackBackImageVoucher: (res) =>
                        cubit.setBackImageVoucher(res, index),
                    urlBackImage:
                    state.vacationDetail.listVoucher[index].filePathBack,
                    urlFontImage:
                    state.vacationDetail.listVoucher[index].filePathFont,
                  ),
                  if (index == state.vacationDetail.listVoucher.length - 1)
                    Row(
                      children: [
                        PrimaryButton(
                          borderRadius: BorderRadius.circular(10),
                          suffixIcon: Icon(
                            Icons.add,
                            color: AppColors.primaryColor,
                          ),
                          title: "Thêm voucher",
                          contentPadding:
                          EdgeInsets.symmetric(horizontal: 10),
                          titleStyle: AppTextStyle.whiteColorS14W500
                              .copyWith(color: AppColors.primaryColor),
                          height: 36,
                          color: AppColors.pinkButtonLogOut,
                          onTap: () {
                            cubit.addNewVoucher();
                          },
                        ),
                        Expanded(child: Container())
                      ],
                    ),
                ],
              ),
            ),
          );
        });
  }
}
