part of 'register_vacation_page_cubit.dart';

class RegisterVacationPageState extends Equatable {
  final LoadStatus loadDataStatus, saveStatus;
  final VacationModel vacationDetail;
  final String filePathFont, filePathBack;
  final String message;
  final int verifyTimeVacation;

  const RegisterVacationPageState({
    this.loadDataStatus = LoadStatus.initial,
    this.saveStatus = LoadStatus.initial,
    this.vacationDetail,
    this.filePathFont,
    this.filePathBack,
    this.message,
    this.verifyTimeVacation,
  });

  @override
  List<Object> get props => [
    loadDataStatus,
    saveStatus,
    vacationDetail,
    filePathFont,
    filePathBack,
    message,
    verifyTimeVacation,
  ];

  RegisterVacationPageState copyWith({
    LoadStatus loadDataStatus,
    LoadStatus saveStatus,
    VacationModel vacationDetail,
    String filePathFont,
    String filePathBack,
    String message,
    int verifyTimeVacation,
  }) {
    return RegisterVacationPageState(
      loadDataStatus: loadDataStatus ?? this.loadDataStatus,
      saveStatus: saveStatus ?? this.saveStatus,
      vacationDetail: vacationDetail ?? this.vacationDetail,
      filePathFont: filePathFont ?? this.filePathFont,
      filePathBack: filePathBack ?? this.filePathBack,
      message: message ?? this.message,
      verifyTimeVacation: verifyTimeVacation ?? this.verifyTimeVacation,
    );
  }
}
