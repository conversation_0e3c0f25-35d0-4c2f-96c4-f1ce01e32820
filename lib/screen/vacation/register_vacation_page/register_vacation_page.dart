import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cupertino_datetime_picker/flutter_cupertino_datetime_picker.dart';

// import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/models/register_vacation/list_register_vacation_response_model.dart';
import 'package:hstd/screen/labor_protection/register_labor_protection_detail/widgets/show_message.dart';
import 'package:hstd/screen/vacation/widgets/select_date_time_widget.dart';
import 'package:hstd/widget/custom_picker_date.dart';
import 'package:hstd/widget/dropdown/dialog_helper.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';

import '../../income_tax/image_incometax/upload_image_income_tax_limit.dart';
import '../../working_on_holiday/widget/text_field_working_holiday.dart';
import '../widgets/preview_vacation_images.dart';
import 'register_vacation_page_cubit.dart';
import 'widget/voucher.dart';

class RegisterVacationPage extends StatelessWidget {
  const RegisterVacationPage({
    Key key,
    this.id,
  }) : super(key: key);
  final int id;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return RegisterVacationPageCubit();
      },
      child: RegisterVacationPageChildPage(
        id: id,
      ),
    );
  }
}

class RegisterVacationPageChildPage extends StatefulWidget {
  const RegisterVacationPageChildPage({Key key, this.id}) : super(key: key);
  final int id;

  @override
  State<RegisterVacationPageChildPage> createState() =>
      _RegisterVacationPageChildPageState();
}

class _RegisterVacationPageChildPageState
    extends State<RegisterVacationPageChildPage> {
  RegisterVacationPageCubit _cubit;

  DateTime startDate = DateTime.now();
  DateTime toDate = DateTime.now();
  TextEditingController startTimeController = TextEditingController();
  TextEditingController endTimeController = TextEditingController();
  List<String> requestImage = [];

  bool validateRegister(
      VacationModel vacationModel, BuildContext currentContext) {
    if (startTimeController.text.isEmptyOrNull) {
      DialogHelper.showSnackBar(currentContext,
          message: "Vui lòng chọn ngày bắt đầu nghỉ dưỡng");
      return false;
    }
    if (endTimeController.text.isEmptyOrNull) {
      DialogHelper.showSnackBar(currentContext,
          message: "Vui lòng chọn ngày kết thúc nghỉ dưỡng");
      return false;
    }
    if (startDate.millisecondsSinceEpoch >= toDate.millisecondsSinceEpoch) {
      DialogHelper.showSnackBar(currentContext,
          message: "Vui lòng chọn ngày bắt đầu trước ngày kết thúc");
      return false;
    }
    // if (toDate.millisecondsSinceEpoch - startDate.millisecondsSinceEpoch >
    //     3 * 24 * 60 * 60 * 1000) {
    //   DialogHelper.showSnackBar(currentContext,
    //       message: "Vui lòng chọn thời gian nghỉ dưỡng trong khoảng 3 ngày");
    //   return false;
    // }
    var validate = true;
    String message = "";
    (vacationModel.listVoucher ?? []).forEach((e) {
      print(e.numberPerson);
      bool selectYear = true;
      (e.listDetail ?? []).forEach((child) {
        if (child.birthYear == null) {
          selectYear = false;
          return;
        } else {
          selectYear = true;
        }
      });
      if (!selectYear) {
        // DialogHelper.showSnackBar(currentContext,
        //     message: "Vui lòng nhập năm sinh trẻ em");
        message = "Vui lòng nhập năm sinh trẻ em";
        validate = false;
        return false;
      }
      if (e.checkInName.trim().isEmptyOrNull) {
        // DialogHelper.showSnackBar(currentContext,
        //     message: "Vui lòng nhập serial voucher");
        message = "Vui lòng tên người nhận phòng";
        validate = false;
        return false;
      }
      if (e.checkInPhone.trim().isEmptyOrNull) {
        // DialogHelper.showSnackBar(currentContext,
        //     message: "Vui lòng nhập serial voucher");
        message = "Vui lòng nhập số điện thoại người nhận phòng";
        validate = false;
        return false;
      }
      if (e.numberPerson == null) {
        // DialogHelper.showSnackBar(currentContext,
        //     message: "Vui lòng nhập số lượng người lớn");
        message = "Vui lòng nhập số lượng người lớn";
        validate = false;
        return false;
      }
      if (e.numberPerson < 1) {
        // DialogHelper.showSnackBar(currentContext,
        //     message: "Vui lòng nhập số lượng người lớn");
        message = "Vui lòng nhập số lượng người lớn lơn hơn 0";
        validate = false;
        return false;
      }
      if (e.codeVoucher.isEmptyOrNull) {
        // DialogHelper.showSnackBar(currentContext,
        //     message: "Vui lòng nhập serial voucher");
        message = "Vui lòng nhập serial voucher";
        validate = false;
        return false;
      }
      if (e.bedType == null) {
        // DialogHelper.showSnackBar(currentContext,
        //     message: "Vui lòng chọn loại giường");
        message = "Vui lòng chọn loại giường";
        validate = false;
        return false;
      }
      if (e.filePathFont == null || e.filePathBack == null) {
        // DialogHelper.showSnackBar(currentContext,
        //     message: "Vui lòng chọn ảnh mặt trước và mặt sau voucher");
        message = "Vui lòng chọn ảnh mặt trước và mặt sau voucher";
        validate = false;
        return false;
      }
      validate = true;
      return true;
    });
    if (validate) {
      return true;
    } else {
      DialogHelper.showSnackBar(currentContext,
          message: message);
      return false;
    }
    // if (vacationModel.listVoucher) {
    //   DialogHelper.showSnackBar(context,
    //       message: "Vui lòng chọn ngày bắt đầu trước ngày kết thúc");
    //   return false;
    // }
    // return validateVoucher;
  }

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData(widget.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Đăng ký nghỉ dưỡng",
          style: TextStyle(color: Colors.black),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: BlocConsumer<RegisterVacationPageCubit, RegisterVacationPageState>(
          // buildWhen: (previous, current) =>
          //     previous.loadDataStatus != current.loadDataStatus,
          listenWhen: (previous, current) =>
              previous.saveStatus != current.saveStatus ||
              previous.loadDataStatus != current.loadDataStatus,
          listener: (context, state) {
            if (state.saveStatus == LoadStatus.loading) {
              LoadingDialogTransparent.show(context);
            }
            if (state.saveStatus == LoadStatus.failure) {
              LoadingDialogTransparent.hide(context);
              DialogHelper.showSnackBar(context, message: state.message);
              _cubit.clearStatus();
            }
            if (state.saveStatus == LoadStatus.success) {
              LoadingDialogTransparent.hide(context);
              _cubit.clearStatus();
              Navigator.pop(context, true);
              DialogHelper.showSnackBar(context, message: state.message);
            }
            if (state.loadDataStatus == LoadStatus.success) {
              DateFormat dateFormat = DateFormat("dd/MM/yyyy");
              startTimeController.text = state.vacationDetail.startDate;
              endTimeController.text = state.vacationDetail.endDate;
              if(state.vacationDetail.startDate != null)
              startDate = dateFormat.parse(state.vacationDetail.startDate);
              if(state.vacationDetail.endDate != null)
                toDate = dateFormat.parse(state.vacationDetail.endDate);
              _cubit.verifyTimeVacation(startDate, toDate);
            }
          },
          builder: (context, state) {
            if (state.loadDataStatus == LoadStatus.loading) {
              return LoadingIndicator();
            } else if (state.loadDataStatus == LoadStatus.failure) {
              return EmptyListWidget(
                  content: "Đã có lỗi xảy ra!",
                  onRefresh: () async {
                    _cubit.loadInitialData(widget.id);
                  });
            } else {
              return Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Container(
                            color: Colors.white,
                            margin: EdgeInsets.only(top: 2),
                            child: Padding(
                              padding: const EdgeInsets.all(
                                16.0,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                            color: Color(0xFF2D84FF)),
                                        color: Color(0XFFE9F2FF)),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 12),
                                    child: Text(
                                      "Đề nghị CBNV thực hiện đăng ký chi tiết thời gian đi nghỉ dưỡng. Sau khi TCT đã chốt danh sách, nếu CBNV có nhu cầu thay đổi/ điều chỉnh đề nghị CBNV trực tiếp liên hệ trao đổi với khách sạn qua email viettel.",
                                      style: AppTextStyle.blackS12Normal
                                          .copyWith(
                                              color: AppColors.textInfoColor,
                                              height: 1.5),
                                    ),
                                  ),
                                  SizedBox(height: 10),
                                  Text(
                                    "Thời gian nghỉ dưỡng",
                                    style: AppTextStyle.blackS16W600.copyWith(
                                        color: AppColors.textDateTimeCardColor),
                                  ),
                                  SizedBox(height: 10),
                                  Row(
                                    children: <Widget>[
                                      Expanded(
                                        child: SelectDateTimeWidget(
                                          title: "Từ ngày",
                                          tapOnly: true,
                                          onTap: () {
                                            DatePicker.showDatePicker(
                                              context,
                                              onMonthChangeStartWithFirstDate:
                                                  true,
                                              dateFormat: 'dd MMMM yyyy',
                                              pickerTheme: DateTimePickerTheme(
                                                showTitle: true,
                                                itemTextStyle: AppTextStyle
                                                    .blackTextS16NormalIncome,
                                                title: Expanded(
                                                  child: Container(
                                                    width:
                                                        MediaQuery.of(context)
                                                            .size
                                                            .width,
                                                    // color: Colors.white,
                                                    decoration: BoxDecoration(
                                                      color: Colors.white,
                                                      borderRadius:
                                                          BorderRadius.only(
                                                        topLeft:
                                                            Radius.circular(20),
                                                        topRight:
                                                            Radius.circular(20),
                                                      ),
                                                    ),
                                                    child: Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              vertical: 10),
                                                      child: Stack(
                                                        children: [
                                                          Container(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        16),
                                                            child: Align(
                                                              alignment:
                                                                  Alignment
                                                                      .topRight,
                                                              child:
                                                                  GestureDetector(
                                                                child: SvgPicture
                                                                    .asset(AppVectors
                                                                        .icCloseDialog),
                                                                onTap: () {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                },
                                                              ),
                                                            ),
                                                          ),
                                                          Center(
                                                              child: Text(
                                                                  "Tháng nghỉ dưỡng",
                                                                  style: AppTextStyle
                                                                      .blackS16Bold)),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              maxDateTime: DateTime(
                                                  state.vacationDetail
                                                      .travelYear,
                                                  state.vacationDetail
                                                          .travelMonth +
                                                      1,
                                                  0),
                                              minDateTime: DateTime(
                                                  state.vacationDetail
                                                      .travelYear,
                                                  state.vacationDetail
                                                      .travelMonth,
                                                  1),
                                              initialDateTime: DateTime.now(),
                                              // dateFormat:
                                              // AppStrings.TYPE_DD_MM_YYYY,
                                              locale: DateTimePickerLocale.vi,
                                              onChange:
                                                  (dateTime, List<int> index) {
                                                startDate = dateTime;
                                                startTimeController.text =
                                                    DateFormat(AppStrings
                                                            .TYPE_DD_MM_YYYY)
                                                        .format(startDate);
                                                _cubit.verifyTimeVacation(startDate, toDate);
                                              },
                                              onConfirm: (dateTime,
                                                  List<int> index) {},
                                            );
                                          },
                                          controller: startTimeController,
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Expanded(
                                        child: SelectDateTimeWidget(
                                          title: "Đến ngày",
                                          tapOnly: true,
                                          onTap: () {
                                            DatePicker.showDatePicker(
                                              context,
                                              onMonthChangeStartWithFirstDate:
                                                  true,
                                              dateFormat: 'dd MMMM yyyy',
                                              pickerTheme: DateTimePickerTheme(
                                                showTitle: true,
                                                itemTextStyle: AppTextStyle
                                                    .blackTextS16NormalIncome,
                                                title: Expanded(
                                                  child: Container(
                                                    width:
                                                        MediaQuery.of(context)
                                                            .size
                                                            .width,
                                                    // color: Colors.white,
                                                    decoration: BoxDecoration(
                                                      color: Colors.white,
                                                      borderRadius:
                                                          BorderRadius.only(
                                                        topLeft:
                                                            Radius.circular(20),
                                                        topRight:
                                                            Radius.circular(20),
                                                      ),
                                                    ),
                                                    child: Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              vertical: 10),
                                                      child: Stack(
                                                        children: [
                                                          Container(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        16),
                                                            child: Align(
                                                              alignment:
                                                                  Alignment
                                                                      .topRight,
                                                              child:
                                                                  GestureDetector(
                                                                child: SvgPicture
                                                                    .asset(AppVectors
                                                                        .icCloseDialog),
                                                                onTap: () {
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop();
                                                                },
                                                              ),
                                                            ),
                                                          ),
                                                          Center(
                                                              child: Text(
                                                                  "Tháng nghỉ dưỡng",
                                                                  style: AppTextStyle
                                                                      .blackS16Bold)),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              maxDateTime: DateTime(
                                                  state.vacationDetail
                                                      .travelYear,
                                                  state.vacationDetail
                                                          .travelMonth +
                                                      1,
                                                  0),
                                              minDateTime: DateTime(
                                                  state.vacationDetail
                                                      .travelYear,
                                                  state.vacationDetail
                                                      .travelMonth,
                                                  1),
                                              initialDateTime: DateTime.now(),
                                              // dateFormat:
                                              // AppStrings.TYPE_DD_MM_YYYY,
                                              locale: DateTimePickerLocale.vi,
                                              onChange:
                                                  (dateTime, List<int> index) {
                                                toDate = dateTime;
                                                endTimeController.text =
                                                    DateFormat(AppStrings
                                                            .TYPE_DD_MM_YYYY)
                                                        .format(toDate);
                                                _cubit.verifyTimeVacation(startDate, toDate);
                                              },
                                              onConfirm: (dateTime,
                                                  List<int> index) {},
                                            );
                                          },
                                          controller: endTimeController,
                                        ),
                                      ),
                                    ],
                                  ),
                                  // SizedBox(height: 10),
                                  (state.verifyTimeVacation ?? 0) == 0?SizedBox():
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        top: 4.0, left: 0),
                                    child: Text(
                                      state.verifyTimeVacation == 1
                                          ? "Đạt tiêu chuẩn voucher"
                                          : "Ngoài tiêu chuẩn voucher (phụ thu thêm phí)",
                                      style: AppTextStyle
                                          .primaryColorS13W500
                                          .copyWith(
                                              fontWeight: FontWeight.w400,
                                              color:
                                                  state.verifyTimeVacation ==
                                                          1
                                                      ? AppColors
                                                          .greenCharacter
                                                      : null),
                                    ),
                                  ),
                                  SizedBox(height: 10),
                                  TextFieldWorkingHoliday(
                                    title: "Địa điểm nghỉ dưỡng",
                                    controller: TextEditingController(
                                        text: state.vacationDetail?.location ??
                                            ""),
                                    readOnly: true,
                                  ),
                                  SizedBox(height: 10),
                                ],
                              ),
                            ),
                          ),
                          Column(
                            children: List.generate(
                                (state.vacationDetail?.listVoucher ?? [])
                                    .length, (index) {
                              var voucher =
                                  state.vacationDetail?.listVoucher[index];
                              return ItemVoucher(
                                index: index,
                                cubit: _cubit,
                                voucher: voucher,
                              );
                            }),
                          )
                        ],
                      ),
                    ),
                  ),
                  SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 8),
                      child: PrimaryButton(
                        borderRadius: BorderRadius.circular(10),
                        title: "Xác nhận",
                        contentPadding: EdgeInsets.symmetric(horizontal: 10),
                        titleStyle: AppTextStyle.whiteS16W500,
                        color: AppColors.primaryColor,
                        onTap: () async {
                          if (validateRegister(state.vacationDetail, context)) {
                            state.vacationDetail.startDate =
                                startTimeController.text;
                            state.vacationDetail.endDate = endTimeController.text;
                            _cubit.saveRegisterVacation();
                          }
                        },
                      ),
                    ),
                  ),
                ],
              );
            }
          }),
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }
}
