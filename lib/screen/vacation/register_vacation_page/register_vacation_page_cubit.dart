import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hstd/bloc/view_file_bloc.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/models/register_vacation/list_register_vacation_response_model.dart';
import 'package:hstd/models/register_vacation/voucher_model.dart';
import 'package:hstd/repositories/register_vacation_service.dart';

part 'register_vacation_page_state.dart';

class RegisterVacationPageCubit extends Cubit<RegisterVacationPageState> {
  RegisterVacationPageCubit() : super(const RegisterVacationPageState());
  RegisterVacationService workingOnHolidayService =
      RegisterVacationServiceImp();
  ViewFileBloc viewFileFrontBloc = new ViewFileBloc();
  ViewFileBloc viewFileBackBloc = new ViewFileBloc();

  Future<void> loadInitialData(int id) async {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));
    try {
      //Todo: add API calls
      var result = await workingOnHolidayService.getDetailTravelRegister(id: id);

      if (result != null && result.status == 1) {
        var vacation = VacationModel.fromJson(result.data);
        if(vacation.listVoucher == null)
        vacation.listVoucher = [VoucherModel(numberPerson: vacation.numberPerson)];
        emit(state.copyWith(
            loadDataStatus: LoadStatus.success,
            vacationDetail: vacation));
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
        ));
      }
    } catch (e, s) {
      //Todo: should print exception here
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }

  void setFrontImageVoucher(FileData fileData, int voucherIndex) {
    if (fileData != null) {
      var vacationDetail = state.vacationDetail;
      vacationDetail?.listVoucher[voucherIndex].filePathFont = fileData.filePath;
      vacationDetail?.listVoucher[voucherIndex].filePathFontName = fileData.fileName;
      updateVacationDetail(vacationDetail);
    }
  }

  void setBackImageVoucher(FileData fileData, int voucherIndex) {
    if (fileData != null) {
      var vacationDetail = state.vacationDetail;
      vacationDetail?.listVoucher[voucherIndex].filePathBack = fileData.filePath;
      vacationDetail?.listVoucher[voucherIndex].filePathBackName = fileData.fileName;
      updateVacationDetail(vacationDetail);
    }
  }

  void addNewVoucher() {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));
    var vacationDetail = state.vacationDetail;
    List<VoucherModel> listVoucher = vacationDetail?.listVoucher ?? [];
    listVoucher.add(VoucherModel(numberPerson: vacationDetail.numberPerson));
    vacationDetail?.listVoucher = listVoucher;

    updateVacationDetail(vacationDetail);
  }

  void addChildren(int index) {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));
    var vacationDetail = state.vacationDetail;
    List<ListDetail> listChild =
        vacationDetail?.listVoucher[index].listDetail ?? [];
    listChild.add(ListDetail());
    vacationDetail?.listVoucher[index].listDetail = listChild;

    verifyBirthChildren(index);
  }

  void verify(int index) {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));
    var vacationDetail = state.vacationDetail;
    if (vacationDetail.listVoucher[index].numberPerson < 1 || vacationDetail.listVoucher[index].numberPerson > vacationDetail.numberPerson)
      vacationDetail.listVoucher[index].verifyNumberPerson = false;
    else
      vacationDetail.listVoucher[index].verifyNumberPerson = true;
    updateVacationDetail(vacationDetail);
  }

  void updateVacationDetail(VacationModel newVacationDetail){
    emit(
      state.copyWith(
        vacationDetail: newVacationDetail,
        loadDataStatus: LoadStatus.success,
      ),
    );
  }

  void deleteChildren({int indexVoucher, int indexChild}) {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));
    var vacationDetail = state.vacationDetail;
    List<ListDetail> listChild =
        vacationDetail?.listVoucher[indexVoucher].listDetail ?? [];
    listChild.removeAt(indexChild);
    vacationDetail?.listVoucher[indexVoucher].listDetail = listChild;

    verifyBirthChildren(indexVoucher);
  }

  void verifyBirthChildren(int index) {
    var yearNow = DateTime.now().year;
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));
    var vacationDetail = state.vacationDetail;
    int countChildTwelve = 0;
    int countChildSix = 0;
    vacationDetail.listVoucher[index].listDetail.forEach((element) {
      if (element.birthYear != null) element.isSatisfyVoucher = false;
      if (countChildSix < (vacationDetail.numberChildSix ?? 0) &&
          element.birthYear != null &&
          (yearNow - element.birthYear < 6)) {
        countChildSix++;
        element.isSatisfyVoucher = true;
      } else if (countChildTwelve < (vacationDetail.numberChildTwelve ?? 0) &&
          element.birthYear != null &&
          (yearNow - element.birthYear < 12)) {
        countChildTwelve++;
        element.isSatisfyVoucher = true;
      }
    });
    updateVacationDetail(vacationDetail);
  }

  Future<void> saveRegisterVacation() async {
    emit(state.copyWith(saveStatus: LoadStatus.loading));
    try {
      //Todo: add API calls
      var result = await workingOnHolidayService.saveRegisterTime(
          request: state.vacationDetail);
      if (result  != null) {
        emit(state.copyWith(
            saveStatus: LoadStatus.success, message: result.message));
      } else {
        emit(state.copyWith(
            saveStatus: LoadStatus.failure, message: result.message));
      }
    } catch (e, s) {
      //Todo: should print exception here
      emit(state.copyWith(
          saveStatus: LoadStatus.failure, message: "Có lỗi xảy ra"));
    }
  }

  void verifyTimeVacation(DateTime startDate, DateTime endDate) {
    int verify = 0;
    /// 1: thoa man dieu kien
    /// 2: ko thoa man dieu kien
    /// 0: ko hien thi
    if (endDate.millisecondsSinceEpoch > startDate.millisecondsSinceEpoch) {
      if (endDate.difference(startDate).inDays > 2) {
        verify = 2;
      } else {
        verify = 1;
      }
    } else {
      verify = 0;
    }
    emit(state.copyWith( verifyTimeVacation: verify));
  }

  void clearStatus() {
    emit(state.copyWith(
        saveStatus: LoadStatus.initial, loadDataStatus: LoadStatus.initial));
  }

  void deleteVoucher({int indexVoucher}) {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));
    var vacationDetail = state.vacationDetail;
    List<VoucherModel> listVoucher =
        vacationDetail?.listVoucher ?? [];
    listVoucher.removeAt(indexVoucher);
    vacationDetail?.listVoucher = listVoucher;
    updateVacationDetail(vacationDetail);
  }
}
