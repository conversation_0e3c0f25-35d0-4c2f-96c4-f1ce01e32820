import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_vectors.dart';

class SelectDateTimeWidget extends StatelessWidget {
  const SelectDateTimeWidget(
      {Key key,
      this.title,
      this.tapOnly = false,
      this.controller,
      this.onTap,
      this.readOnly = false})
      : super(key: key);
  final Function onTap;
  final String title;
  final TextEditingController controller;
  final bool readOnly;
  final tapOnly;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        color: readOnly ? AppColors.greyRoadmap : Colors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(10),
        ),
        border: Border.all(
          color: AppColors.greyRoadmap,
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        readOnly: readOnly || tapOnly,
        onTap: () async {
          onTap();
        },
        decoration: InputDecoration(
          label: Container(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: title,
                    style: TextStyle(
                      color: AppColors.borderColor,
                    ),
                  ),
                  TextSpan(text: ' *', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.fromLTRB(
            10.0,
            0.0,
            20.0,
            0.0,
          ),
          suffixIcon: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 10),
                child: SvgPicture.asset(
                  AppVectors.icCalendar2,
                  color: AppColors.borderColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
