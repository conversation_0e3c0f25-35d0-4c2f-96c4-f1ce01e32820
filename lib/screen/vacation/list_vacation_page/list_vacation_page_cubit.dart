import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/register_vacation/list_register_vacation_response_model.dart';
import 'package:hstd/repositories/register_vacation_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'list_vacation_page_state.dart';

class ListVacationPageCubit extends Cubit<ListVacationPageState> {

  ListVacationPageCubit() : super(const ListVacationPageState());
  RegisterVacationService workingOnHolidayService = RegisterVacationServiceImp();

  Future<void> loadInitialData() async {
      emit(state.copyWith(loadDataStatus: LoadStatus.initial));
      SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
      String employeeCode = sharedPreferences.get(AppConstant.KEY_USER_CODE);
      try {
        //Todo: add API calls
        var result =
        await workingOnHolidayService.getAllRegisterVacation(employeeCode: employeeCode);
        if (result != null) {

          emit(state.copyWith(
            loadDataStatus: LoadStatus.success,
            listVacation:  result.data
          ));
        } else {
          emit(state.copyWith(
            loadDataStatus: LoadStatus.failure,
          ));
        }
      } catch (e, s) {
        //Todo: should print exception here
        emit(state.copyWith(loadDataStatus: LoadStatus.failure));
      }
    }
}
