part of 'list_vacation_page_cubit.dart';

class ListVacationPageState extends Equatable {
  final LoadStatus loadDataStatus;
  final List<VacationModel> listVacation;

  const ListVacationPageState({
    this.loadDataStatus = LoadStatus.initial,
    this.listVacation,
  });

  @override
  List<Object> get props => [
        loadDataStatus,
    listVacation,
      ];

  ListVacationPageState copyWith({
    LoadStatus loadDataStatus,
    List<VacationModel> listVacation
  }) {
    return ListVacationPageState(
      loadDataStatus: loadDataStatus ?? this.loadDataStatus,
      listVacation: listVacation ?? this.listVacation,
    );
  }
}
