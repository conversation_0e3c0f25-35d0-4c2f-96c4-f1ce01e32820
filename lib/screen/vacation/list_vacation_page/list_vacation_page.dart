import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/screen/vacation/confirm_register_vacation/confirm_register_vacation_page.dart';
import 'package:hstd/screen/vacation/detail_register_vacation/detail_register_vacation_page.dart';
import 'package:hstd/screen/vacation/register_location_vacation/register_location_vacation_page.dart';
import 'package:hstd/screen/vacation/register_vacation_page/register_vacation_page.dart';
import 'package:hstd/screen/vacation/sent_email_to_hotel/sent_email_to_hotel_page.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:hstd/widget/primary_button.dart';

import '../../../common/app_text_styles.dart';
import '../../../common/app_vectors.dart';
import 'list_vacation_page_cubit.dart';

class ListVacationPage extends StatelessWidget {
  const ListVacationPage({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return ListVacationPageCubit();
      },
      child: const ListVacationPageChildPage(),
    );
  }
}

class ListVacationPageChildPage extends StatefulWidget {
  const ListVacationPageChildPage({Key key}) : super(key: key);

  @override
  State<ListVacationPageChildPage> createState() =>
      _ListVacationPageChildPageState();
}

class _ListVacationPageChildPageState extends State<ListVacationPageChildPage> {
  ListVacationPageCubit _cubit;

  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData();
  }

  @override
  Widget build(BuildContext context) {
    const _textStatusTrueStyle = TextStyle(
      color: Color(0xFF39B54A),
      fontSize: 14,
      fontWeight: FontWeight.w500,
    );
    const _textStatusFalseStyle = TextStyle(
      color: Colors.red,
      fontSize: 14,
      fontWeight: FontWeight.w500,
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Đăng ký nghỉ dưỡng",
          style: TextStyle(color: Colors.black),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: BlocBuilder<ListVacationPageCubit, ListVacationPageState>(
          buildWhen: (previous, current) =>
              previous.loadDataStatus != current.loadDataStatus,
          builder: (context, state) {
            if (state.loadDataStatus == LoadStatus.loading) {
              return LoadingIndicator();
            } else if (state.loadDataStatus == LoadStatus.failure) {
              return EmptyListWidget(
                  content: "Đã có lỗi xảy ra!",
                  onRefresh: () async {
                    _cubit.loadInitialData();
                  });
            } else {
              if ((state.listVacation ?? []).isNotEmpty) {
                return SafeArea(
                  child: RefreshIndicator(
                    onRefresh: () async {
                      _cubit.loadInitialData();
                    },
                    child: ListView.separated(
                        controller: _scrollController,
                        padding: EdgeInsets.only(top: 8, bottom: 60),
                        itemCount: (state.listVacation ?? []).length,
                        separatorBuilder: (_, index) => SizedBox(),
                        itemBuilder: (context, index) {
                          return GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      DetailRegisterVacationPage(
                                    arguments: DetailRegisterVacationArguments(
                                        state.listVacation[index].id),
                                  ),
                                ),
                              );
                            },
                            child: Card(
                              color: Colors.white,
                              shape: RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10))),
                              margin: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 10),
                              child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  child: Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            "Năm ${state.listVacation[index].travelYear}",
                                            style: TextStyle(
                                                fontWeight: FontWeight.w500,
                                                fontStyle: FontStyle.normal),
                                          ),
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 12, vertical: 3),
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                                color: state.listVacation[index]
                                                    .vacationStatusBackgroundColor),
                                            child: Text(
                                              state.listVacation[index]
                                                      .vacationStatusStr ??
                                                  '',
                                              style: _textStatusTrueStyle.copyWith(
                                                  color: state
                                                      .listVacation[index]
                                                      .vacationStatusTextColor),
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Row(
                                        children: [
                                          Text(
                                            "Hạn đăng ký: ",
                                            style: TextStyle(fontSize: 14),
                                          ),
                                          Text(
                                            state.listVacation[index]
                                                    .registerEndDate ??
                                                '',
                                            style:
                                                AppTextStyle.blackColorS14W500,
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10),
                                      Divider(
                                        height: 1,
                                        thickness: 0.5,
                                        color:
                                            AppColors.textDateTimeDividerColor,
                                      ),
                                      SizedBox(height: 10),
                                      if (state.listVacation[index]
                                              .locationStatus ==
                                          2) ...[
                                        contentCard(
                                            icon: AppVectors.ic_close_type_3,
                                            content: "Không đăng ký"),
                                        SizedBox(height: 10),
                                        contentCard(
                                            icon: AppVectors.ic_calendar3,
                                            content: state.listVacation[index]
                                                    .noRegisterReason ??
                                                ""),
                                        SizedBox(height: 10),
                                      ],
                                      if (state.listVacation[index]
                                              .locationStatus ==
                                          1) ...[
                                        contentCard(
                                            icon: AppVectors.ic_location,
                                            content: state.listVacation[index]
                                                    .location ??
                                                ""),
                                        SizedBox(height: 10),
                                        contentCard(
                                            icon: AppVectors.ic_calendar_type_4,
                                            content:
                                                "${state.listVacation[index].travelMonth}/${state.listVacation[index].travelYear}"),
                                        if ((state.listVacation[index].countVoucher ?? 0) > 0 ) ...[
                                          SizedBox(height: 10),
                                          contentCard(
                                              icon: AppVectors.ic_voucher,
                                              content:
                                                  "${state.listVacation[index].countVoucher} voucher"),
                                        ]
                                      ],
                                      SizedBox(
                                        height: 8,
                                      ),
                                      if (state
                                                  .listVacation[index]
                                                  .registerEndDateTime
                                                  .millisecondsSinceEpoch >
                                              DateTime.now()
                                                  .millisecondsSinceEpoch &&
                                          state.listVacation[index]
                                                  .locationStatus ==
                                              0 &&
                                          state.listVacation[index]
                                                  .registerLock !=
                                              1)
                                        PrimaryButton(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          title: "Đăng ký địa điểm",
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 10),
                                          titleStyle:
                                              AppTextStyle.whiteColorS14W500,
                                          height: 40,
                                          color: AppColors.primaryColor,
                                          onTap: () async {
                                            var result = await Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    ConfirmRegisterVacationPage(
                                                        periodId: state
                                                            .listVacation[index]
                                                            .periodId,
                                                        id: state
                                                            .listVacation[index]
                                                            .id),
                                              ),
                                            );
                                            if (result ?? false) {
                                              _cubit.loadInitialData();
                                            }
                                          },
                                        ),
                                      if (state.listVacation[index]
                                                  .locationStatus ==
                                              1 &&
                                          state.listVacation[index]
                                                  .timeStatus ==
                                              0)
                                        PrimaryButton(
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          title: "Chọn ngày đi nghỉ dưỡng",
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 10),
                                          titleStyle:
                                              AppTextStyle.whiteColorS14W500,
                                          height: 40,
                                          color: AppColors.primaryColor,
                                          onTap: () async {
                                            var result = await Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    RegisterVacationPage(
                                                        id: state
                                                            .listVacation[index]
                                                            .id),
                                              ),
                                            );
                                            if (result ?? false) {
                                              _cubit.loadInitialData();
                                            }
                                          },
                                        ),
                                      if (state.listVacation[index]
                                                  .timeStatus ==
                                              1 ||
                                          (state.listVacation[index]
                                                      .locationStatus ==
                                                  1 &&
                                              state.listVacation[index]
                                                      .timeStatus ==
                                                  null &&
                                              state.listVacation[index]
                                                      .registerLock !=
                                                  1) ||
                                          (state.listVacation[index]
                                                      .locationStatus ==
                                                  2 &&
                                              state.listVacation[index]
                                                      .approveStatus ==
                                                  2))
                                        Row(
                                          children: [
                                            Expanded(
                                              child: PrimaryButton(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                title: "Chỉnh sửa",
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        horizontal: 10),
                                                titleStyle:
                                                    AppTextStyle.blackS14W500,
                                                height: 40,
                                                color:
                                                    AppColors.greyButtonColor,
                                                onTap: () async {
                                                  if ((state.listVacation[index]
                                                                  .locationStatus ==
                                                              1 &&
                                                          state
                                                                  .listVacation[
                                                                      index]
                                                                  .timeStatus ==
                                                              null) ||
                                                      ((state
                                                                  .listVacation[
                                                                      index]
                                                                  .locationStatus ==
                                                              2 &&
                                                          state
                                                                  .listVacation[
                                                                      index]
                                                                  .approveStatus ==
                                                              2))) {
                                                    state.listVacation[index]
                                                            .registerId =
                                                        state
                                                            .listVacation[index]
                                                            .id;
                                                    var result =
                                                        await Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) =>
                                                            RegisterLocationVacationPage(
                                                                initVacation:
                                                                    state.listVacation[
                                                                        index],
                                                                isEdit: true),
                                                      ),
                                                    );
                                                    if (result ?? false) {
                                                      _cubit.loadInitialData();
                                                    }
                                                  } else
                                                    Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) =>
                                                            RegisterVacationPage(
                                                                id: state
                                                                    .listVacation[
                                                                        index]
                                                                    .id),
                                                      ),
                                                    );
                                                },
                                              ),
                                            ),
                                            if (state.listVacation[index]
                                                    .timeStatus ==
                                                1)
                                              SizedBox(
                                                width: 10,
                                              ),
                                            if (state.listVacation[index]
                                                        .timeStatus ==
                                                    1 &&
                                                DateTime.now().year <=
                                                    state.listVacation[index]
                                                        .travelYear)
                                              Expanded(
                                                child: PrimaryButton(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                  title: "Gửi mail cho KS",
                                                  contentPadding:
                                                      EdgeInsets.symmetric(
                                                          horizontal: 10),
                                                  titleStyle: AppTextStyle
                                                      .whiteColorS14W500,
                                                  height: 40,
                                                  color: AppColors.primaryColor,
                                                  onTap: () async {
                                                    Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) =>
                                                            SentEmailToHotelPage(
                                                                id: state
                                                                    .listVacation[
                                                                        index]
                                                                    .id),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              )
                                          ],
                                        ),
                                    ],
                                  )),
                            ),
                          );
                        }),
                  ),
                );
              } else {
                return Stack(
                  children: [
                    EmptyListWidget(
                      content: "Không có văn bản nào",
                      onRefresh: () async {
                        _cubit.loadInitialData();
                      },
                    ),
                  ],
                );
              }
            }
          }),
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  Widget contentCard(
      {String icon, String content, TextStyle contentStyle, Function onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        color: Colors.white,
        child: Row(
          children: [
            SvgPicture.asset(icon),
            SizedBox(width: 10),
            Expanded(
              child: Text(
                content,
                style: contentStyle,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
