import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_images.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/screen/vacation/register_location_vacation/register_location_vacation_page.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:hstd/widget/primary_button.dart';

import 'confirm_register_vacation_cubit.dart';
class NavigationService {
  static GlobalKey<NavigatorState> navigatorKey =
  GlobalKey<NavigatorState>();
}

class ConfirmRegisterVacationPage extends StatelessWidget {
  const ConfirmRegisterVacationPage({
    Key key,
    this.periodId,
    this.id,
  }) : super(key: key);
  final int periodId;
  final int id;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return ConfirmRegisterVacationCubit();
      },
      child: ConfirmRegisterVacationChildPage(periodId: periodId, id: id,),
    );
  }
}

class ConfirmRegisterVacationChildPage extends StatefulWidget {
  const ConfirmRegisterVacationChildPage({Key key, this.periodId, this.id})
      : super(key: key);
  final int periodId;
  final int id;

  @override
  State<ConfirmRegisterVacationChildPage> createState() =>
      _ConfirmRegisterVacationChildPageState();
}

class _ConfirmRegisterVacationChildPageState
    extends State<ConfirmRegisterVacationChildPage> {
  ConfirmRegisterVacationCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData(widget.periodId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: NavigationService.navigatorKey,
      backgroundColor: Colors.white,
      body: BlocBuilder<ConfirmRegisterVacationCubit,
              ConfirmRegisterVacationState>(
          buildWhen: (previous, current) =>
              previous.loadDataStatus != current.loadDataStatus,
          builder: (context, state) {
            if (state.loadDataStatus == LoadStatus.loading) {
              return LoadingIndicator();
            } else if (state.loadDataStatus == LoadStatus.failure) {
              return EmptyListWidget(
                  content: "Đã có lỗi xảy ra!",
                  onRefresh: () async {
                    _cubit.loadInitialData(widget.periodId);
                  });
            } else {
              return SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100),
                            color: AppColors.greyBackButtonColor,
                          ),
                          child: Icon(
                            Icons.arrow_back_ios,
                            size: 18,
                          ),
                        ),
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 8,
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 30.0),
                                child: Image.asset(
                                  AppImages.imgVacation,
                                ),
                              ),
                              SizedBox(
                                height: 24,
                              ),
                              Align(
                                alignment: Alignment.center,
                                child: Column(
                                  children: [
                                    Text(
                                      "Đăng ký nghỉ dưỡng năm ${state.vacationDetail.travelYear ?? ""}",
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 20),
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    Text.rich(
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                            text:
                                                "Đề nghị CBNV đăng ký trước ngày ",
                                            style: TextStyle(
                                                color: AppColors.textInfoColor,
                                                fontSize: 14,
                                                fontWeight: FontWeight.w400),
                                          ),
                                          TextSpan(
                                              text: state.vacationDetail.registerEndDate ?? "",
                                              style: TextStyle(
                                                  color: AppColors.primaryColor,
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400)),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 24,
                              ),
                              Text(
                                "Địa điểm nghỉ dưỡng",
                                style: AppTextStyle.blackS16W600,
                              ),
                              SizedBox(
                                height: 12,
                              ),
                              Column(
                                children: List.generate(
                                    (state.vacationDetail?.listDetail ?? [])
                                        .length,
                                    (index) => locationVacation(state
                                        .vacationDetail
                                        ?.listDetail[index]
                                        .hotelName)),
                              ),
                              SizedBox(
                                height: 16,
                              ),
                              Text(
                                "Trình tự đăng ký",
                                style: AppTextStyle.blackS16W600,
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  registrationProcess("1",
                                      "Cá nhân thực hiện đăng ký địa điểm và tháng nghỉ dưỡng"),
                                  SizedBox(
                                    height: 8,
                                  ),
                                  registrationProcess("2",
                                      "TCT cấp voucher, cá nhân thực hiện đăng ký thời gian nghỉ dưỡng và người thân đi cùng"),
                                  SizedBox(
                                    height: 8,
                                  ),
                                  registrationProcess("3",
                                      "Cá nhân gọi điện check phòng Khách sạn, sau đó gửi email đăng ký đặt phòng"),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 24,
                      ),
                      PrimaryButton(
                        borderRadius: BorderRadius.circular(10),
                        title: "Thực hiện",
                        contentPadding: EdgeInsets.symmetric(horizontal: 10),
                        titleStyle: AppTextStyle.blackS16W500
                            .copyWith(color: Colors.white),
                        height: 48,
                        color: AppColors.primaryColor,
                        onTap: () {
                          state.vacationDetail.registerId = widget.id;
                          state.vacationDetail.periodId = widget.periodId;
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  RegisterLocationVacationPage(initVacation: state.vacationDetail,),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              );
            }
          }),
    );
  }

  Widget locationVacation(String location) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100),
              color: AppColors.primarySurfaceColor,
            ),
            child: Icon(Icons.check, color: AppColors.primaryColor, size: 10),
          ),
          SizedBox(
            width: 5,
          ),
          Expanded(
            child: Text(
              "$location",
              style: AppTextStyle.blackS14W400
                  .copyWith(color: AppColors.textLocationVacationColor),
            ),
          )
        ],
      ),
    );
  }

  Widget registrationProcess(String index, String content) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: "Bước $index: ",
            style: AppTextStyle.blackS14W400.copyWith(
              color: AppColors.textLocationVacationColor,
              decoration: TextDecoration.underline,
            ),
          ),
          TextSpan(
            text: "$content",
            style: AppTextStyle.blackS14W400.copyWith(
                color: AppColors.textLocationVacationColor, height: 1.5),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }
}
