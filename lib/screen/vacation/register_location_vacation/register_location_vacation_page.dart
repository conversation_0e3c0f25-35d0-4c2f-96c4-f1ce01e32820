import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:another_flushbar/flushbar.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cupertino_datetime_picker/flutter_cupertino_datetime_picker.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/register_vacation/list_register_vacation_response_model.dart';
import 'package:hstd/models/register_vacation/save_location_request.dart';
import 'package:hstd/screen/income_tax/widget/dialog_delete.dart';
import 'package:hstd/screen/information_salary/payment_periods_view_pdf.dart';
import 'package:hstd/screen/vacation/confirm_register_vacation/confirm_register_vacation_page.dart';
import 'package:hstd/screen/vacation/widgets/select_date_time_widget.dart';
import 'package:hstd/widget/dropdown/dialog_helper.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:intl/intl.dart';

import '../widgets/select_field_widget.dart';
import 'register_location_vacation_cubit.dart';

class RegisterLocationVacationPage extends StatelessWidget {
  const RegisterLocationVacationPage({
    Key key,
    this.initVacation,
    this.isEdit,
  }) : super(key: key);
  final VacationModel initVacation;
  final bool isEdit;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return RegisterLocationVacationCubit();
      },
      child: RegisterLocationVacationChildPage(
        initVacation: initVacation,
        isEdit: isEdit,
      ),
    );
  }
}

class RegisterLocationVacationChildPage extends StatefulWidget {
  const RegisterLocationVacationChildPage(
      {Key key, this.initVacation, this.isEdit})
      : super(key: key);
  final VacationModel initVacation;
  final bool isEdit;

  @override
  State<RegisterLocationVacationChildPage> createState() =>
      _RegisterLocationVacationChildPageState();
}

class _RegisterLocationVacationChildPageState
    extends State<RegisterLocationVacationChildPage> {
  RegisterLocationVacationCubit _cubit;
  int valueLocationSelect;
  int valueRegionSelect;
  TextEditingController dateTimeController = TextEditingController();
  TextEditingController locationController = TextEditingController();
  TextEditingController reasonController = TextEditingController();
  List<String> requestImage = [];
  DateTime dateTimeMonth = DateTime.now();

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData(widget.initVacation.periodId);
    if(widget.isEdit ?? false)
    _cubit.loadDetailData(widget.initVacation.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text(
            "Đăng ký địa điểm nghỉ dưỡng",
            style: TextStyle(color: Colors.black),
          ),
          foregroundColor: Colors.black,
          centerTitle: true,
          backgroundColor: Colors.white,
          elevation: 0,
        ),
        body: BlocConsumer<RegisterLocationVacationCubit,
            RegisterLocationVacationState>(
          listenWhen: (previous, current) =>
              previous.loadDataStatus != current.loadDataStatus ||
              previous.loadInitData != current.loadInitData ||
              previous.saveStatus != current.saveStatus,
          listener: (context, state) {
            if (state.saveStatus == LoadStatus.loading) {
              LoadingDialogTransparent.show(context);
            }
            if (state.saveStatus == LoadStatus.failure) {
              LoadingDialogTransparent.hide(context);
              _cubit.clearSaveStatus();
              DialogHelper.showSnackBar(context, snackBarStatus: SnackBarStatus.error, message: "Có lỗi xảy ra");
            }
            if (state.saveStatus == LoadStatus.success) {
              LoadingDialogTransparent.hide(context);
              _cubit.clearSaveStatus();
              Navigator.pop(context, true);
              if (!(widget.isEdit ?? false))
                Navigator.pop(
                    NavigationService.navigatorKey.currentContext, true);
              DialogHelper.showSnackBar(context, snackBarStatus: SnackBarStatus.success, message: "Thành công");
            }
            if (state.loadDataStatus == LoadStatus.success && state.loadInitData == LoadStatus.success) {
              _cubit.clearSaveStatus();
              if (widget.isEdit ?? false) {
                if(state.vacationDetail.locationStatus == 1){
                  _cubit.setValueRegister(value: 1);
                  locationController.text = state.vacationDetail.location;
                  valueLocationSelect = (state.vacationInit?.listDetail ?? []).firstWhere((element) => element.hotelName == state.vacationDetail.location).id;
                  dateTimeController.text = DateFormat(AppStrings.TYPE_MM_YYYY)
                      .format(DateTime(state.vacationDetail.travelYear,
                      state.vacationDetail.travelMonth));
                }
                if(state.vacationDetail.locationStatus == 2){
                  _cubit.setValueRegister(value: 2);
                  reasonController.text = state.vacationDetail.noRegisterReason;
                  valueRegionSelect = ReasonsNotRegisteringEnum.values.firstWhere((item)=>item.display == state.vacationDetail.noRegisterReason).keyToServer;
                  _cubit.setImageData(state.vacationDetail.filePath);
                }
              }
            }
          },
          builder: (context, state) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Container(
                        color: Colors.white,
                        margin: EdgeInsets.only(top: 2),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Đăng ký nghỉ dưỡng",
                              style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontStyle: FontStyle.normal),
                            ),
                            RadioListTile(
                              value: 1,
                              groupValue: state.registerValue,
                              onChanged: (val) {
                                _cubit.setValueRegister(value: val);
                              },
                              activeColor: AppColors.primaryColor,
                              contentPadding: EdgeInsets.zero,
                              dense: true,
                              title: Transform.translate(
                                offset: Offset(-15, 0),
                                child: Text(
                                  "Đăng ký",
                                  style: AppTextStyle.blackS16W400.copyWith(
                                      color:
                                          AppColors.textLocationVacationColor),
                                ),
                              ),
                            ),
                            RadioListTile(
                              value: 2,
                              groupValue: state.registerValue,
                              onChanged: (val) {
                                _cubit.setValueRegister(value: val);
                              },
                              activeColor: AppColors.primaryColor,
                              contentPadding: EdgeInsets.zero,
                              dense: true,
                              title: Transform.translate(
                                offset: Offset(-15, 0),
                                child: Text(
                                  "Không đăng ký",
                                  style: AppTextStyle.blackS16W400.copyWith(
                                      color:
                                          AppColors.textLocationVacationColor),
                                ),
                              ),
                            ),
                            // if (state.registerValue == 1)
                            Visibility(
                              visible: state.registerValue == 1,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 16,
                                  ),
                                  SelectFieldWidget(
                                    title: "Địa điểm nghỉ dưỡng",
                                    valueSelect: valueLocationSelect,
                                    items: (state.vacationInit?.listDetail ??
                                            [])
                                        .map((item) => DropdownMenuItem<int>(
                                              value: item.id,
                                              child: Text(
                                                item.hotelName,
                                                style: AppTextStyle.blackS16W400
                                                    .copyWith(
                                                        color: AppColors
                                                            .textLocationVacationColor),
                                              ),
                                            ))
                                        .toList(),
                                    controller: locationController,
                                    onChanged: (value) {
                                      valueLocationSelect =
                                          (state.vacationInit?.listDetail ??
                                                  [])[value]
                                              .id;
                                      locationController.text =
                                          (state.vacationInit?.listDetail ??
                                                  [])[value]
                                              .hotelName;
                                    },
                                  ),
                                  SizedBox(
                                    height: 16,
                                  ),
                                  SelectDateTimeWidget(
                                    title: "Tháng nghỉ dưỡng",
                                    tapOnly: true,
                                    onTap: () {
                                      DatePicker.showDatePicker(
                                        context,
                                        onMonthChangeStartWithFirstDate: true,
                                        dateFormat: 'MMMM yyyy',
                                        pickerTheme: DateTimePickerTheme(
                                          showTitle: true,
                                          itemTextStyle: AppTextStyle
                                              .blackTextS16NormalIncome,
                                          title: Expanded(
                                            child: Container(
                                              width: MediaQuery.of(context)
                                                  .size
                                                  .width,
                                              // color: Colors.white,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(20),
                                                  topRight: Radius.circular(20),
                                                ),
                                              ),
                                              child: Padding(
                                                padding: EdgeInsets.symmetric(
                                                    vertical: 10),
                                                child: Stack(
                                                  children: [
                                                    Container(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 16),
                                                      child: Align(
                                                        alignment:
                                                            Alignment.topRight,
                                                        child: GestureDetector(
                                                          child: SvgPicture
                                                              .asset(AppVectors
                                                                  .icCloseDialog),
                                                          onTap: () {
                                                            Navigator.of(
                                                                    context)
                                                                .pop();
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                    Center(
                                                        child: Text(
                                                            "Tháng nghỉ dưỡng",
                                                            style: AppTextStyle
                                                                .blackS16Bold)),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        maxDateTime: DateTime(
                                            state.vacationInit.travelYear,
                                            12),
                                        minDateTime: DateTime(
                                            state.vacationInit.travelYear,
                                            01),
                                        initialDateTime: DateTime(
                                            state.vacationInit.travelYear,
                                            01),
                                        // dateFormat:
                                        // AppStrings.TYPE_DD_MM_YYYY,
                                        locale: DateTimePickerLocale.vi,
                                        onChange: (dateTime, List<int> index) {
                                          dateTimeMonth = dateTime;
                                          dateTimeController.text = DateFormat(
                                                  AppStrings.TYPE_MM_YYYY)
                                              .format(dateTime);
                                        },
                                        onConfirm:
                                            (dateTime, List<int> index) {},
                                      );
                                    },
                                    controller: dateTimeController,
                                  ),
                                ],
                              ),
                            ),
                            // if (state.registerValue == 2)
                            Visibility(
                              visible: state.registerValue == 2,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    height: 16,
                                  ),
                                  SelectFieldWidget(
                                    title: "Lý do không đăng ký",
                                    valueSelect: valueRegionSelect,
                                    controller: reasonController,
                                    items: ReasonsNotRegisteringEnum.values
                                        .map((item) => DropdownMenuItem<int>(
                                              value: item.keyToServer,
                                              child: Text(item.display),
                                            ))
                                        .toList(),
                                    onChanged: (value) {
                                      valueRegionSelect =
                                          ReasonsNotRegisteringEnum
                                              .values[value].keyToServer;
                                      reasonController.text =
                                          ReasonsNotRegisteringEnum
                                              .values[value].display;
                                    },
                                  ),
                                  SizedBox(
                                    height: 16,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        "Tệp đính kèm",
                                        style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontStyle: FontStyle.normal),
                                      ),
                                      Text(
                                        "*",
                                        style: TextStyle(color: Colors.red),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 5),
                                  Text(
                                    "Hồ sơ đính kèm chứng minh lý do không đăng ký nghỉ dưỡng",
                                    style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.textInfoColor,
                                        fontStyle: FontStyle.normal),
                                  ),
                                  SizedBox(height: 10),
                                  BlocBuilder<RegisterLocationVacationCubit,
                                      RegisterLocationVacationState>(
                                    buildWhen: (previous, current) =>
                                    previous.loadFileStatus != current.loadFileStatus,
                                    builder: (context, state) {
                                      if (_cubit.state.loadFileStatus == LoadStatus.success) {
                                        return _buildImageInvoice(context, _cubit,
                                            _cubit.state.fileInvoice.fileName);
                                      } else {
                                        return GestureDetector(
                                          onTap: () async {
                                            _cubit.onOpenMultiFile();
                                          },
                                          child: DottedBorder(
                                            borderType: BorderType.RRect,
                                            radius: Radius.circular(8),
                                            dashPattern: [10, 10],
                                            color: Colors.grey,
                                            strokeWidth: 1,
                                            child: Padding(
                                              padding: const EdgeInsets.all(16.0),
                                              child: Row(
                                                  children: [
                                                    SvgPicture.asset(AppVectors.icPostFile),
                                                    SizedBox(width: 8,),
                                                    Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        "Tải lên tệp tin",
                                                        style: AppTextStyle
                                                            .blackS14Normal
                                                            .copyWith(
                                                                color: AppColors
                                                                    .textLocationVacationColor),
                                                      ),
                                                      SizedBox(height: 2,),
                                                      Text(
                                                          "Định dạng PDF, JPG, PNG",
                                                          style: AppTextStyle
                                                              .blackS12Normal
                                                              .copyWith(
                                                                  color: AppColors
                                                                      .textInfoColor))
                                                    ],
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      }
                                    },
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  PrimaryButton(
                    borderRadius: BorderRadius.circular(10),
                    title: "Xác nhận",
                    contentPadding: EdgeInsets.symmetric(horizontal: 10),
                    titleStyle:
                        AppTextStyle.blackS16W500.copyWith(color: Colors.white),
                    height: 48,
                    color: AppColors.primaryColor,
                    onTap: () async {
                      if (validateRegisterLocation()) {
                        _cubit.saveLocationTravel(
                          SaveLocationRequest(
                            id: widget.initVacation?.registerId,
                            locationStatus: state.registerValue,
                            travelMonth: state.registerValue == 1
                                ? dateTimeMonth.month
                                : null,
                            location: state.registerValue == 1
                                ? locationController.text
                                : null,
                            periodDetailId: state.registerValue == 1
                                ? valueLocationSelect
                                : null,
                            noRegisterReason: state.registerValue == 2
                                ? ReasonsNotRegisteringEnumExtension.getType(
                                        valueRegionSelect)
                                    .display
                                : null,
                            filePath: state.registerValue == 2
                                ? _cubit.state.fileInvoice.urlPath
                                : null,
                            fileName: state.registerValue == 2
                                ? _cubit.state.fileInvoice.urlPath
                                : null,
                          ),
                        );
                      }
                    },
                  ),
                ],
              ),
            );
          },
        ));
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  bool validateRegisterLocation() {
    if (locationController.text.trim().isEmpty &&
        _cubit.state.registerValue == 1) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập địa điểm nghỉ dưỡng");
      return false;
    }
    if (dateTimeController.text.trim().isEmpty &&
        _cubit.state.registerValue == 1) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập tháng nghỉ dưỡng");
      return false;
    }
    if (reasonController.text.trim().isEmpty &&
        _cubit.state.registerValue == 2) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập lý do không đăng ký nghỉ dưỡng");
      return false;
    }
    if (_cubit.state.fileInvoice == null &&
        _cubit.state.registerValue == 2) {
      DialogHelper.showSnackBar(context,
          snackBarStatus: SnackBarStatus.error,
          position: FlushbarPosition.TOP,
          message: "Vui lòng nhập hồ sơ đính kèm");
      return false;
    }
    return true;
  }

  Widget _buildImageInvoice(
      BuildContext context,
      RegisterLocationVacationCubit cubit,
      // PaymentPeriodsDto item,
      String fileName) {
    bool isSigned = true;
    return DottedBorder(
      borderType: BorderType.RRect,
      radius: Radius.circular(8),
      dashPattern: [10, 10],
      color: Colors.grey,
      strokeWidth: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: InkWell(
                onTap: () async {
                  Uint8List imageBytes;
                  if (cubit.state.fileInvoice.base64String != null) {
                    imageBytes =
                        base64.decode(cubit.state.fileInvoice.base64String);
                  } else {
                    File imageFile = File(cubit.state.fileInvoice.filePath);
                    imageBytes = await imageFile.readAsBytes();
                  }
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PaymentPeriodsViewPdf(
                        arguments: PaymentPeriodsViewPdfArgument(
                          title: fileName,
                          file: imageBytes,
                          isInvoiceFile: true,
                        ),
                      ),
                    ),
                  );
                },
                child: Row(
                  children: [
                    SvgPicture.asset(AppVectors.icPostFileDone),
                    SizedBox(
                      width: 8,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "$fileName",
                            style: AppTextStyle.blackS14Normal.copyWith(
                                color: AppColors.textLocationVacationColor),
                          ),
                          SizedBox(
                            height: 2,
                          ),
                          Text("Định dạng PDF, JPG, PNG",
                              style: AppTextStyle.blackS12Normal
                                  .copyWith(color: AppColors.textInfoColor))
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
            InkWell(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return ShowDialogAskDelete(
                        title: AppStrings.youDefinitelyDeleteInvoiceSale,
                      );
                    },
                  ).then((value) async {
                    if (value != null && value) {
                      cubit.removeFile();
                    }
                  });
                },
                child: SvgPicture.asset(
                  AppVectors.icTrash,
                ),
            ),
          ],
        ),
      ),
    );
  }
}
