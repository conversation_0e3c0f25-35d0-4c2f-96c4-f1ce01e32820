part of 'register_location_vacation_cubit.dart';

class RegisterLocationVacationState extends Equatable {
  final LoadStatus loadDataStatus, loadInitData, saveStatus, loadFileStatus;
  final int registerValue;
  final VacationModel vacationInit;
  final VacationModel vacationDetail;
  final FileData fileData;
  final ImageModel fileInvoice;

  const RegisterLocationVacationState({
    this.loadDataStatus = LoadStatus.initial,
    this.loadInitData = LoadStatus.initial,
    this.saveStatus = LoadStatus.initial,
    this.loadFileStatus = LoadStatus.initial,
    this.registerValue = 1,
    this.vacationInit,
    this.vacationDetail,
    this.fileData,
    this.fileInvoice,
  });

  @override
  List<Object> get props => [
        loadDataStatus,
        loadFileStatus,
        loadInitData,
        saveStatus,
        registerValue,
        vacationInit,
        vacationDetail,
        fileData,
    fileInvoice,
      ];

  RegisterLocationVacationState copyWith({
    LoadStatus loadDataStatus,
    LoadStatus loadInitData,
    LoadStatus saveStatus,
    LoadStatus loadFileStatus,
    int registerValue,
    VacationModel vacationInit,
    VacationModel vacationDetail,
    FileData fileData,
    ImageModel fileInvoice,
  }) {
    return RegisterLocationVacationState(
      loadDataStatus: loadDataStatus ?? this.loadDataStatus,
      loadInitData: loadInitData ?? this.loadInitData,
      saveStatus: saveStatus ?? this.saveStatus,
      loadFileStatus: loadFileStatus ?? this.loadFileStatus,
      registerValue: registerValue ?? this.registerValue,
      vacationInit: vacationInit ?? this.vacationInit,
      vacationDetail: vacationDetail ?? this.vacationDetail,
      fileData: fileData ?? this.fileData,
      fileInvoice: fileInvoice ?? this.fileInvoice,
    );
  }
}
