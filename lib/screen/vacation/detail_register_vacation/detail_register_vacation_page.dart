import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/screen/information_salary/payment_periods_view_pdf.dart';
import 'package:hstd/widget/empty_list_widget.dart';
import 'package:hstd/widget/loading_indicator.dart';
import 'package:nb_utils/nb_utils.dart';

import 'detail_register_vacation_cubit.dart';

class DetailRegisterVacationArguments {
  int id;

  DetailRegisterVacationArguments(this.id);
}

class DetailRegisterVacationPage extends StatelessWidget {
  const DetailRegisterVacationPage({Key key, this.arguments}) : super(key: key);
  final DetailRegisterVacationArguments arguments;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return DetailRegisterVacationCubit();
      },
      child: DetailRegisterVacationChildPage(
        arguments: arguments,
      ),
    );
  }
}

class DetailRegisterVacationChildPage extends StatefulWidget {
  const DetailRegisterVacationChildPage({Key key, this.arguments})
      : super(key: key);
  final DetailRegisterVacationArguments arguments;

  @override
  State<DetailRegisterVacationChildPage> createState() =>
      _DetailRegisterVacationChildPageState();
}

class _DetailRegisterVacationChildPageState
    extends State<DetailRegisterVacationChildPage> {
  DetailRegisterVacationCubit _cubit;
  final tooltipkey = GlobalKey<State<Tooltip>>();

  Future showAndCloseTooltip() async {
    await Future.delayed(const Duration(milliseconds: 10));
    // tooltipkey.currentState.ensureTooltipVisible();
    final dynamic tooltip = tooltipkey.currentState;
    tooltip?.ensureTooltipVisible();
    await Future.delayed(const Duration(seconds: 4));
    // tooltipkey.currentState.deactivate();
    tooltip?.deactivate();
  }

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of(context);
    _cubit.loadInitialData(widget.arguments.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Chi tiết đăng ký nghỉ dưỡng",
          style: TextStyle(color: Colors.black),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: _buildBodyWidget(),
      ),
    );
  }

  Widget _buildBodyWidget() {
    return BlocBuilder<DetailRegisterVacationCubit,
            DetailRegisterVacationState>(
        // buildWhen: (previous, current) =>
        //     previous.loadDataStatus != current.loadDataStatus,
        builder: (context, state) {
      if (state.loadDataStatus == LoadStatus.loading) {
        return LoadingIndicator();
      } else if (state.loadDataStatus == LoadStatus.failure) {
        return EmptyListWidget(
            content: "Đã có lỗi xảy ra!",
            onRefresh: () async {
              _cubit.loadInitialData(widget.arguments.id);
            });
      } else {
        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: 1,
              ),
              Container(
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${state.vacationDetail?.travelYear ?? ""}",
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontStyle: FontStyle.normal),
                      ),
                      SizedBox(height: 8),
                      itemInfo(
                        title: "Đăng ký lúc",
                        content:
                            "${state.vacationDetail?.timeRegisterAt ?? state.vacationDetail?.locationRegisterAt ?? ""}",
                      ),
                      SizedBox(height: 16),
                      Divider(
                        height: 1,
                        thickness: 0.5,
                        color: AppColors.textDateTimeDividerColor,
                      ),
                      SizedBox(height: 10),
                      itemInfo(
                          title: "Trạng thái đăng ký",
                          content: state.vacationDetail?.vacationStatusStr,
                          contentStyle: AppTextStyle.blackS14W400.copyWith(
                              color:
                                  state.vacationDetail?.vacationStatusTextColor)),
                      SizedBox(height: 10),
                      if (state.vacationDetail?.locationStatus == 1) ...[
                        itemInfo(
                          title: "Địa điểm nghỉ dưỡng",
                          content: "${state.vacationDetail?.location ?? ""}",
                          contentStyle: AppTextStyle.blackS14W400
                              .copyWith(color: Colors.black),
                        ),
                        SizedBox(height: 10),
                        itemInfo(
                          title: "Tháng nghỉ dưỡng",
                          content:
                              "Tháng ${state.vacationDetail?.travelMonth ?? ""} năm ${state.vacationDetail?.travelYear ?? ""}",
                          contentStyle: AppTextStyle.blackS14W400
                              .copyWith(color: Colors.black),
                        ),
                        SizedBox(height: 10)
                      ],
                      if (state.vacationDetail?.locationStatus == 2) ...[
                        itemInfo(
                          title: "Lý do",
                          content:
                              "${state.vacationDetail?.noRegisterReason ?? ""}",
                          contentStyle: AppTextStyle.blackS14W400
                              .copyWith(color: Colors.black),
                        ),
                        SizedBox(height: 10),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Hồ sơ đính kèm"),
                            SizedBox(height: 6),
                            DottedBorder(
                              borderType: BorderType.RRect,
                              radius: Radius.circular(8),
                              dashPattern: [10, 10],
                              color: Colors.grey,
                              strokeWidth: 1,
                              child: GestureDetector(
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    children: [
                                      SvgPicture.asset(AppVectors.icPostFileDone),
                                      SizedBox(
                                        width: 8,
                                      ),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "${state.fileData?.fileName}",
                                              style: AppTextStyle.blackS14Normal.copyWith(
                                                  color: AppColors.textLocationVacationColor),
                                            ),
                                            SizedBox(
                                              height: 2,
                                            ),
                                            Text("Định dạng PDF, JPG, PNG",
                                                style: AppTextStyle.blackS12Normal
                                                    .copyWith(color: AppColors.textInfoColor))
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                onTap: () async {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => PaymentPeriodsViewPdf(
                                        arguments: PaymentPeriodsViewPdfArgument(
                                          title: state.fileData?.fileName ??"",
                                          file: Base64Decoder()
                                              .convert(state.fileData?.data ?? ""),
                                          isInvoiceFile: true,
                                        ),
                                      ),
                                    ),
                                  );
                                  // }
                                },
                              ),
                            ),
                            // ClipRRect(
                            //   borderRadius: BorderRadius.circular(8.0),
                            //   child: (state.fileData?.data ?? "").isEmpty
                            //       ? Container()
                            //       : Image.memory(
                            //           Base64Decoder()
                            //               .convert(state.fileData?.data ?? ""),
                            //           fit: BoxFit.fill,
                            //           width: 100,
                            //           height: 100,
                            //         ),
                            // ),
                            SizedBox(height: 16),
                            Divider(
                              height: 1,
                              thickness: 0.5,
                              color: AppColors.textDateTimeDividerColor,
                            ),
                            SizedBox(height: 10),
                            if(state.vacationDetail?.approveName != null)
                            Row(
                              children: [
                                Expanded(
                                  child: itemInfo(
                                      title: "Người phê duyệt",
                                      content:
                                          "${state.vacationDetail?.approveName ?? ""}",
                                      contentStyle: AppTextStyle.blackS14W400
                                          .copyWith(color: Colors.black)),
                                ),
                                Tooltip(
                                  message:
                                  "Nhân viên: ${state.vacationDetail?.approveCode} - ${state.vacationDetail?.approveName}\nSố điện thoại: ${state.vacationDetail?.approvePhone}",
                                  triggerMode:
                                  TooltipTriggerMode
                                      .manual,
                                  key: tooltipkey,
                                  preferBelow: false,
                                  child: IconButton(
                                    padding:
                                    EdgeInsets.zero,
                                    icon: const Icon(
                                      Icons.info,
                                      color: Colors.blue,
                                    ),
                                    color: AppColors
                                        .primaryColor,
                                    onPressed: () {
                                      showAndCloseTooltip();
                                    },
                                  ),
                                ),
                              ],
                            ),
                            if(state.vacationDetail?.approveName != null)
                            SizedBox(height: 10),
                            if(state.vacationDetail?.approveStatus == 2)
                            itemInfo(
                                title: "Lý do từ chối",
                                content:
                                    "${state.vacationDetail?.rejectReason ?? ""}",
                                contentStyle: AppTextStyle.blackS14W400
                                    .copyWith(color: Colors.black)),
                          ],
                        ),
                      ],
                      if (state.vacationDetail?.timeStatus == 1) ...[
                        itemInfo(
                          title: "Thời gian nghỉ dưỡng",
                          content: "${state.vacationDetail?.startDate} - ${state.vacationDetail?.endDate}",
                          contentStyle: AppTextStyle.blackS14W400
                              .copyWith(color: Colors.black),
                        ),
                        SizedBox(height: 10),
                        itemInfo(
                          title: "Số lượng voucher",
                          content:
                              "${(state.vacationDetail?.listVoucher ?? []).length ?? ""}",
                          contentStyle: AppTextStyle.blackS14W400
                              .copyWith(color: Colors.black),
                        )
                      ],
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 8,
              ),
              if (state.vacationDetail?.timeStatus == 1)
                Column(
                  children: List.generate(
                    (state.vacationDetail?.listVoucher ?? []).length,
                    (index) {
                      var voucher =
                          (state.vacationDetail?.listVoucher ?? [])[index];
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Voucher ${index + 1}",
                              style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontStyle: FontStyle.normal),
                            ),
                            SizedBox(height: 16),
                            itemInfo(
                              title: "Số lượng người lớn",
                              content: "${voucher.numberPerson ?? ""}",
                            ),
                            SizedBox(height: 10),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: List.generate(
                                  (voucher.listDetail ?? []).length,
                                  (index) => Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 10.0),
                                        child: itemInfo(
                                          title: "Trẻ em ${index + 1}",
                                          content:
                                              "Sinh năm ${voucher.listDetail[index].birthYear}",
                                        ),
                                      )),
                            ),
                            SizedBox(height: 10),
                            itemInfo(
                              title: "Số serial",
                              content: "${voucher.codeVoucher ?? ""}",
                            ),
                            SizedBox(height: 10),
                            Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: voucher.base64Font.isEmptyOrNull
                                      ? Container()
                                      : Image.memory(
                                          Base64Decoder()
                                              .convert(voucher.base64Font ?? ""),
                                          fit: BoxFit.fill,
                                          width: 100,
                                          height: 100,
                                        ),
                                ),
                                SizedBox(width: 10),
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8.0),
                                  child: voucher.base64Back.isEmptyOrNull
                                      ? Container()
                                      : Image.memory(
                                          Base64Decoder()
                                              .convert(voucher.base64Back ?? ""),
                                          fit: BoxFit.fill,
                                          width: 100,
                                          height: 100,
                                        ),
                                ),
                              ],
                            )
                          ],
                        ),
                      );
                    },
                  ),
                )
            ],
          ),
        );
      }
    });
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  Widget itemInfo(
      {String title,
      String content,
      TextStyle titleStyle,
      TextStyle contentStyle}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "$title: ",
          style: titleStyle ?? TextStyle(color: AppColors.textInfoColor),
        ),
        Expanded(
          child: Text(
            '$content',
            textAlign: TextAlign.end,
            style: contentStyle ?? TextStyle(color: AppColors.textInfoColor),
          ),
        )
      ],
    );
  }
}
