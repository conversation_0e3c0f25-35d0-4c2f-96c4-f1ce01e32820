import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/models/register_vacation/list_register_vacation_response_model.dart';
import 'package:hstd/repositories/file_service.dart';
import 'package:hstd/repositories/register_vacation_service.dart';

part 'detail_register_vacation_state.dart';

class DetailRegisterVacationCubit extends Cubit<DetailRegisterVacationState> {
  DetailRegisterVacationCubit() : super(const DetailRegisterVacationState());
  RegisterVacationService workingOnHolidayService =
      RegisterVacationServiceImp();
  FileService _fileService = new FileServiceImp();

  Future<void> getFileImage(String path) async {
    emit(
      state.copyWith(
        loadFileStatus: LoadStatus.loading,
      ),
    );
    var fileData = await _fileService.downloadFile(path);
    if (fileData != null) {
      emit(
        state.copyWith(
          loadFileStatus: LoadStatus.success,
          fileData: fileData,
        ),
      );
    } else {
      emit(
        state.copyWith(loadFileStatus: LoadStatus.failure, fileData: null),
      );
    }
  }

  Future<void> getFileImageFont(String path, int indexVoucher) async {
    emit(
      state.copyWith(
        loadFileStatus: LoadStatus.loading,
      ),
    );
    var fileData = await _fileService.downloadFile(path);
    if (fileData != null) {
      var vacation = state.vacationDetail;
      vacation.listVoucher[indexVoucher].base64Font = fileData.data;
      emit(
        state.copyWith(
          loadFileStatus: LoadStatus.success,
          vacationDetail: vacation,
        ),
      );
    } else {
      emit(
        state.copyWith(loadFileStatus: LoadStatus.failure),
      );
    }
  }

  Future<void> getFileImageBack(String path, int indexVoucher) async {
    emit(
      state.copyWith(
        loadFileStatus: LoadStatus.loading,
      ),
    );
    var fileData = await _fileService.downloadFile(path);
    if (fileData != null) {
      var vacation = state.vacationDetail;
      vacation.listVoucher[indexVoucher].base64Back = fileData.data;
      emit(
        state.copyWith(
          loadFileStatus: LoadStatus.success,
          vacationDetail: vacation,
        ),
      );
    } else {
      emit(
        state.copyWith(loadFileStatus: LoadStatus.failure),
      );
    }
  }

  Future<void> loadInitialData(int id) async {
    emit(state.copyWith(loadDataStatus: LoadStatus.loading));
    try {
      //Todo: add API calls
      var result =
          await workingOnHolidayService.getDetailTravelRegister(id: id);

      if (result != null && result.status == 1) {
        var vacation = VacationModel.fromJson(result.data);
        emit(state.copyWith(
            loadDataStatus: LoadStatus.success, vacationDetail: vacation));
        if (vacation.filePath != null) await getFileImage(vacation.filePath);
        for (int i = 0; i < (vacation.listVoucher ?? []).length; i++) {
          await getFileImageFont(vacation.listVoucher[i].filePathFont, i);
          await getFileImageBack(vacation.listVoucher[i].filePathBack, i);
        }
      } else {
        emit(state.copyWith(
          loadDataStatus: LoadStatus.failure,
        ));
      }
    } catch (e, s) {
      //Todo: should print exception here
      emit(state.copyWith(loadDataStatus: LoadStatus.failure));
    }
  }
}
