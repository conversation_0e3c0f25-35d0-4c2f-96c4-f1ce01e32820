import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/network/api_request.dart';
import 'package:hstd/screen/dependant/dependant_list/dependant_list_page.dart';
import 'package:hstd/screen/dependant/models/file_response.dart';
import 'package:hstd/screen/income_tax/income_tax_page.dart';
import 'package:hstd/screen/income_tax/personal_income_tax/personal_income_tax_page.dart';
import 'package:http/http.dart' as http;
import 'package:nb_utils/nb_utils.dart';

import '../../../configs/app_configs.dart';
import '../../../widget/loading_dialog.dart';
import '../../dependant/view_pdf/view_pdf_page.dart';

class IncomeTaxMenuPage extends StatelessWidget {
  const IncomeTaxMenuPage({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Thuế thu nhập cá nhân',
          style: TextStyle(color: Colors.black),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SectionTitle(label: 'Hồ sơ chứng từ thuế'),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _MenuItem(
                  title: 'Đăng ký hình thức quyết toán thuế',
                  icon: AppVectors.icEditFilled,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) {
                          return IncomeTaxPage(
                            arguments: IncomeTaxArguments(tabIndex: 0),
                          );
                        },
                      ),
                    );
                  },
                ),
                const SizedBox(width: 12),
                _MenuItem(
                  title: 'Chứng từ thuế',
                  icon: AppVectors.icMoneyFilled,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) {
                          return IncomeTaxPage(
                            arguments: IncomeTaxArguments(tabIndex: 1),
                          );
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          SizedBox(height: 16),
          SectionTitle(label: 'Hồ sơ giảm trừ gia cảnh'),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _MenuItem(
                  title: 'Thông tin MST cá nhân',
                  icon: AppVectors.icUserCircle,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) {
                          return PersonalIncomeTaxPage();
                        },
                      ),
                    );
                  },
                ),
                const SizedBox(width: 12),
                _MenuItem(
                  title: 'Hồ sơ đăng ký kết thúc trừ gia cảnh',
                  icon: AppVectors.icUserRemoveFilled,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        settings: RouteSettings(name: '/dependant-list'),
                        builder: (context) {
                          return DependantListPage();
                        },
                      ),
                    );
                  },
                ),
                const SizedBox(width: 12),
                _MenuItem(
                  title: 'Hướng dẫn sử dụng',
                  icon: AppVectors.icBookBold,
                  onTap: () {
                    LoadingDialogTransparent.show(context);
                    _getFile('${AppConfigs.baseUrl}/sys-config/detail-by-key/HDSD_GTGC').then((value) {
                      LoadingDialogTransparent.hide(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) {
                            return ViewPdfPage(
                              arguments: ViewPdfArgument(
                                title: 'Điều hiện và hồ sơ đăng ký GTGC',
                                fileName: value.fileName,
                                data: base64.decode(value.data),
                                isInvoiceFile: false,
                                isShowButton: true,
                                buttonText: 'Tải điều hiện và hồ sơ đăng ký GTGC',
                              ),
                            );
                          },
                        ),
                      );
                    });
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<FileResponse> _getFile(String url) async {
    try {
      http.Response response = await ApiRequest.get(url);

      log("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final error = jsonData['error'];
      final result = jsonData['result'];

      if (error != null) {
        throw Exception(error);
      }

      return FileResponse.fromJson(result);
    } catch (ex) {
      log(ex.toString());
      throw ex;
    }
  }
}

class SectionTitle extends StatelessWidget {
  const SectionTitle({
    Key key,
    this.label,
  }) : super(key: key);

  final String label;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 16,
          color: Color(0xff2a2a2a),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

class _MenuItem extends StatelessWidget {
  const _MenuItem({
    Key key,
    this.title,
    this.icon,
    this.onTap,
  }) : super(key: key);

  final String title;
  final String icon;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 90,
      height: 120,
      child: CupertinoButton(
        minSize: 0,
        padding: EdgeInsets.zero,
        onPressed: () {
          onTap();
        },
        child: Container(
          child: Column(
            children: [
              Container(
                height: 52,
                width: 52,
                decoration: BoxDecoration(
                  color: AppColors.pinkItemHome,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Align(
                  alignment: Alignment.center,
                  child: SvgPicture.asset(
                    icon,
                    height: 30,
                    width: 30,
                    color: Colors.red,
                  ),
                ),
              ),
              SizedBox(
                height: 4,
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                  ),
                  child: Text(
                    title,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xff2a2a2a),
                      height: 1.25,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
