import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hstd/bloc/auth/authentication.dart';
import 'package:hstd/bloc/list_safety_certificates_bloc.dart';
import 'package:hstd/bloc/list_safety_electrical_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_images.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/app_configs.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/commit/sign_commit_request.dart';
import 'package:hstd/models/safety_certificates_model.dart';
import 'package:hstd/models/smart_otp/validate_smart_otp_request.dart';
import 'package:hstd/preferences/data_center.dart';
import 'package:hstd/screen/commit/widgets/item_list_view_commit.dart';
import 'package:hstd/screen/income_tax/register_income_tax/widgets/custom_widget_income.dart';
import 'package:hstd/screen/otp/otp_page.dart';
import 'package:hstd/screen/sign/sign_document/sign_document_cubit.dart';
import 'package:hstd/screen/sign/sign_document/sign_document_page.dart';
import 'package:hstd/screen/smart_otp/widget/active_smart_otp_dialog.dart';
import 'package:hstd/screen/smart_otp/widget/submit_smart_otp_dialog.dart';
import 'package:hstd/widget/dialog_auto_close.dart';
import 'package:hstd/widget/empty_widget.dart';
import 'package:hstd/widget/item_osh_card.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:signature/signature.dart';

class SafetyElectricalUnsignedsScreen extends StatelessWidget {
  const SafetyElectricalUnsignedsScreen({
    Key key,
  }) : super(key: key);

  static Route route({SignDocumentArguments arguments}) {
    return MaterialPageRoute<void>(
      builder: (_) => new SafetyElectricalUnsignedsScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        return SignDocumentCubit();
      },
      child: ListSafetyElectricalUnsignedsScreen(),
    );
  }
}

class ListSafetyElectricalUnsignedsScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _ListSafetyElectricalUnsignedsScreenState();

  static Route route() {
    return MaterialPageRoute<void>(builder: (_) => new ListSafetyElectricalUnsignedsScreen());
  }
}

class _ListSafetyElectricalUnsignedsScreenState extends State<ListSafetyElectricalUnsignedsScreen> {
  ListSafetyElectricalBloc _listSafetyElectricalBloc;
  SignDocumentCubit _cubit;
  SafetyCertificates _itemSign;

  final SignatureController _controller = SignatureController(
    penStrokeWidth: 3,
    penColor: Color(0xFF283AD2),
    exportBackgroundColor: Colors.transparent,
    onDrawStart: () => print('Start draw!'),
    onDrawEnd: () => print('End draw!'),
  );

  /*listening*/
  StreamSubscription _listeningAuth;

  @override
  void initState() {
    _cubit = BlocProvider.of(context);
    _controller.addListener(() {
      if (_controller.isEmpty) {
        _cubit.setSigned(false);
      }
      if (_controller.isNotEmpty) {
        _cubit.setSigned(true);
      }
    });
    _listSafetyElectricalBloc = new ListSafetyElectricalBloc();
    _listSafetyElectricalBloc.getListSafetyElectricalUnsigned();
    super.initState();
  }

  _listening(context) {
    _listeningAuth = _listSafetyElectricalBloc.isAuthorization.listen((event) {
      if (event != null && event == false)
        BlocProvider.of<AuthenticationBloc>(context)
          ..add(AuthenticationLogoutRequested());
    });
  }

  @override
  void dispose() {
    super.dispose();
    _listSafetyElectricalBloc.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_listeningAuth == null) {
      _listening(context);
    }

    return BlocConsumer<SignDocumentCubit, SignDocumentState>(
      listenWhen: (previous, current) =>
      previous.createOtpStatus != current.createOtpStatus ||
          previous.validateStatus != current.validateStatus||
          previous.signStautus != current.signStautus,
      listener: (context, state) async {
        if (state.createOtpStatus == LoadStatus.loading) {
          LoadingDialogTransparent.show(context);
        }
        if (state.createOtpStatus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          SharedPreferences sharedPreferences =
          await SharedPreferences.getInstance();
          String phoneNumber =
          sharedPreferences.getString(AppConstant.KEY_PHONE_NUMBER_VCC);
          bool result = await Navigator.push(
            context,
            OtpPage.route(
              arguments: OtpArguments(
                phoneNumber: phoneNumber,
                otp: state.otp,
                typeSign: TypeSign.TRAINING_ELECTRICAL_SAFETY_BOOK,
              ),
            ),
          );
          if (result != null) {
            _cubit.clearCreateOtpStatus();
            _controller.toPngBytes().then((value) {
              _cubit.sign(
                type: TypeSign.TRAINING_ELECTRICAL_SAFETY_BOOK,
                sign: value,
                signElectricalSafetyRequest: SignElectricalSafetyRequest(
                  laborElectricalSafetyId: _itemSign?.laborElectricalSafetyId,
                ),
                path: _cubit.state.signatureFile?.signaturePath,
              );
            });
          }
        }
        if (state.createOtpStatus == LoadStatus.failure) {
          LoadingDialogTransparent.hide(context);
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: state.message,
              showIcon: false,
            ),
          );
        }
        if (state.signStautus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          _cubit.actionAfterSignSuccess(
            context: context,
            typeSign: TypeSign.TRAINING_ELECTRICAL_SAFETY_BOOK,
          );
        }
        if (state.signStautus == LoadStatus.loading) {
          LoadingDialogTransparent.show(context);
        }
        if (state.signStautus == LoadStatus.failure) {
          LoadingDialogTransparent.hide(context);
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: state.message,
              showIcon: false,
            ),
          );
        }
        if (state.validateStatus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          _cubit.clearValidateStatus();
          _controller.toPngBytes().then((value) {
            _cubit.sign(
              type: TypeSign.TRAINING_ELECTRICAL_SAFETY_BOOK,
              sign: value,
              signElectricalSafetyRequest: SignElectricalSafetyRequest(
                laborElectricalSafetyId: _itemSign?.laborElectricalSafetyId,
              ),
              path: _cubit.state.signatureFile?.signaturePath,
            );
          });
        }
        if (state.validateStatus == LoadStatus.loading) {
          LoadingDialogTransparent.show(context);
        }
        if (state.validateStatus == LoadStatus.failure) {
          LoadingDialogTransparent.hide(context);
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: state.message,
              showIcon: false,
            ),
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            title: Text(
              'Ký sổ theo dõi An toàn điện',
              style: TextStyle(
                color: Colors.black,
              ),
            ),
            foregroundColor: Colors.black,
            centerTitle: true,
            backgroundColor: Colors.white,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            physics: ScrollPhysics(),
            scrollDirection: Axis.vertical,
            child: Column(
              children: [
                _listSafetyCards(),
              ],
            ),
          ),
        );
      },
    );


  }

  Widget _listSafetyCards() {
    Size size = MediaQuery.of(context).size;
    return StreamBuilder<List<SafetyCertificates>>(
      initialData: [],
      stream: _listSafetyElectricalBloc.oshList$,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: new AlwaysStoppedAnimation<Color>(
                    AppColors.primaryColor,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.all(10),
                  child: Text('Đang tải'),
                )
              ],
            ),
          );
        }
        if (snapshot.hasData &&
            snapshot.data != null &&
            snapshot.data.length != 0) {
          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              SafetyCertificates safetyCertificate = snapshot.data[index];
              return ItemOshCard(
                item: safetyCertificate,
                signButton: PrimaryButton(
                  borderRadius: BorderRadius.circular(10),
                  title: AppStrings.sign_electronic,
                  titleStyle: AppTextStyle.textButtonSignElectricS16W600,
                  height: 48,
                  color: AppColors.bgButtonSignElectric,
                  padding: EdgeInsets.fromLTRB(0, 10, 0, 16),
                  onTap: () async {
                    _itemSign = safetyCertificate;
                    showDialog(
                      context: context,
                      barrierDismissible: true,
                      builder: (BuildContext context) {
                        return _bottomSheetSign();
                      },
                    );
                  },
                ),
              );
            },
            itemCount: snapshot.data.length,
            padding: EdgeInsets.symmetric(
              horizontal: 10.0,
              vertical: 10.0,
            ),
          );
        }
        return EmptyWidget(
          content: "Không có thẻ an toàn điện nào",
        );
      },
    );
  }

  Widget _bottomSheetSign() {
    Future.microtask(() => _cubit
        .getDefaultSignature(TypeSign.TRAINING_ELECTRICAL_SAFETY_BOOK.name));

    return BlocBuilder<SignDocumentCubit, SignDocumentState>(
      bloc: _cubit,
      buildWhen: (previous, current) =>
          previous.signed != current.signed ||
              previous.loadDefaultSignature != current.loadDefaultSignature,
      builder: (context, state) {
        final bool hasSignature = state.signed ||
            (state.signatureFile?.signaturePath ?? '').isNotEmpty;
        final bool needsSignature = state.signatureFile != null && state.signatureFile?.signaturePath == null;
        final screenWidth = MediaQuery.of(context).size.width;
        final signatureHeight = MediaQuery.of(context).size.height * 0.4;
        if (needsSignature)
          return _dialogNeedsSignature(context);
        return Dialog(
          alignment: Alignment.bottomCenter,
          insetPadding: EdgeInsets.zero,
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            height: (MediaQuery.of(context).size.height * 0.4) + 180,
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 16,
                  right: 16,
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: SvgPicture.asset(
                      AppVectors.ic_delete_type_3,
                    ),
                  ),
                ),
                if (!hasSignature) ...[
                  Align(
                    alignment: Alignment.center,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 25),
                      child: Text(
                        AppStrings.sign_here,
                        style: AppTextStyle.blackS14Normal,
                      ),
                    ),
                  ),
                ],
                Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Text(
                        AppStrings.employee_signature,
                        style: AppTextStyle.blackS16W600,
                      ),
                    ),
                    Divider(height: 1),
                    if ((state.signatureFile?.signaturePath ?? '').isNotEmpty)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          "${AppConfigs.baseUrl}/public/image?path=${state.signatureFile.signaturePath}",
                          width: screenWidth - 38,
                          height: signatureHeight,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) =>
                              Center(child: Text('Không thể tải ảnh chữ ký')),
                        ),
                      )
                    else
                      Opacity(
                        opacity: 0.8,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.backgroundItemListView,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Stack(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(5),
                                  child: Signature(
                                    controller: _controller,
                                    backgroundColor:
                                        AppColors.backgroundItemListView,
                                    height: signatureHeight,
                                    width: screenWidth - 38,
                                  ),
                                ),
                                if (state.signed)
                                  Positioned(
                                    top: 0,
                                    right: 0,
                                    child: InkWell(
                                      onTap: () => _controller.clear(),
                                      child: Container(
                                        width: 38,
                                        height: 38,
                                        color: AppColors.backgroundItemListView,
                                        child: Padding(
                                          padding: const EdgeInsets.all(8),
                                          child: SvgPicture.asset(
                                              AppVectors.ic_clear_sign),
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    Opacity(
                      opacity: hasSignature ? 1 : 0.5,
                      child: PrimaryButton(
                        borderRadius: BorderRadius.circular(24),
                        title: AppStrings.confirm,
                        titleStyle: AppTextStyle.whiteS16W600,
                        height: 48,
                        padding:
                            EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                        color: AppColors.primaryColor,
                        borderColor: AppColors.primaryColor,
                        onTap: () async {
                          if (!hasSignature) return;
                          Navigator.of(context).pop();
                          var dataCenter = DataCenter.shared();
                          if (dataCenter.getUserDeviceId() != null &&
                              dataCenter.getUserDeviceId() ==
                                  dataCenter.getDeviceId()) {
                            var result = await showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return SubmitSmartOtpDialog(
                                  argument: SubmitSmartOtpArgument(
                                      entityId: _itemSign
                                          .laborElectricalSafetyId
                                          .toString(),
                                      entityType:
                                          "TRAINING_ELECTRICAL_SAFETY_BOOK"),
                                );
                              },
                            );
                            if (result != null) {
                              String otp = result as String;
                              _cubit.validateSmartOtp(
                                ValidateSmartOtpRequest(
                                  otp: otp,
                                  entityId: _itemSign.laborElectricalSafetyId
                                      .toString(),
                                  entityType: "TRAINING_ELECTRICAL_SAFETY_BOOK",
                                  deviceId: dataCenter.getUserDeviceId(),
                                ),
                              );
                            }
                            return;
                          } else {
                            var result = await showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return ActiveSmartOtpDialog(
                                  smartOtpPageContext: context,
                                );
                              },
                            );
                            if (result ?? false) {
                              _cubit.confirm(
                                  typeSign:
                                      TypeSign.TRAINING_ELECTRICAL_SAFETY_BOOK);
                            }
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Dialog _dialogNeedsSignature(BuildContext context) {
    return Dialog(
      alignment: Alignment.bottomCenter,
      insetPadding: EdgeInsets.zero,
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                Center(
                  child: Text(
                    AppStrings.employee_signature,
                    style: AppTextStyle.blackS16W600,
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  child: InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: SvgPicture.asset(AppVectors.ic_delete_type_3),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              AppStrings.needs_signature_image,
              style: AppTextStyle.blackS14Normal,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            PrimaryButton(
              borderRadius: BorderRadius.circular(24),
              title: AppStrings.confirm,
              titleStyle: AppTextStyle.whiteS16W600,
              height: 48,
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              color: AppColors.primaryColor,
              borderColor: AppColors.primaryColor,
              onTap: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }
}
