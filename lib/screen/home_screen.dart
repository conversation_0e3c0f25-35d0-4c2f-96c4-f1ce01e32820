import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_beautiful_popup/main.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hstd/bloc/auth/authentication_bloc.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/models/contract_dto.dart';
import 'package:hstd/models/labor_protecttion/labor_protection_dto.dart';
import 'package:hstd/models/notify_contract_dto.dart';
import 'package:hstd/repositories/advancement_roadmap_service.dart';
import 'package:hstd/repositories/contract_service.dart';
import 'package:hstd/repositories/labor_service.dart';
import 'package:hstd/repositories/notify_contract_service.dart';
import 'package:hstd/repositories/profile_service.dart';
import 'package:hstd/screen/account_screen_outside.dart';
import 'package:hstd/screen/advancemet_roadmap/register_roadmap/register_roadmap_page.dart';
import 'package:hstd/screen/information_screen.dart';
import 'package:hstd/screen/labor_protection/register_labor_protection_detail/register_labor_protection_detail_page.dart';
import 'package:hstd/screen/labor_protection/widgets/labor_dialog.dart';
import 'package:hstd/screen/list_contract_screen.dart';
import 'package:hstd/screen/list_contracts_appendix_screen.dart';
import 'package:hstd/screen/login_by_sdt/sign_electronic/sign_electronic_screen.dart';
import 'package:hstd/screen/notify_contract/notify_detail/notify_detail_page.dart';
import 'package:hstd/screen/notify_contract/widgets/notify_dialog.dart';
import 'package:hstd/screen/profile_screen.dart';
import 'package:hstd/screen/resignation/resignation_page.dart';
import 'package:hstd/utils/pushMultiPageScreen.dart';
import 'package:hstd/widget/sliver_grid_delegate.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:store_redirect/store_redirect.dart';

import 'accout_screen.dart';
import 'advancemet_roadmap/custom_widget.dart';
import 'certification/ask_for_certification/ask_for_certification_page.dart';
import 'information_screen_new.dart';

class HomeScreen extends StatefulWidget {
  static bool showPopup = true;
  static bool showLabor = true;
  static bool showNotify = true;
  static bool showRegisterRoadmap = true;
  final SharedPreferences sharedPreferences;
  final bool isResignation;

  static Route route(SharedPreferences sharedPreferences) {
    return MaterialPageRoute<void>(
        builder: (_) => HomeScreen(sharedPreferences: sharedPreferences));
  }

  static Route routeResignation(SharedPreferences sharedPreferences) {
    return MaterialPageRoute<void>(
        builder: (_) => HomeScreen(sharedPreferences: sharedPreferences, isResignation: true,));
  }

  static Route routeNoAnimation(SharedPreferences sharedPreferences) {
    return NoAnimationPageRoute<void>(
        builder: (_) => HomeScreen(sharedPreferences: sharedPreferences));
  }

  HomeScreen({this.sharedPreferences, this.isResignation = false, Key key})
      : super(key: key);

  @override
  HomeState createState() => HomeState();

  static showToast(Color color, String message) {
    Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: color,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  static showToastCenter(Color color, String message) {
    Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 1,
        backgroundColor: color,
        textColor: Colors.white,
        fontSize: 16.0);
  }
  static showToastTop(Color color, String message) {
    Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.TOP,
        timeInSecForIosWeb: 0,
        backgroundColor: color,
        textColor: Colors.white,
        fontSize: 14.0);
  }
}

class HomeState extends State<HomeScreen> with WidgetsBindingObserver {
  int selectedIndex = 0;
  final tabInformation = 0;
  final tabContract = 1;
  final tabTerminateContract = 2;
  final profile = 3;
  static List<Widget> _widgetOptions = <Widget>[
    InformationScreenNew(
      isLLTN: false,
    ),
    // ListContractScreen(),
    ListContractsAppendixScreen(),
    AskForCertificationPage(),
    AccoutScreen()
  ];

  static List<Widget> _widgetOptionsLLTN = <Widget>[
    InformationScreenNew(
      isLLTN: true,
    ),
    SignElectronicScreen(),
    AccountOutsideScreen()
  ];

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    if (widget.isResignation) {
      selectedIndex = tabTerminateContract;
    }
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    if (state == AppLifecycleState.paused) {
      sharedPreferences.setInt(
          'last_paused', DateTime.now().millisecondsSinceEpoch);
    }

    if (state == AppLifecycleState.resumed) {
      int now = DateTime.now().millisecondsSinceEpoch;
      int lastTime = sharedPreferences.getInt('last_paused');
      sharedPreferences.remove('last_paused');
      if (now - lastTime > 5 * 60 * 1000) {
        BlocProvider.of<AuthenticationBloc>(context)
          ..add(AuthenticationLogoutRequested());
      }
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    Future.delayed(Duration.zero, () => _showPopup(context));
    return Scaffold(
      body: Center(
        child: Container(
          child: (widget.sharedPreferences
                      .getStringList(AppConstant.KEY_ROLE)[0] ==
                  'HSTD_OUTSOURCE')
              ? _widgetOptionsLLTN.elementAt(selectedIndex)
              : _widgetOptions.elementAt(selectedIndex),
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        iconSize: 28,
        selectedFontSize: 12,
        unselectedFontSize: 12,
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.white,
        items:
            (widget.sharedPreferences.getStringList(AppConstant.KEY_ROLE)[0] ==
                    'HSTD_OUTSOURCE')
                ? <BottomNavigationBarItem>[
                    BottomNavigationBarItem(
                      label: AppStrings.home_page,
                      icon: SvgPicture.asset(
                        AppVectors.icHome,
                      ),
                      activeIcon: Padding(
                        padding: const EdgeInsets.only(top: 1),
                        child: SvgPicture.asset(
                          AppVectors.icHomeSelect,
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ),
                    BottomNavigationBarItem(
                      label: AppStrings.sign_electronic,
                      icon: SvgPicture.asset(
                        AppVectors.icElectSign,
                      ),
                      activeIcon: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: SvgPicture.asset(
                          AppVectors.icElectSignSelect,
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ),
                    BottomNavigationBarItem(
                      label: AppStrings.account,
                      icon: SvgPicture.asset(
                        AppVectors.icProfile,
                      ),
                      activeIcon: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: SvgPicture.asset(
                          AppVectors.icProfileSelect,
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ),
                  ]
                : <BottomNavigationBarItem>[
                    BottomNavigationBarItem(
                      label: AppStrings.home_page,
                      icon: SvgPicture.asset(
                        AppVectors.icHome,
                      ),
                      activeIcon: SvgPicture.asset(
                        AppVectors.icHomeSelect,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    BottomNavigationBarItem(
                      label: AppStrings.sign_electronic,
                      icon: SvgPicture.asset(
                        AppVectors.icElectSign,
                      ),
                      activeIcon: SvgPicture.asset(
                        AppVectors.icElectSignSelect,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    BottomNavigationBarItem(
                      label: AppStrings.income,
                      icon: SvgPicture.asset(
                        AppVectors.icMoney,
                      ),
                      activeIcon: SvgPicture.asset(
                        AppVectors.ic_infoCollected,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    BottomNavigationBarItem(
                      label: AppStrings.account,
                      icon: SvgPicture.asset(
                        AppVectors.icProfile,
                      ),
                      activeIcon: SvgPicture.asset(
                        AppVectors.icProfileSelect,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ],
        currentIndex: selectedIndex,
        selectedItemColor: AppColors.primaryColor,
        onTap: _onItemTapped,
      ),
    );
  }

  void _showPopup(BuildContext context) async {
    if (HomeScreen.showLabor) {
      try {
        HomeScreen.showLabor = false;
        final _laborService = LaborServiceImp();
        LaborProtectionDto laborProtectionDto =
            await _laborService.checkLaborProtection();
        int totalRegister = await _laborService.getTotalRegister(registerId: 0);
        if (laborProtectionDto.registrationStatus == 0 &&
            laborProtectionDto.lockStatus == 0) {
          showDialog(
            context: context,
            builder: (BuildContext context) => ShowLaborDialog(
              action: () async {
                pushWithoutAnimation(RegisterLaborProtectionDetailPage(
                  arguments: RegisterLaborProtectionDetailArguments(
                    id: laborProtectionDto.id,
                    fromDialog: true,
                  ),
                ));
              },
              title: AppStrings.notification,
              content:
                  'Bạn có ${totalRegister ?? 0} yêu cầu cần đăng ký đồ bảo hồ lao động',
            ),
            barrierDismissible: false,
          );
        }
      } catch (e) {}
    }
    if (HomeScreen.showNotify) {
      try {
        HomeScreen.showNotify = false;
        final _notifyService = NotifyContractServiceImp();
        NotifyContractDto notify = await _notifyService.checkNotify();
        if (notify.approve == 0) {
          showDialog(
            context: context,
            builder: (BuildContext context) => ShowDialogNotification(
              action: () async {
                Future.delayed(const Duration(milliseconds: 0), () {
                  _onItemTapped(profile);
                });
                pushWithoutAnimation(NotifyDetailPage(
                  argument: NotifyDetailArgument(
                    id: notify.id ?? 0,
                    pathPdf: notify.filePath ?? '',
                    fromDialog: true,
                  ),
                ));
              },
              title: 'Thông báo',
              content:
                  'Đồng chí có một văn bản thông báo không ký tiếp HĐLĐ. Vui lòng đọc và xác nhận thông tin',
            ),
            barrierDismissible: false,
          );
        }
      } catch (e) {}
    }

    if (HomeScreen.showRegisterRoadmap) {
      try {
        HomeScreen.showRegisterRoadmap = false;
        final _roadmapService = AdvancementRoadmapServiceImp();
        int result = await _roadmapService.checkRegisteredRoadmap();
        if (result == 2) {
          showDialog(
            context: context,
            builder: (BuildContext context) => ShowDialogRegisterRoadmap(
              action: () async {
                pushWithoutAnimation(RegisterRoadmapPage(
                  arguments: RegisterRoadmapArguments(
                    fromDialog: true,
                  ),
                ));
              },
              title: 'Thông báo',
              content:
              'Lộ trình thăng tiến của bạn đăng ký không còn tồn tại. Bạn vui lòng đăng ký lộ trình thăng tiến mới!',
            ),
            barrierDismissible: false,
          );
        }
      } catch (e) {}
    }

    if (HomeScreen.showPopup) {
      try {
        HomeScreen.showPopup = false;
        final _service = ContractServiceImp();
        Contract probationContract = await _service.getContractProbation();
        Contract laborContract = await _service.getContractLabor();

        final _profile = ProfileServiceImp();
        String version = await _profile.getAppVersion();
        var versionApp = await PackageInfo.fromPlatform();

        if (int.parse(version.replaceAll('.', '')) >
            int.parse(versionApp.version.replaceAll('.', ''))) {
          return showUpdateApp();
        }

        String message = probationContract != null
            ? "Bạn có một hợp đồng thử việc cần ký."
            : (laborContract != null
                ? "Bạn có một hợp đồng lao động cần ký"
                : "");
        final popup = BeautifulPopup(context: context, template: TemplateGift);
        (probationContract != null || laborContract != null)
            ? popup.show(
                close: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    HomeScreen.showPopup = false;
                  },
                  child: SizedBox(
                    width: 48,
                    height: 48,
                    child: Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ),
                title: 'Thông báo',
                content: Container(
                  padding: EdgeInsets.symmetric(vertical: 20),
                  child: Text(
                    message,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.black54,
                        fontSize: 16),
                  ),
                ),
                actions: [
                  popup.button(
                    label: 'Xem chi tiết',
                    onPressed: () {
                      Navigator.of(context).pop();
                      HomeScreen.showPopup = false;
                      Future.delayed(const Duration(milliseconds: 300), () {
                        _onItemTapped(tabContract);
                      });
                    },
                  ),
                ],
              )
            : Container();
      } catch (ex) {
        HomeScreen.showPopup = false;
      }
    }
  }

  void showUpdateApp() {
    showDialog(
      context: context,
      builder: (_) => WillPopScope(
        onWillPop: () async => false,
        child: Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          'Thông báo',
                          style: AppTextStyle.blackColorS20W700,
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.topRight,
                      child: GestureDetector(
                        child: SvgPicture.asset(AppVectors.icCloseDialog),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 28),
                Text(
                  'VCC-Hồ sơ điện tử đã có phiên bản mới. Bạn vui lòng cập nhật để sử dụng tính năng mới nhất',
                  style: AppTextStyle.greyTextFieldLableS16W400,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 28),
                GridView(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  gridDelegate:
                  SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight(
                    crossAxisCount: 2,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 0,
                    height: 44,
                  ),
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        height: 40,
                        padding: EdgeInsets.symmetric(
                          horizontal: 5,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          color: AppColors.backgroundCloseButton,
                          border: Border.all(
                            color: AppColors.backgroundCloseButton,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            AppStrings.cancel,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.blackS16W500,
                          ),
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        StoreRedirect.redirect(
                          androidAppId: 'com.vcc.hstd.hsdt',
                          iOSAppId: '1619482219',
                        );
                      },
                      child: Container(
                        height: 40,
                        padding: EdgeInsets.symmetric(
                          horizontal: 5,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          color: AppColors.primaryColor,
                          border: Border.all(
                            color: AppColors.primaryColor,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            AppStrings.download,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.whiteS16W500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4),
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  Future<T> pushWithoutAnimation<T extends Object>(Widget page) {
    Route route = NoAnimationPageRoute(builder: (BuildContext context) => page);
    return Navigator.push(context, route);
  }
}
