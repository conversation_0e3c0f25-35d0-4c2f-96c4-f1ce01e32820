import 'dart:typed_data';

import 'package:equatable/equatable.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/employee_families/image_model.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/models/otp_dto.dart';
import 'package:hstd/repositories/payment_periods_service.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:permission_handler/permission_handler.dart';

part 'payment_periods_detail_state.dart';

class PaymentPeriodsDetailCubit extends Cubit<PaymentPeriodsDetailState> {
  PaymentPeriodsDetailCubit() : super(const PaymentPeriodsDetailState());
  PaymentPeriodsService _service = PaymentPeriodsServiceImpl();

  Future<void> getPaymentPeriodsDetail(int paymentId, String type, String title) async {
    emit(state.copyWith(
      loadDataStatus: LoadStatus.loading,
    ));
    try {
      var response = await _service.downloadFile(paymentId, type);
      if (response.result) {
        emit(
          state.copyWith(
              loadDataStatus: LoadStatus.success, base64File: response.image, fileTitle: title),
        );
      } else {
        emit(state.copyWith(
            loadDataStatus: LoadStatus.failure, message: response.message));
      }
    } catch (e) {
      emit(state.copyWith(
        loadDataStatus: LoadStatus.failure,
      ));
    }
  }
  Future<void> downloadFileBillById(int id, String checkSum) async {
    emit(state.copyWith(
      loadFileBillStatus: LoadStatus.loading,
    ));
    try {
      var response = await _service.downloadFileById(id, checkSum);
      if (response.result) {
        log("DownloadResponse e: ${response.image}");
        emit(
          state.copyWith(
              loadFileBillStatus: LoadStatus.success, fileBill: response.image)
        );
      } else {
        log("DownloadResponse failure");
        emit(state.copyWith(
            loadFileBillStatus: LoadStatus.failure, message: response.message));
      }
    } catch (e) {
      log("DownloadResponse failure");
      emit(state.copyWith(
        loadFileBillStatus: LoadStatus.failure,
      ));
    }
  }

  Future<void> createOtp() async {
    emit(state.copyWith(createOtpStatus: LoadStatus.loading));

    OtpRequest otpRequest = new OtpRequest(content: null);
    try {
      final Otp result = await _service.createOtp(otpRequest);
      if (result != null) {
        emit(state.copyWith(
          createOtpStatus: LoadStatus.success,
          otp: result,
        ));
      } else {
        emit(
          state.copyWith(
            createOtpStatus: LoadStatus.failure,
            message: AppStrings.error_create_otp,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          createOtpStatus: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }

  void setImageSign(Uint8List value) {
    log("setImageSign");
    emit(
      state.copyWith(
        imageSign: value,
      ),
    );
  }

  void setOtpRequestStatus() {
    emit(
      state.copyWith(
        createOtpStatus: LoadStatus.initial,
        message: "",
      ),
    );
  }

  void setDownloadFileStatus() {
    emit(
      state.copyWith(
        loadDataStatus: LoadStatus.initial,
        message: "",
      ),
    );
  }
  void setDownloadFileBillStatus() {
    emit(
      state.copyWith(
        loadFileBillStatus: LoadStatus.initial,
          message: "",
      ),
    );
  }

  void setSignedReal(bool value) {
    emit(
      state.copyWith(
        signedReal: value,
      ),
    );
  }

  void setSignedTemp(bool value) {
    emit(
      state.copyWith(
        signedTemp: value,
      ),
    );
  }

  void onOpenMultiFile() async {
    emit(state.copyWith(
      loadFileStatus: LoadStatus.loading,
    ));
    chooseMultiPartFile();
    await Permission.manageExternalStorage.request();
    final permissionStatus = await Permission.manageExternalStorage.status;
    if (permissionStatus.isDenied) {
      await Permission.storage.request();
      final permission = await Permission.manageExternalStorage.status;
      if (permission.isDenied) {
        await Permission.manageExternalStorage.request();
      } else if (permission.isGranted) {
        chooseMultiPartFile();
      }
    } else if (permissionStatus.isPermanentlyDenied) {
      await openAppSettings();
    } else if (permissionStatus.isGranted) {
      chooseMultiPartFile();
    }
  }

  void chooseMultiPartFile() async {
    var resultFile = await FilePicker.platform.pickFiles(allowMultiple: false);
    if (resultFile == null) {
      return;
    } else {
      resultFile.files.forEach((element) {
        if (element.path.endsWith(AppStrings.endFile) ||
            element.path.endsWith(AppStrings.endPngFile) ||
            element.path.endsWith(AppStrings.endJpegFile) ||
            element.path.endsWith(AppStrings.endJpgFile)) {
          ImageModel invoiceFile =
              ImageModel(fileName: element.name, filePath: element.path);
          if (invoiceFile.isLessThan10MB()) {
            emit(
              state.copyWith(
                  fileInvoice: invoiceFile, loadFileStatus: LoadStatus.success),
            );
          } else {
            state.copyWith(loadFileStatus: LoadStatus.failure);
            Fluttertoast.showToast(
              msg: AppStrings.fileLengthIncorrect,
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
            );
          }
        } else {
          state.copyWith(loadFileStatus: LoadStatus.failure);
          Fluttertoast.showToast(
            msg: AppStrings.fileFormatIncorrect,
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.BOTTOM,
          );
        }
      });
    }
  }

  void removeFile() {
    emit(
      state.copyWith(
          fileInvoice: ImageModel(filePath: null, fileName: null),
          loadFileStatus: LoadStatus.initial),
    );
  }

  void setFileBill(String fileName) {
    emit(
      state.copyWith(
          fileInvoice: ImageModel(filePath: null, fileName: fileName),
          loadFileStatus: LoadStatus.success),
    );
  }

  Future<void> getDefaultSignature(String signType) async {
    emit(
      state.copyWith(
        loadDefaultSignature: LoadStatus.loading,
      ),
    );

    try {
      final file = await _service.getDefaultSignature(signType);

      if (file != null) {
        emit(
          state.copyWith(
            loadDefaultSignature: LoadStatus.success,
            signatureFile: file,
          ),
        );
      } else {
        emit(state.copyWith(
          loadDefaultSignature: LoadStatus.success,
        ));
      }
    } catch (e) {
      emit(
        state.copyWith(
          loadDefaultSignature: LoadStatus.failure,
          message: AppStrings.has_error,
        ),
      );
    }
  }
}
