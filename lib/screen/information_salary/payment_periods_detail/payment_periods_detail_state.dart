part of 'payment_periods_detail_cubit.dart';

class PaymentPeriodsDetailState extends Equatable {
  final LoadStatus loadDataStatus;
  final LoadStatus loadFileBillStatus;
  final Uint8List base64File;
  final Uint8List imageSign;
  final Uint8List fileBill;
  final ImageModel fileInvoice;
  final LoadStatus loadFileStatus;
  final LoadStatus createOtpStatus;
  final LoadStatus confirmPaymentStatus;
  final bool signedReal;
  final bool signedTemp;
  final Otp otp;
  final String message;
  final String fileTitle;

  const PaymentPeriodsDetailState(
      {this.loadDataStatus = LoadStatus.initial,
      this.createOtpStatus = LoadStatus.initial,
      this.loadFileBillStatus = LoadStatus.initial,
      this.confirmPaymentStatus = LoadStatus.initial,
      this.base64File,
      this.imageSign,
      this.fileBill,
      this.fileInvoice,
      this.signedReal = false,
      this.signedTemp = false,
      this.otp,
      this.message,
      this.fileTitle,
      this.loadFileStatus = LoadStatus.initial});

  @override
  List<Object> get props => [
        loadDataStatus,
        createOtpStatus,
        confirmPaymentStatus,
        loadFileBillStatus,
        base64File,
        fileBill,
        imageSign,
        fileInvoice,
        loadFileStatus,
    signedReal,
        signedTemp,
        otp,
        fileTitle,
        message
      ];

  PaymentPeriodsDetailState copyWith(
      {LoadStatus loadDataStatus,
      Uint8List base64File,
      Uint8List imageSign,
      Uint8List fileBill,
      ImageModel fileInvoice,
      LoadStatus loadFileStatus,
      LoadStatus loadFileBillStatus,
      Otp otp,
      LoadStatus createOtpStatus,
      String message,
      String fileTitle,
      bool signedReal,
      bool signedTemp}) {
    return PaymentPeriodsDetailState(
        loadDataStatus: loadDataStatus ?? this.loadDataStatus,
        base64File: base64File ?? this.base64File,
        fileInvoice: fileInvoice ?? this.fileInvoice,
        signedReal: signedReal ?? this.signedReal,
        fileBill: fileBill ?? this.fileBill,
        signedTemp: signedTemp ?? this.signedTemp,
        imageSign: imageSign ?? this.imageSign,
        otp: otp ?? this.otp,
        loadFileStatus: loadFileStatus ?? this.loadFileStatus,
        loadFileBillStatus: loadFileBillStatus ?? this.loadFileBillStatus,
        confirmPaymentStatus: confirmPaymentStatus ?? this.confirmPaymentStatus,
        message: message ?? this.message,
        fileTitle: fileTitle ?? this.fileTitle,
        createOtpStatus: createOtpStatus ?? this.createOtpStatus);
  }
}
