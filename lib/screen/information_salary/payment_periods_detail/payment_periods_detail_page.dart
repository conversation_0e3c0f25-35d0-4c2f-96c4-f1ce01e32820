import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_text_styles.dart';
import 'package:hstd/common/app_vectors.dart';
import 'package:hstd/configs/app_configs.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/core/constant.dart';
import 'package:hstd/models/payment_periods/payment_periods_confirm_dto.dart';
import 'package:hstd/models/payment_periods/payment_periods_dto.dart';
import 'package:hstd/screen/advancemet_roadmap/custom_widget.dart';
import 'package:hstd/screen/income_tax/widget/dialog_delete.dart';
import 'package:hstd/screen/information_salary/payment_periods_view_pdf.dart';
import 'package:hstd/screen/otp_periods/otp_periods_page.dart';
import 'package:hstd/utils/common_utils.dart';
import 'package:hstd/utils/enum_work_detail.dart';
import 'package:hstd/widget/dialog_auto_close.dart';
import 'package:hstd/widget/loading_dialog.dart';
import 'package:hstd/widget/primary_button.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:signature/signature.dart';

import 'payment_periods_detail_cubit.dart';

class PaymentPeriodsDetailArguments {
  PaymentPeriodsDto item;

  PaymentPeriodsDetailArguments({this.item});
}

class PaymentPeriodsDetailPage extends HookWidget {
  final PaymentPeriodsDetailArguments arguments;

  const PaymentPeriodsDetailPage({
    Key key,
    this.arguments,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PaymentPeriodsDto item = arguments.item;
    final cubit = useMemoized(() => PaymentPeriodsDetailCubit());
    final blocState = useState<PaymentPeriodsDetailState>(cubit.state);

    useEffect(() {
      if (item.fileBill != null) {
        cubit.setFileBill(item.fileBill.fileName);
      }
      bool isSigned = item.status != WAIT_SIGN && item.status != UNSIGN;
      cubit.setSignedReal(isSigned);
      final subscription = cubit.stream.listen((state) {
        blocState.value = state;
        if (state.loadDataStatus == LoadStatus.loading ||
            state.loadFileBillStatus == LoadStatus.loading) {
          LoadingDialogTransparent.show(context);
        } else if (state.loadDataStatus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PaymentPeriodsViewPdf(
                arguments: PaymentPeriodsViewPdfArgument(
                    title: state.fileTitle,
                    file: state.base64File,
                    isInvoiceFile: false),
              ),
            ),
          );
          cubit.setDownloadFileStatus();
        } else if (state.loadFileBillStatus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PaymentPeriodsViewPdf(
                arguments: PaymentPeriodsViewPdfArgument(
                    title: item.fileBill.fileName,
                    file: state.fileBill,
                    isInvoiceFile: true),
              ),
            ),
          );
          cubit.setDownloadFileBillStatus();
        } else if (state.loadDataStatus == LoadStatus.failure ||
            state.loadFileBillStatus == LoadStatus.failure) {
          LoadingDialogTransparent.hide(context);
          cubit.setDownloadFileStatus();
          cubit.setDownloadFileBillStatus();
          openCustomDialog(
            context: context,
            dialog: DialogNotify(
              content: state.message,
              showIcon: false,
            ),
          );
        }

        Future<bool> requestOtp() async {
          SharedPreferences sharedPreferences =
              await SharedPreferences.getInstance();
          String phoneNumber =
              sharedPreferences.getString(AppConstant.KEY_PHONE_NUMBER_VCC);
          final deviceInfo = await CommonUtils.getDeviceInfo();
          PaymentPeriodsConfirmDto dto = PaymentPeriodsConfirmDto(
              paymentId: item.paymentId,
              otpId: cubit.state.otp.otpId,
              deviceId: deviceInfo.deviceId,
              isSmartOtp: "N",
              duration: cubit.state.otp.duration);
          if (cubit.state.fileInvoice != null) {
            dto.fileBill = cubit.state.fileInvoice.filePath;
          }
          Uint8List imageSignature;
          if ((cubit.state.signatureFile?.signaturePath ?? '').isEmpty) {
            imageSignature =
            await CommonUtils.resizeUint8List(cubit.state.imageSign, 105, 85);
          }

          if (!cubit.state.signedReal &&
              ((cubit.state.imageSign != null &&
              cubit.state.imageSign.isNotEmpty) || (cubit.state.signatureFile?.signaturePath ?? '').isNotEmpty)) {
            bool result = await Navigator.push(
              context,
              OtpPeriodsPage.route(
                arguments: OtpPeriodsArguments(
                  phoneNumber: phoneNumber,
                  paymentPeriodsConfirmDto: dto,
                  imageSign: imageSignature,
                  signaturePath: cubit.state.signatureFile?.signaturePath,
                ),
              ),
            );
            // Navigator.of(context).pop();
            if (result != null && result) {
              cubit.setSignedReal(true);
            }
            cubit.setOtpRequestStatus();
            return result;
          }
        }

        if (state.createOtpStatus == LoadStatus.loading) {
          LoadingDialogTransparent.show(context);
        } else if (state.createOtpStatus == LoadStatus.success) {
          LoadingDialogTransparent.hide(context);
          requestOtp().then((value) => value);
        }
      });
      return subscription.cancel;
    }, [cubit]);
    return _rootWidget(context, item, cubit);
  }

  Widget _rootWidget(BuildContext context, PaymentPeriodsDto item,
      PaymentPeriodsDetailCubit cubit) {
    final SignatureController _controller = SignatureController(
      penStrokeWidth: 3,
      penColor: Color(0xFF283AD2),
      exportBackgroundColor: Colors.transparent,
      onDrawStart: () => print('Start draw!'),
      onDrawEnd: () => print('End draw!'),
    );
    _controller.addListener(() async {
      if (_controller.isEmpty) {
        log("SignatureController");
        cubit.setImageSign(Uint8List(0));
        cubit.setSignedTemp(false);
      }
      if (_controller.isNotEmpty) {
        log("SignatureController1");
        Uint8List imageSign = await _controller.toPngBytes();
        cubit.setImageSign(imageSign);
        cubit.setSignedTemp(true);
      }
    });
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppStrings.payment_periods_detail_title,
          style: TextStyle(
            color: Colors.black,
          ),
        ),
        foregroundColor: Colors.black,
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 1,
      ),
      body: BlocProvider(
        create: (context) => PaymentPeriodsDetailCubit(),
        child: Container(
          color: Colors.white,
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Expanded(
                child: Container(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.grayBackgroundRoadmap,
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${item.outsourceCode ?? ''} - ${item.outsourceName ?? ''}",
                                style: TextStyle(
                                    color: AppColors.backLighterColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              Text(
                                item.positionName ?? '',
                                style: TextStyle(
                                    color: AppColors.greyCertification,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              Text(
                                item.unitName ?? '',
                                style: TextStyle(
                                  color: AppColors.grayIcon,
                                  fontSize: 14,
                                ),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              Divider(
                                height: 1,
                              ),
                              _buildSubItem(
                                  AppStrings.period_date,
                                  "${AppStrings.month} ${item.periodDate ?? ''}",
                                  AppColors.grayTextTitle),
                              _buildSubItem(
                                  AppStrings.payment_month,
                                  "${AppStrings.month} ${item.prevPeriodDate ?? ''}",
                                  AppColors.grayTextTitle),
                              _buildSubItem(
                                  AppStrings.create_date,
                                  "${item.createdDate ?? ''}",
                                  AppColors.grayTextTitle),
                              BlocBuilder<PaymentPeriodsDetailCubit,
                                  PaymentPeriodsDetailState>(
                                bloc: cubit,
                                buildWhen: (previous, current) =>
                                    previous.signedReal != current.signedReal,
                                builder: (context, state) {
                                  bool _isSigned = cubit.state.signedReal;
                                  return _buildSubItem(
                                      AppStrings.status,
                                      _isSigned
                                          ? AppStrings.sign_done
                                          : item.status == WAIT_SIGN ? AppStrings.sign_wait : AppStrings.un_sign,
                                      _isSigned
                                          ? AppColors.greenCharacter
                                          : AppColors.orangeCharacter);
                                },
                              ),
                            ],
                          ),
                        ),
                        _buildFileBillWidget(context, cubit, item),
                        SizedBox(
                          height: 32,
                        ),
                        Text(
                          AppStrings.volume_work_detail,
                          style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                              color: AppColors.grayTextTitle),
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        Text(
                          AppStrings.oft_volume_work_detail,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.greyCertification,
                          ),
                        ),
                        GestureDetector(
                          child:
                              _buildItemFile(EnumWorkDetail.WORK_DETAIL.name),
                          onTap: () {
                            cubit.getPaymentPeriodsDetail(
                                item.paymentId,
                                EnumWorkDetail.WORK_DETAIL.value,
                                EnumWorkDetail.WORK_DETAIL.name);
                          },
                        ),
                        GestureDetector(
                          child:
                              _buildItemFile(EnumWorkDetail.WORK_MAINTAIN.name),
                          onTap: () {
                            cubit.getPaymentPeriodsDetail(
                                item.paymentId,
                                EnumWorkDetail.WORK_MAINTAIN.value,
                                EnumWorkDetail.WORK_MAINTAIN.name);
                          },
                        ),
                        GestureDetector(
                          child:
                              _buildItemFile(EnumWorkDetail.WORK_SALARY.name),
                          onTap: () {
                            cubit.getPaymentPeriodsDetail(
                                item.paymentId,
                                EnumWorkDetail.WORK_SALARY.value,
                                EnumWorkDetail.WORK_SALARY.name);
                          },
                        ),
                        SizedBox(
                          height: 16,
                        ),
                        Text(
                          AppStrings.oft_volume_work_payment,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.greyCertification,
                            height: 1.5,
                          ),
                        ),
                        SizedBox(
                          height: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Column(
                children: [
                  Divider(
                    height: 1,
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  BlocBuilder<PaymentPeriodsDetailCubit,
                      PaymentPeriodsDetailState>(
                    bloc: cubit,
                    buildWhen: (previous, current) =>
                        previous.signedReal != current.signedReal ||
                        previous.fileInvoice != current.fileInvoice,
                    builder: (context, state) {
                      return (!cubit.state.signedReal)
                          ? SizedBox(
                              width: double.infinity,
                              child: Opacity(
                                opacity: (item.isTax == 1 &&
                                            cubit.state.fileInvoice != null &&
                                            cubit.state.fileInvoice.filePath !=
                                                null) ||
                                        item.isTax != 1 ||
                                        item.isTax == null
                                    ? 1
                                    : 0.5,
                                child: item.status == WAIT_SIGN
                                    ? ElevatedButton(
                                        child: Text(
                                          AppStrings.signature_confirmation,
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white,
                                          ),
                                        ),
                                        onPressed: () async {
                                          if ((item.isTax == 1 &&
                                                  cubit.state.fileInvoice !=
                                                      null &&
                                                  cubit.state.fileInvoice
                                                          .filePath !=
                                                      null) ||
                                              item.isTax != 1 ||
                                              item.isTax == null) {
                                            showDialog(
                                                context: context,
                                                barrierDismissible: true,
                                                builder:
                                                    (BuildContext context) {
                                                  return _bottomSheetSign(
                                                      cubit, _controller);
                                                });
                                          }
                                        },
                                        style: ButtonStyle(
                                          backgroundColor:
                                              MaterialStateProperty.all<Color>(
                                                  AppColors.primaryColor),
                                          padding: MaterialStateProperty.all<
                                                  EdgeInsets>(
                                              EdgeInsets.symmetric(
                                                  vertical: 15)),
                                          shape: MaterialStateProperty.all<
                                              RoundedRectangleBorder>(
                                            RoundedRectangleBorder(
                                              borderRadius: BorderRadius.all(
                                                Radius.circular(25),
                                              ),
                                            ),
                                          ),
                                          side: MaterialStateProperty
                                              .all<BorderSide>(BorderSide(
                                                  style: BorderStyle.none)),
                                          textStyle: MaterialStateProperty.all<
                                                  TextStyle>(
                                              TextStyle(color: Colors.white)),
                                          elevation:
                                              MaterialStateProperty.resolveWith(
                                                  (states) => 0),
                                        ),
                                      )
                                    : SizedBox(),
                              ),
                            )
                          : SizedBox();
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubItem(String title, String text, Color color) {
    return Container(
      margin: EdgeInsets.only(top: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.greyCertification,
            ),
          ),
          SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
                fontSize: 14, color: color, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _buildItemFile(String nameFile) {
    return Container(
      margin: EdgeInsets.only(top: 16),
      width: double.infinity,
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.bgGrayLighter,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            AppVectors.icPdfFormat,
            width: 32,
            height: 32,
          ),
          SizedBox(
            width: 20,
          ),
          Text(
            nameFile,
            style: TextStyle(
                fontSize: 14,
                color: AppColors.grayIcon,
                fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildImageInvoice(
      BuildContext context,
      PaymentPeriodsDetailCubit cubit,
      PaymentPeriodsDto item,
      String fileName) {
    bool isSigned = cubit.state.signedReal;
    return Stack(
      children: <Widget>[
        GestureDetector(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: Container(
              height: 100,
              width: 100,
              color: AppColors.bgFileListIncome,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Center(
                  child: Tooltip(
                    triggerMode: TooltipTriggerMode.tap,
                    message: fileName,
                    child: Text(
                      fileName,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
            ),
          ),
          onTap: () async {
            if (item.fileBill != null && item.fileBill.attachmentId != null && item.fileBill.fileName == cubit.state.fileInvoice.fileName) {
              cubit.downloadFileBillById(
                  item.fileBill.attachmentId, item.fileBill.checkSum);
            } else {
              File imageFile = File(cubit.state.fileInvoice.filePath);
              Uint8List imageBytes = await imageFile.readAsBytes();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PaymentPeriodsViewPdf(
                    arguments: PaymentPeriodsViewPdfArgument(
                      title: fileName,
                      file: imageBytes,
                      isInvoiceFile: true,
                    ),
                  ),
                ),
              );
            }
          },
        ),
        !isSigned && item.status != UNSIGN
            ? Positioned(
                right: 5,
                top: 5,
                child: Container(
                  width: 20,
                  height: 20,
                  padding: EdgeInsets.zero,
                  margin: EdgeInsets.zero,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                        style: BorderStyle.solid,
                        color: AppColors.borderIncomeTax),
                    borderRadius: BorderRadius.all(Radius.circular(20.0)),
                  ),
                  child: GestureDetector(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return ShowDialogAskDelete(
                            title: AppStrings.youDefinitelyDeleteInvoiceSale,
                          );
                        },
                      ).then((value) async {
                        if (value != null && value) {
                          cubit.removeFile();
                        }
                      });
                    },
                    child: SvgPicture.asset(
                      AppVectors.icDeleteIncome,
                      fit: BoxFit.cover,
                      height: 1,
                      width: 1,
                    ),
                  ),
                ),
              )
            : SizedBox(),
      ],
    );
  }

  Widget _bottomSheetSign(
      PaymentPeriodsDetailCubit cubit, SignatureController _controller) {
    Future.microtask(
            () => cubit.getDefaultSignature(TypeSign.WORKLOAD_VERIFICATION.name));

    return BlocBuilder<PaymentPeriodsDetailCubit, PaymentPeriodsDetailState>(
      bloc: cubit,
      buildWhen: (previous, current) =>
          previous.signedTemp != current.signedTemp ||
          previous.imageSign != current.imageSign ||
          previous.loadDefaultSignature != current.loadDefaultSignature,
      builder: (context, state) {
        final bool hasSignature = (state.signatureFile?.signaturePath ?? '').isNotEmpty;
        final bool needsSignature = state.signatureFile != null && state.signatureFile?.signaturePath == null;
        final screenWidth = MediaQuery.of(context).size.width;
        final signatureHeight = MediaQuery.of(context).size.height * 0.4;
        if (needsSignature)
          return _dialogNeedsSignature(context);
        return Dialog(
          alignment: Alignment.bottomCenter,
          insetPadding: EdgeInsets.zero,
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            height: signatureHeight + 180,
            width: screenWidth,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 16,
                  right: 16,
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: SvgPicture.asset(
                      AppVectors.ic_delete_type_3,
                    ),
                  ),
                ),
                if (state.imageSign == null ||
                    (state.imageSign != null && state.imageSign.isEmpty) || !hasSignature) ...[
                  Align(
                    alignment: Alignment.center,
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 25),
                      child: Text(
                        AppStrings.sign_here,
                        style: AppTextStyle.blackS14Normal,
                      ),
                    ),
                  ),
                ],
                Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Text(
                        AppStrings.employee_signature,
                        style: AppTextStyle.blackS16W600,
                      ),
                    ),
                    Divider(height: 1),

                    if ((state.signatureFile?.signaturePath ?? '').isNotEmpty)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          "${AppConfigs.baseUrl}/public/image?path=${state.signatureFile.signaturePath}",
                          width: screenWidth - 38,
                          height: signatureHeight,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) =>
                              Center(child: Text('Không thể tải ảnh chữ ký')),
                        ),
                      )
                    else
                      Opacity(
                        opacity: 0.8,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.backgroundItemListView,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Stack(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(5),
                                  child: Signature(
                                    controller: _controller,
                                    backgroundColor:
                                    AppColors.backgroundItemListView,
                                    height:
                                    MediaQuery.of(context).size.height * 0.4,
                                    width: MediaQuery.of(context).size.width - 38,
                                  ),
                                ),
                                Visibility(
                                  visible: state.imageSign != null &&
                                      state.imageSign.isNotEmpty,
                                  child: Positioned(
                                    top: 0,
                                    right: 0,
                                    child: InkWell(
                                      onTap: () {
                                        _controller.clear();
                                      },
                                      child: Container(
                                        width: 38,
                                        height: 38,
                                        color: AppColors.backgroundItemListView,
                                        // color: Colors.green,
                                        child: Padding(
                                          padding: const EdgeInsets.all(8),
                                          child: SvgPicture.asset(
                                            AppVectors.ic_clear_sign,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    Opacity(
                      opacity: cubit.state.signedTemp ||
                              (state.imageSign != null &&
                                  cubit.state.imageSign.isNotEmpty) || hasSignature
                          ? 1
                          : 0.5,
                      child: PrimaryButton(
                        borderRadius: BorderRadius.circular(24),
                        title: AppStrings.confirm,
                        titleStyle: AppTextStyle.whiteS16W600,
                        height: 48,
                        padding:
                            EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                        color: AppColors.primaryColor,
                        borderColor: AppColors.primaryColor,
                        onTap: () async {
                          if (cubit.state.signedTemp ||
                              (state.imageSign != null &&
                                  cubit.state.imageSign.isNotEmpty) ||
                              hasSignature) {
                            cubit.createOtp();
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Dialog _dialogNeedsSignature(BuildContext context) {
    return Dialog(
      alignment: Alignment.bottomCenter,
      insetPadding: EdgeInsets.zero,
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                Center(
                  child: Text(
                    AppStrings.employee_signature,
                    style: AppTextStyle.blackS16W600,
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  child: InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: SvgPicture.asset(AppVectors.ic_delete_type_3),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              AppStrings.needs_signature_image,
              style: AppTextStyle.blackS14Normal,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            PrimaryButton(
              borderRadius: BorderRadius.circular(24),
              title: AppStrings.confirm,
              titleStyle: AppTextStyle.whiteS16W600,
              height: 48,
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              color: AppColors.primaryColor,
              borderColor: AppColors.primaryColor,
              onTap: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileBillWidget(BuildContext context,
      PaymentPeriodsDetailCubit cubit, PaymentPeriodsDto item) {
    return BlocBuilder<PaymentPeriodsDetailCubit, PaymentPeriodsDetailState>(
        bloc: cubit,
        buildWhen: (previous, current) =>
            previous.signedReal != current.signedReal,
        builder: (context, state) {
          return (item.isTax == 1 && item.status != UNSIGN) || item.fileBill != null
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 20,
                    ),
                    Text(
                      AppStrings.order_invoice,
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                          color: AppColors.grayTextTitle),
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    Text(
                      AppStrings.order_invoice_desc,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.greyCertification,
                      ),
                    ),
                    SizedBox(
                      height: 16,
                    ),
                    BlocBuilder<PaymentPeriodsDetailCubit,
                        PaymentPeriodsDetailState>(
                      buildWhen: (previous, current) =>
                          previous.loadFileStatus != current.loadFileStatus,
                      builder: (context, state) {
                        if (cubit.state.loadFileStatus == LoadStatus.success) {
                          return _buildImageInvoice(context, cubit, item,
                              cubit.state.fileInvoice.fileName);
                        } else {
                          return GestureDetector(
                            onTap: () async {
                              cubit.onOpenMultiFile();
                            },
                            child: SvgPicture.asset(AppVectors.icUploadFile),
                          );
                        }
                      },
                    ),
                  ],
                )
              : SizedBox();
        });
  }
}
