import 'package:flutter/material.dart';

class AppColors {
  static const primaryColor = Color(0xFFEE0033);

  static final grayBackground = Color(0xFFF2F2F2);
  static final grayBackgroundRoadmap = Color(0xFFFAFAFA);
  static final green = Color(0xFF2DB2A2);
  static final gray = Color(0xFF82878B);
  static final grayIcon = Color(0xFF525252);
  static final greyBorder = Color(0xFFD7D6D6);
  static const greyColor = Color(0xFFCACDD2);
  static const greyBlackColor = Color(0xFF636363);
  static final grey = Color(0xFFB5B4B4);
  static final dividerColor = Color(0xFFD7D6D6);
  static final lightGrey = Color(0xFFDAD9D9);
  static final outerSpaceColor = Color(0xFF44494D);
  static final backLighterColor = Color(0xFF2A2A2A);
  static final bgGrayLighter = Color(0xFFF4F4F4);
  static final hintTextSearchColor = Color(0xFFAAAAAA);
  static final bgOrangeTag= Color(0xFFFFEEEF);

  static final blueColor = Color(0xFF007AFF);
  static final blueColorLighter = Color(0xFF2D84FF);
  static final blackColor = Color(0xFF212121);
  static final redLight = Color(0xFFE7C7CD);

  static final backgroundItemListView = Color(0xFFF2F2F2);
  static final blueHeader = Color(0xFF174269);
  static final orangeError = Color(0xFFBE6221);
  static final orangeAccent = Color(0xFFfff3eb);
  static final orangeCharacter= Color(0xFFFF6B00);
  static final orangeSecondary = Color(0xFFfeb179);
  static final greenAccent = Color(0xFFe8ffec);
  static final greenCharacter = Color(0xFF39B54A);
  static final greenSecondary = Color(0xFF60c56d);
  static final titleGrey = Color(0xFFF2F2F2);
  static final redBlur = Color(0xFFFDEAEC);
  static final backBoxShadow = Color(0x4D000000);
  static final borderColor = Color(0xFFB5B4B4);
  static final buttonCancelColor = Color(0xFF82878B);
  static final greyTextBottomBar = Color(0xFF737373);
  static final grayTextTitle = Color(0xFF404040);
  static final grayBlackTitle = Color(0xFF2F2F2F);

  static final greyBottomSheet = Color(0xFF5A5A5A);

  static final pinkButtonLogOut = Color(0xFFFDEAEC);
  static final backgroundCloseButton = Color(0xFFEEEEEE);
  static final calendarColor = Color(0xFF44494D);
  static final greyColorIncome = Color(0xFFB5B4B4);
  static final greyColorBoderIncome = Color(0xFFD7D6D6);
  static final greyIndicator = Color(0xFFE0E0E0);
  static final greyTextIncome = Color(0xFF757575);
  static final blackTextIncome = Color(0xFF424242);
  static final greyTextFieldLable = Color(0xFF616161);
  static final bgAdRoadmapIncome = Color(0xFFF5F5F5);
  static final greyTitleListIncome = Color(0xFFa3a3a3);
  static final bgFileListIncome = Color(0xFFF1EFEF);
  static final greyRoadmap = Color(0xFFE0E0E0);
  static final greyLoginOtp = Color(0xFF535353);

  static final pinkItemHome = Color(0xFFFDEAEC);
  static final bgAdRoadmap = Color(0xFFF5F5F5);
  static final greyTextRoadmap = Color(0xFF757575);
  static final blackTextRoadmap = Color(0xFF424242);
  static final hintTextField = Color(0xFF9E9E9E);
  static final neutral2_5 = Color(0xFF82878B);
  static final greyTextTabBar = Color(0xFF7E7D7D);
  static final greyLogoFilter = Color(0xFF797979);
  static final greyTextTitle = Color(0x80000000);
  static final borderIncomeTax = Color(0x11707070);

  static final greenCertification = Color(0xFF00884F);
  static final orangeCertification = Color(0xFFF59A23);
  static final redCertification = Color(0xFFD9001B);
  static final greyCertification = Color(0xFF7F7F7F);
  static final redBlurCertification = Color(0xFFE60A32);
  static final redSecondary = Color(0xFFE81D2C);

  static final unselectRadioButton = Color(0xFFB8BCC4);
  static final bgButtonSignElectric = Color(0xFFE6F2FF);
  static final textButtonSignElectric = Color(0xFF1C89FF);
  static final textItemCardColor = Color(0xFF525252);
  static final textDateTimeCardColor = Color(0xFF2A2A2A);
  static final borderDateTimeCardColor = Color(0xFFEAEAEA);
  static final textDateTimeDividerColor = Color(0xFFD4D4D4);
  static final textInfoColor = Color(0xFF7F7F7F);
  static final greyButtonColor = Color(0xFFF4F4F4);
  static final greyBackButtonColor = Color(0xFFF5F5F5);
  static final primarySurfaceColor = Color(0xFFFFEEEF);
  static final textLocationVacationColor = Color(0xFF525252);
  static final warringSurfaceColor = Color(0xFFFFF3EB);
  static final successSurfaceColor = Color(0xFFE8FFEC);
  static final errorSurfaceColor = Color(0xFFFFF1F0);
}
