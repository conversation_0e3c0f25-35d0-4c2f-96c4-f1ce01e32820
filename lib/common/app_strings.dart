class AppStrings {
  AppStrings._();

  ///MESSAGE_ERROR
  static ERROR_TEXT_FIELD_REGISTER({String error}) => '$error đang nhập không đúng.';
  static ERROR_TEXT_FIELD_TYPE({String error}) => '$error không hợp lệ.';
  static ERROR_TEXT_NOT_EMPTY({String error}) => '$error không được để trống.';
  static ERROR_TEXT_PERSON_NOT_EMPTY({String error, int number}) => '$error của người nhận quà $number không được để trống.';
  static ERROR_TEXT_MAX_SIZE({String error}) => '$error chỉ được nhập tối đa 255 ký tự.';
  static ERROR_MAX_CHILD_CATEGORY({String error}) => '$error đã đủ số lượng.';
  static ERROR_LACK_CHILD_CATEGORY({String error}) => '$error chưa chọn đủ số lượng.';
  static ERROR_PHONE_NUMBER_SIZE({String error}) => '$error chưa đủ 10 ký tự.';
  static ERROR_PHONE_NUMBER_OVER_SIZE({String error}) => '$error đã quá 10 ký tự.';
  static ERROR_PHONE_NUMBER_FORMAT({String error}) => '$error chưa đúng định dạng.';
  static ERROR_ID_CARD_FORMAT({String error}) => '$error chưa đúng định dạng.';
  static const ERROR_SIZE_SHOE = 'Bạn chưa chọn size giày bảo hộ';
  static const regExp = "[0-9a-zA-Z]";

  ///TYPE_DATE
  static const TYPE_DD_MM_YYYY = 'dd/MM/yyyy';
  static const TYPE_MM_YYYY = 'MM/yyyy';
  static const YYYY = 'yyyy';

  ///TYPE_STATUS
  static String statusResignationForm(int status) {
    switch (status) {
      case 2:
        return waitManagementConfirm;
      case 3:
        return managerAgreedQuit;
      case 4:
        return managerRefusedQuit;
      case 5:
        return manager_signing;
      case 6:
        return waitTCTResolve;
      case 7:
        return unitRefusedQuit;
      case 8:
        return presentResignationDecision;
      case 9:
        return CEORefusedQuit;
      case 10:
        return hadDecisionQuit;
      case 11:
        return hasRetired;
      default:
        return '';
    }
  }

  static String statusLaborContract(int status) {
    switch (status) {
      case 0:
        return "Đã tạo HĐ tạm thời";
      case 1:
        return "Đã gửi HĐ cho NLĐ";
      case 2:
        return "NLĐ đã ký/ Chưa trình ký VO";
      case 3:
        return "Đang trình ký VO";
      case 4:
        return "Trình ký VO thất bại";
      case 5:
        return "Đã ký VO";
      default:
        return '';
    }
  }

  static String statusLaborContractVCC({int type, int contractStatus, int resignStatus, bool isActive}) {
    if (type==5) {
      switch (contractStatus) {
        case 5:
          return "Đang gửi cho NLĐ ký";
        case 6:
          return "Người Lao Động đã ký/Chưa trình ký Voffice";
        case 7:
          return "Đang trình ký VO";
        case 8:
          return "Đã ban hành";
        case 9:
          return "Chỉ huy DV từ chối ký";
      }
    }
    if (type==4) {
      switch (resignStatus) {
        case 13:
          return "Chờ NLĐ ký";
        case 14:
          return "NLĐ đã ký";
        case 15:
          return "Đang trình ký VO";
        case 16:
          return "Chỉ huy đơn vị từ chối ký";
      }
      if (isActive) {
        return "Hợp đồng đã ban hành";
      }
    }
    return '';
  }

  static String statusCommitCertificate(int status) {
    switch (status) {
      case 0:
        return "Dự thảo";
      case 1:
        return "Chờ NV P.TCLĐ phê duyệt";
      case 2:
        return "Đã duyệt";
      case 3:
        return "NV P.TCLĐ từ chối duyệt";
      case 4:
        return "Chờ NLĐ ký cam kết";
      case 5:
        return "Chờ lãnh đạo P.TCLĐ phê duyệt";
      case 6:
        return "Lãnh đạo P.TCLĐ từ chối duyệt";
      case 7:
        return "Chờ TCLĐ đơn vị phê duyệt";
      case 8:
        return "TCLĐ đơn vị từ chối duyệt";
      default:
        return '';
    }
  }

  static String statusCommitCertificateAddDocs(
      {int signStatus, int approveCommitStatus}) {
    if (signStatus == 0) {
      return "Chưa ký";
    }
    if (signStatus == 1 && approveCommitStatus == 0) {
      return "Đã ký chờ TCLĐ đơn vị phê duyệt";
    }
    if (approveCommitStatus == 2) {
      return "TCLĐ đơn vị từ chối duyệt";
    }
    if (approveCommitStatus == 1) {
      return "TCLĐ đơn vị đã duyệt";
    }
  }

  ///TEXT
  static const app_name = "VCC - Hồ sơ điện tử";
  static const common_notification = "Thông báo";
  static const salary_user_manual = "Thuật ngữ và hướng dẫn sử dụng";
  static const salary_user_manual_short = "Thuật ngữ và HDSD";
  static const salary_user_manual_see_detail = "Xem chi tiết";
  static const empty_data = "Không có dữ liệu";
  static const error_data = "Có lỗi xảy ra!";
  static const notification = "Thông báo";
  static const read_and_confirm = "Đọc và xác nhận";
  static const register_labor_protection = "Đăng ký đồ BHLĐ";
  static const register_labor_protection_detail = "Chi tiết đăng ký đồ BHLĐ";
  static const confirm_granted = "Xác nhận đồ bảo hộ được cấp";
  static const confirm_granted_detail = "Chi tiết đồ bảo hộ được cấp";
  static const height = "Chiều cao";
  static const weight = "Cân nặng";
  static const cm = "cm";
  static const kg = "kg";
  static const size_shoe = "Size giày bảo hộ";
  static const heightWeight = "Chiều cao, cân nặng";
  static const sizeShirt = "Số đo áo";
  static const longShirt = "Dài áo";
  static const suggestLongShirt = "Dài áo từ 50-78cm";
  static const broadShoulders = "Rộng vai";
  static const suggestBroadShoulders = "Rộng vai từ 35-65cm";
  static const longSleeve = "Dài tay";
  static const suggestLongSleeve = "Dài tay từ 30-70cm";
  static const chest = "Ngực";
  static const suggestChestCircumference = "Vòng ngực từ 70-120cm";
  static const sizePants = "Số đo quần";
  static const pantsLength = "Dài quần";
  static const suggestPantsLength = "Dài quần từ 75-109cm";
  static const waistCircumference = "Vòng bụng";
  static const suggestWaistCircumference = "Vòng bụng từ 50-125cm";
  static const buttCircumference = "Vòng mông";
  static const suggestButtCircumference = "Vòng mông từ 65-120cm";
  static const cancel = "Hủy bỏ";
  static const confirm = "Xác nhận";
  static const delete = "Xóa";
  static const suppliesProvided = "Dự kiến đồ BHLĐ ";
  static const blank = "";
  static const register_success = "Đăng ký thành công";
  static const register_fail = "Đăng ký thất bại";
  static const register = "Đăng ký";
  static const register_uppercase = "ĐĂNG KÝ";
  static const granted = "Được cấp";
  static const laborProtectionGear = "Đồ bảo hộ lao động";
  static const has_error = "Đã có lỗi xảy ra!";
  static const file_not_found = "Không tìm thấy file!";
  static const contract_not_found = "Không tìm thấy hợp đồng!";
  static const no_register = 'Không có đồ bảo hộ lao động cần đăng ký';
  static const no_granted = 'Không có đồ bảo hộ lao động cần xác nhận';
  static const lock_status = 'Trạng thái đợt';
  static const status = 'Trạng thái';
  static const registered = 'Đã đăng ký';
  static const not_register = 'Chưa đăng ký';
  static const cancel_register = 'Hủy đăng ký';
  static const detail = 'Xem chi tiết';
  static const locked = 'Đã khóa';
  static const unlock = 'Chưa khóa';
  static const completed = "Hoàn thành";
  static const confirmed = "Đã xác nhận";
  static const not_confirm = "Chưa xác nhận";
  static const date_granted = "Ngày cấp";
  static const date_confirm = "Ngày xác nhận";
  static const view_and_confirm = "Xem và xác nhận";
  static const confirm_success = "Xác nhận thành công";
  static const confirm_fail = "Xác nhận thất bại";
  static const edit = "Chỉnh sửa";
  static const piece = "cái";
  static const register_date = "Ngày đăng ký";
  static const image_labor = "Hình ảnh";
  static const year = "năm";
  static const measure_shirt_length = "Cách lấy số đo Dài áo (đo từ đốt sống cổ tới qua mông)";
  static const measure_shoulder_width = "Rộng Vai (đo từ đầu vai bên trái qua đầu vai bên phải)";
  static const measure_arm_length = "Dài Tay (đo từ đầu vai tới ngang mu bàn tay)";
  static const measure_chest = "Ngực (đo một vòng qua phần nở nhất của ngực)";
  static const measure_pants_length = "Số đo quần Dài Quần (đo từ cạp quần tới chạm gót chân)";
  static const measure_waist_circumference = "Vòng Bụng (đo phần cạp quần)";
  static const measure_butt_circumference = "Vòng Mông (đo 01 vòng qua phần nở nhất của mông)";
  static const title_notify_contract = "Danh sách văn bản thông báo";
  static const tab_warining_task = "Biên bản";
  static const tab_notify = "Văn bản thông báo";
  static const filter = "Bộ lọc";
  static const violation_type = "Loại vi phạm";
  static const home_title = "Hồ sơ VCC";
  static const close = "Đóng";
  static const apply = "Áp dụng";
  static const from_month = "Từ tháng";
  static const to_month = "Đến tháng";
  static const no_report = "Không có biên bản nào";
  static const full_name = "Họ và tên";
  static const unit_block = "Khối đơn vị";
  static const unit = "Đơn vị";
  static const center = "Trung tâm";
  static const branch = "Chi nhánh";
  static const approve = "Đã xác nhận";
  static const not_approve = "Chưa xác nhận";
  static const no_notify_contract = 'Không có văn bản thông báo nào';
  static const no_commit_contract = 'Không có văn bản cam kết nào';
  static const content = 'Nội dung';
  static const content_notify_contract = "Thông báo không ký tiếp HĐLĐ";
  static const approve_success = "Xác nhận thành công";
  static const approve_fail = "Xác nhận không thành công";
  static const document = "Văn bản";
  static const month = "Tháng";
  static const commit = "Cam kết";
  static const sign_commit_bhxh_title = "Ký cam kết bàn giao sổ BHXH về TCT";
  static const view_commit_bhxh_title = "Chi tiết cam kết bàn giao sổ BHXH về TCT";
  static const sign_date = "Ngày ký";
  static const sign_date_commit = "Ngày ký";
  static const not_yet_signed = "Chưa ký";
  static const signed = "Đã ký";
  static const sign_commit = "Ký cam kết";
  static const other = "Khác";
  static const sign_probationary_contract = "Ký hợp đồng thử việc";
  static const probationary_contract = "Hợp đồng thử việc";
  static const sing_labor_contract = "Ký hợp đồng lao động";
  static const labor_contract = "Hợp đồng lao động";
  static const sign_contract_addendum = "Ký phụ lục hợp đồng";
  static const sign_electronic_text = "Ký văn bản điện tử";
  static const sign_contract_electronic = "Ký hợp đồng điện tử";
  static const sign_document = "Văn bản điện tử";
  static const sign_contract_commit = "Văn bản điện tử";
  static const sign_electronic = "Ký điện tử";
  static const sign_commit_fail = "ký cam kết không thành công";
  static const sign_commit_success = "Ký cam kết thành công";
  static const not_has_commit_to_sign = "Không có cam kết cần ký";
  static const download_document = "Tải xuống văn bản";
  static const listCommitSocialInsurance = "Danh sách biên bản bàn giao sổ BHXH";
  static const do_not_commit = "Không cam kết";
  static const commitment_is_valid = "Cam kết còn thời hạn";
  static const commitment_expires = "Cam kết hết hạn";
  static const commitment_signed = "Cam kết đã ký";
  static const sign_success = "Đã ký thành công";

  static const commitment_information_insurance = "Thông tin cam kết bàn giao sổ BHXH";
  static const verify = "Xác nhận";
  static const fullName = "Họ và tên";
  static const employeeCode = "Mã nhân viên";
  static const gender = "Giới tính";
  static const dateOfBirth = "Sinh ngày";
  static const idNumber = "Số CCCD";
  static const productAt = "Cấp tại";
  static const dateRange = "Ngày cấp";
  static const phoneNumber = "Số điện thoại";
  static const socialInsuranceBookNumber = "Số sổ BHXH công ty cấp";
  static const position = "Chức danh";
  static const bookIssuingCompany = "Công ty cấp sổ";
  static const reasonNotHandingOver = "Lý do chưa bàn giao sổ BHXH";
  static const dateHandingOver = "Ngày cam kết bàn giao sổ BHXH";
  static const currentProvince = "Tỉnh thành hiện tại";
  static const noInformation = "Không có thông tin cam kết";
  static const no_Information_commit = "Không có thông tin cam kết";
  static const signCommitmentHandOverBook = "Ký cam kết bàn giao sổ BHXH về TCT";
  static const inforSocialInsurance = "BHYT, BHXH";
  static const resignationForm = "Đơn nghỉ việc";
  static const confirmResignationForm = "Duyệt đơn nghỉ việc";
  static const rewardEmployee = "Khen thưởng cấp TCT";
  static const workingOnHoliday = "Trực lễ/Tết";

  static const no_data_resignation = "Không có đơn nghỉ việc nào";
  static const create_date = "Ngày tạo";
  static const expected_Date = "Ngày dự kiến nghỉ việc";
  static const manager_confirm_resignation = "Người quản lý xác nhận nghỉ việc";
  static const manager_access_resignation_date = "Ngày quản lý cho phép nghỉ việc";
  static const button_continue = "Tiếp tục";
  static const content_resignation_1 = "Cảm ơn sự đóng góp của đồng chí đối với sự phát triển của Tổng Công ty trong thời gian qua.";
  static const content_resignation_2 = "Mời đồng chí tham gia khảo sát lý do nghỉ việc để giúp cho TCT tạo nên môi trường làm việc tốt nhất trong thời gian tới.";
  static const content_resignation_3 = "(Kết quả khảo sát của đồng chí chúng tôi tuyệt đối giữ bí mật và không gửi cho chỉ huy đơn vị của đồng chí)";
  static const organizationName = "Phòng ban";
  static const expected_date = "Ngày dự kiến nghỉ việc";
  static const reason_resignation = "Lý do xin nghỉ việc";
  static const manager_direct = "Người quản lý trực tiếp";
  static const search_manager = "Tìm kiếm người quản lý";
  static const select_manager = "Chọn người quản lý";
  static const please_select_manager = "Hãy lựa chọn người quản lý";
  static const receiver = "Người bàn giao công việc";
  static const please_select = "Chọn";
  static const please_select_receiver = "Hãy lựa chọn người tiếp quản";
  static const select_receiver = "Chọn người tiếp quản công việc";
  static const search_receiver = "Tìm kiếm người tiếp quản công việc";
  static const create_resignation_success = "Tạo đơn xin nghỉ việc thành công";
  static const update_resignation_success = "Chỉnh sửa đơn xin nghỉ việc thành công";
  static const create_resignation_failure = "Tạo đơn xin nghỉ việc thất bại";
  static const update_resignation_failure = "Chỉnh sửa đơn xin nghỉ việc thất bại";
  static const please_insert_phoneNumber = "Yêu cầu đơn vị bổ sung số điện thoại của bạn để tạo đơn xin nghỉ việc";
  static const warning = "Cảnh báo";
  static const error_expectedDate = "Bạn chưa chọn ngày dự kiến nghỉ việc";
  static const error_selected_expectedDate = "Ngày dự kiến nghỉ việc phải lớn hơn ngày hiện tại";
  static const error_reason_resignation = "Bạn chưa nhập lý do xin nghỉ việc";
  static const error_select_manager = "Bạn chưa chọn người quản lý trực tiếp";
  static const error_select_receiver = "Bạn chưa chọn người bàn giao công việc";

  static const confirm_resignation = "Xác nhận nghỉ việc";
  static const expected_date_retirement = "Ngày dự kiến nghỉ việc";
  static const expected_date_confirm_resignation = "Ngày quản lý cho phép nghỉ việc";
  static const contract_type = "Loại hợp đồng";
  static const reason_quitting_job = "Lý do nghỉ việc";
  static const no_resignations_determined = 'Không có đơn nghỉ việc nào cần xác nhận';
  static const search_hint_text = 'Tìm kiếm mã/email/họ tên';
  static const waitManagementConfirm = 'Chờ quản lý xác nhận';
  static const managerAgreedQuit = 'Quản lý đồng ý nghỉ việc';
  static const managerRefusedQuit = 'Quản lý từ chối nghỉ việc';
  static const unitSubmitDesignation = 'Đơn vị đang trình ký nghỉ việc';
  static const unitRefusedQuit = 'Đơn vị từ chối nghỉ việc';
  static const waitTCTResolve = 'Chờ TCT giải quyết hồ sơ';
  static const presentResignationDecision = 'Đang trình ký QĐ nghỉ việc';
  static const CEORefusedQuit = 'TGĐ từ chối QĐ nghỉ việc';
  static const hadDecisionQuit = 'Đã có QĐ nghỉ việc với HĐLĐ';
  static const hasRetired = 'Đã nghỉ việc với HĐTV';
  static const manager_signing = 'Đơn vị đang trình ký nghỉ việc';
  static const jobTitle = 'Chức danh';
  static const employees = 'Người lao động';
  static const manage = 'Quản lý';
  static const expectedDate = 'Ngày dự kiến nghỉ việc';
  static const confirmDate = 'Ngày xác nhận nghỉ việc';
  static const new_employee_contract = 'Hợp đồng nhân viên mới';
  static const cooperation_agreement = 'Thỏa thuận hợp tác';
  static const allotment_contract = 'Hợp đồng giao khoán';
  static const official_labor_contract = 'Hợp đồng lao động chính thức';
  static const try_job = 'Thử việc';
  static const service_contract = 'Hợp đồng dịch vụ OFT';
  static const person_receives_work = 'Người nhận bàn giao công việc';
  static const direct_manager = 'Người quản lý trực tiếp';
  static const content_management_comments = 'Nội dung nhận xét của quản lý';
  static const manager_confirms_resignation = 'Quản lý xác nhận nghỉ việc';
  static const agree = 'Đồng ý';
  static const disagree = 'Không đồng ý';
  static const select_text = 'Chọn văn bản';
  static const view_resignation_application_file = 'Xem file đơn xin nghỉ việc';
  static const view_debt_confirmation_file = 'Xem file xác nhận công nợ';
  static const view_contract_termination_agreement_file = 'Xem file thỏa thuận chấm dứt HĐLĐ';
  static const type_edit = 'TYPE_EDIT';
  static const type_confirm = 'TYPE_CONFIRM';
  static const type_view = 'TYPE_VIEW';
  static const resignation_letter_details = 'Chi tiết đơn nghỉ việc';
  static const confirmation_resignation = 'Xác nhận nghỉ việc';
  static const edit_resignation_confirmation = 'Chỉnh sửa xác nhận nghỉ việc';
  static const not_insert_note_manager = 'Bạn chưa nhập nội dung nhận xét của quản lý';
  static const not_insert_approved_date_manager = 'Bạn chưa chọn ngày quản lý cho phép nghỉ việc';
  static const not_select_status_resignation = 'Bạn chưa đồng ý hay từ chối đơn nghỉ việc';
  static const create_resignation_form = "Tạo đơn xin nghỉ việc";
  static const edit_resignation_form = "Chỉnh sửa đơn xin nghỉ việc";

  static const hintReason = "Nhập lý do, viết ngắn gọn";
  static const hintPosition = "Nhập chức danh";


  //income tax
  static const taxSettlementForm = "Hình thức quyết toán thuế";
  static const taxDocuments = "Chứng từ thuế";
  static const annualRegistrationPeriod = "Đợt đăng ký năm";
  static const staff = "Nhân viên";
  static const belongsSubject = "Thuộc đối tượng";
  static const selfSettlement = "Tự quyết toán";
  static const authority = "Ủy quyền";
  static const registerSettlementForm = "Đăng ký hình thức quyết toán";
  static const registerTaxSettlementForm = "Đăng ký hình thức QT thuế";
  static const yearRegistration = "Năm đăng ký";
  static const searchTaxCode = "Tra cứu mã số thuế tại đây";
  static const taxCode = "Mã số thuế";
  static const copyPersonalTaxCode = "Bản chụp mã số thuế cá nhân";
  static const agreeTakeFullResponsibility = "Tôi đồng ý hoàn toàn chịu trách nhiệm về thông tin và hồ sơ đăng ký của mình trước Pháp luật";
  static const addImage = "Thêm ảnh";
  static const authenticateIDCardUsingEKYC = "Xác thực CMND/CCCD bằng eKYC";
  static const addFrontPhoto = "Thêm ảnh\nmặt trước";
  static const addBackPhoto = "Thêm ảnh\nmặt sau";
  static const idCardIncome = "Số CMND/CCCD";
  static const issuedBy = "Nơi cấp";
  static const inCategoryOf = "Thuộc diện";
  static const individualsOnlyHaveTaxableIncomeVCC = "Cá nhân chỉ có thu nhập chịu thuế tại VCC";
  static const individualTransferredFromUnitdifferentAboutVCC = "Cá nhân điều chuyển trong Tập đoàn từ đơn vị khác về VCC";
  static const individualsWantSelfFinalizeTaxes = "Cá nhân muốn tự quyết toán thuế";
  static const waitUnitReviewApplication = "Chờ đơn vị xét duyệt hồ sơ";
  static const unitSubmittingDocuments = "Đơn vị đang trình ký hồ sơ";
  static const unitRefusesApplication = "Đơn vị từ chối hồ sơ";
  static const unitSubmittingDocumentsSignature = "Đơn vị đang trình ký hồ sơ";
  static const frontPhoto = "Ảnh mặt trước CMND/ CCCD";
  static const backPhoto = "Ảnh mặt sau CMND/ CCCD";
  static const urlIncomeTax = "https://tracuunnt.gdt.gov.vn";
  static const noIncomeTaxList = 'Không có danh sách đăng ký quyết toán thuế';
  static const captureImage = 'Chụp ảnh';
  static const chooseImage = 'Chọn ảnh';
  static const selectDateIssuanceIDCard = 'Chọn ngày cấp CMND/CCCD';
  static const authorizedSignatory = 'Ký ủy quyền';
  static const transferFromUnit = 'Điều chuyển từ đơn vị';
  static const reason = 'Lý do';
  static const chooseReason = 'Chọn lý do';
  static const uploadFile = 'Tải file';
  static const individualsIncomeUnitsOtherGroup = 'Cá nhân có thu nhập tại đơn vị khác ngoài Tập đoàn';
  static const individualRegisteredFamily = 'Cá nhân đã đăng ký giảm trừ gia cảnh nhưng thông tin đăng ký giảm trừ bị sai, thiếu hồ sơ đăng ký';
  static const taxInvoicesProvingIndividual = 'Hóa đơn chứng từ thuế chứng minh cá nhân có thu nhập phát sinh tại công ty khác ngoài Tập đoàn';
  static const seeDetailRegister = 'Xem chi tiết đăng ký';
  static const cardImageIncome = "Ảnh CMND/CCCD";
  static const updateTaxSettlementForm = "Cập nhật hình thức QT thuế";
  static const electronicAttorney = "Giấy ủy quyền điện tử";
  static const signUpSuccess = "Đăng ký thành công. Hồ sơ của bạn đã được gửi cho đơn vị chờ phê duyệt ";
  static const endFile = ".pdf";
  static const endPngFile = ".png";
  static const endJpgFile = ".jpg";
  static const endJpegFile = ".jpeg";
  static const fileFormatIncorrect = "File không đúng định dạng!";
  static const fileLengthIncorrect = "File không được quá 10MB!";
  static const idCardIncomeError = "Số CMND/CCCD phải có 9 hoặc 12 ký tự";
  static const taxIncomeError = "Mã số thuế không hợp lệ";


  static const oldInsuranceUnit = "Số sổ BHXH đơn vị cũ";
  static const inputOldInsuranceUnit = "Nhập số sổ BHXH đơn vị cũ";
  static const pleaseFillAllMarkedInformationFields = "Vui lòng nhập đầy đủ các trường thông tin có dấu *";
  static const missingBirthCertificatePhoto = "Thiếu ảnh giấy khai sinh";
  static const missingTwoSidedCitizenIDPhoto = "Thiếu ảnh căn cước công dân hai mặt";
  static const missingImageRegistration = "Thiếu ảnh sổ hộ khẩu hoặc giấy xác nhận thông tin cư trú";
  static const birthday = "Ngày sinh";
  static const inputDob = "Nhập ngày/tháng/năm sinh";
  static const codeConfirm = "Mã số:";
  static const codeBHYT = "Số thẻ BHYT";
  static const fullNameConfirm = "Họ và tên:";
  static const birthdayConfirm = "Ngày sinh";
  static const genderConfirm = "Giới tính";
  static const placeConfirm = "Nơi đăng ký KCB";
  static const inforHealthInsuranceConfirm = "1. Thông tin thẻ BHYT:";
  static const confirmHealthInsurance = "Xác nhận thông tin thẻ BHYT";
  static const titleConfirmHealthInsurance = "Xác nhận thẻ BHYT";
  static const male = "Nam";
  static const female = "Nữ";
  static const total = "Tổng số";
  static const image = "hình ảnh";
  static const pleaseProvideInformationAtLeast = "Vui lòng cung cấp thông tin ít nhất 1 thành viên trong hộ gia đình!";
  static const pleaseDeclareHouseholdAnnex = "Đồng chí đã cập nhật thông tin cá nhân thành công! Vui lòng cập nhật thông tin phụ lục HGĐ!";
  static const updateInformationSuccess = "Cập nhật thông tin cá nhân thành công";
  static const theNumberPhotosLimited = "Số lượng ảnh đã quá giới hạn, hãy chọn bớt ảnh đi để thêm mới";
  static const somethingWentWrong = "Đã có vấn đề xảy ra";
  static const loading = "Đang tải";
  static const correctCardInformation = "Thông tin thẻ đúng";
  static const cardInformationWrong = "Thông tin thẻ sai";

  static const confirm_social_insurance_card_success = "Xác nhận thông tin thẻ BHYT thành công !";

  static const salary_transfer = "Lương chuyển khoản";
  static const ask_for_certification = "Xin giấy xác nhận";

  static const infomation_salary = "Thông tin thù lao";
  static const payment_periods = "Danh sách kỳ thanh toán";
  static const payment_periods_title = "Kỳ thanh toán";
  static const payment_periods_detail_title = "Chi tiết thù lao";
  static const payment_periods_from = "Từ kỳ trả";
  static const payment_periods_to = "Đến kỳ trả";
  static const sign_wait = "Chờ ký";
  static const un_sign = "Không ký";
  static const sign_done = "Đã ký";
  static const personal_income_tax = "Cập nhật hồ sơ thuế TNCN";
  static const infomation_collected = "Thông tin thu nhập";
  static const salary = "Khảo sát";
  static const protection_gear = "Đồ BHLĐ";
  static const chung_tu_thue = "Chứng từ thuế TNCN";
  static const thue_TNCN = "Thuế TNCN";
  static const card_labor_safety = "Thẻ ATLĐ";
  static const advancement_roadmap = "Lộ trình thăng tiến";
  static const advancement_roadmap_detail = "Chi tiết lộ trình thăng tiến";
  static const general_information = "Thông tin chung";
  static const user_infomation = "Thông tin cá nhân";
  static const ki_infomation = "Thông tin KI nhân viên";
  static const position_current_standard = "Tiêu chuẩn bậc nghề";
  static const roadmap_standards = "Quá trình phát triển nghề nghiệp";
  static const register_history = "Lịch sử đăng ký";
  static const top_10_vcc = "Top 10 vị trí hot nhất VCC";
  static const department = "Phòng ban";
  static const state_join_group = "Ngày vào tập đoàn";
  static const years_of_experience = "Thâm niên";
  static const join_company_date = "Ngày tuyển dụng";
  static const level = "Trình độ";
  static const education_subject_name = "Chuyên ngành";
  static const self_punishment_type_name = "Kỷ luật";
  static const specialized = "Chuyên ngành";
  static const certificate = "Chứng chỉ";
  static const job_description = "Mô tả công việc";
  static const current_unit = "Đơn vị hiện tại";
  static const current_department = "Phòng ban hiện tại";
  static const current_position = "Chức danh hiện tại";
  static const target_unit = "Đơn vị mục tiêu";
  static const target_department_new = "Phòng ban mục tiêu";
  static const target_position = "Chức danh mục tiêu";
  static const register_advancement_roadmap = "Đăng ký lộ trình thăng tiến";
  static const functional_standards = "Tiêu chuẩn chức danh";
  static const target_title = "Chức danh luân chuyển";
  static const target_department = "Phòng ban luân chuyển";
  static const target_org = "Đơn vị luân chuyển";
  static const experience = "Kinh nghiệm";
  static const select_target_title = "Chọn chức danh đăng ký";
  static const select_advancement_roadmap = "Chọn lộ trình thăng tiến";
  static const search_unit = "Tìm đơn vị";
  static const selected_area = "Khu vực được chọn";
  static const reset = "Thiết lập lại";
  static const select_title = "Chọn chức danh";
  static const select_org = "Chọn đơn vị";
  static const select_center = "Chọn trung tâm";
  static const select_branch = "Chọn chi nhánh";
  static const select_department = "Chọn phòng ban";
  static const roadmap = "Lộ trình";
  static const step = "bước";
  static const not_has_roadmap = "Chưa có lộ trình thăng tiến cho chức danh mục tiêu này !";
  static const view_advancement_roadmap = "Xem thông tin lộ trình thăng tiến";
  static const warning_registered_roadmap_first = "Bạn đã đăng ký lộ trình thăng tiến với chức danh mục tiêu ";
  static const warning_registered_roadmap_last = " trước đó. Bạn có chắc muốn hủy để đăng ký lộ trình thăng tiến mới?";
  static const confirm_registered_roadmap = "Bạn chắc chắn muốn đăng ký lộ trình thăng tiến với chức danh mục tiêu ";
  static const destroyed = "Đã hủy";
  static const view_detail_advance_roadmap = "Xem chi tiết lộ trình thăng tiến";
  static const search_org = "Tìm đơn vị";
  static const search_center = "Tìm trung tâm";
  static const search_branch = "Tìm chi nhánh";
  static const search_department = "Tìm phòng ban";
  static const search_standard = "Tìm chức danh";
  static const not_has_roadmap_standards = "Chưa có tiêu chuẩn lộ trình";
  static const no_information = "Chưa có thông tin";
  static const not_has_info_register_roadmap = "Chưa có thông tin đăng ký lộ trình";
  static const not_has_account = "Bạn chưa có tài khoản ?";
  static const login = "Đăng nhập";
  static const password = "Mật khẩu";
  static const user_name = "Tên người dùng";
  static const login_not_employee = "Đăng nhập với đối tượng ngoài VCC";
  static const login_otp = "Đăng nhập OTP";
  static const input_otp = "Nhập mã xác minh OTP";
  static const please_input_phone_number = "Vui lòng nhập số điện thoại để đăng nhập";
  static const otp_has_been_sent = "Mã xác thực (OTP) đã được gửi qua Tin nhắn số";
  static const please_input_otp = "Hãy nhập mã OTP";
  static const contact_switchboard = "Trường hợp bạn không nhận được mã OTP.\nVui lòng liên hệ tổng đài ******** (Miễn phí)";
  static const contact_otp_period_payment = "Trường hợp bạn không nhận được mã OTP.\nVui lòng liên hệ tổng đài ******** (Miễn phí)";
  static const validity_period = "Thời hạn hiệu lực";
  static const second = "giây";
  static const login_success = "Đăng nhập thành công";
  static const otp_wrong = "Mã OTP không chính xác. Vui lòng kiểm tra lạ";
  static const contract_start_date = "Ngày bắt đầu HĐ";
  static const no_has_document = "Không có văn bản nào";
  static const account_info = "Thông tin tài khoản";
  static const home_page = "Trang chủ";
  static const account = "Tài khoản";
  static const resignation = "Nghỉ việc";
  static const income = "Thu nhập";
  static const view_signed_document = "Xem văn bản điện tử đã ký";
  static const document_content = "Nội dung văn bản";
  static const object = "Đối tượng";
  static const contract_status = "Trạng thái hợp đồng";
  static const indefinite = "Không thời hạn";
  static const access_terms = "Tôi đồng ý với các điều khoản/Điều kiện trong văn bản và tự nguyện ký kết văn bản này.";
  static const employee_signature = "Chữ ký nhân viên";
  static const error_create_otp = "Tạo mã OTP không thành công";
  static const go_to_list = "Đến danh sách";
  static const electronic_power_of_attorney = "Giấy ủy quyền điện tử";
  static const error_upload_sign_file = "Lỗi upload file ký";
  static const error_sign_contract = "Ký hợp đồng không thành công";
  static const error_sign_commit = "Ký cam kết không thành công";
  static const sign_here = "Viết chữ ký của bạn tại đây!";
  static const needs_signature_image  = "Bạn chưa được cấu hình ảnh ký điện tử. Vui lòng liên hệ với đầu mối tỉnh để cấu hình ảnh ký điện tử";
  static const error_get_information_employee = "Lấy thông tin chi tiết nhân viên không thành công!";
  static const haveCompletedUpdatingInformation = "Đồng chí đã hoàn thiện cập nhật thông tin Hộ gia đình";
  static const birthCertificateImageInvalid = "CCCD/CMND không hợp lệ!";
  static const iDCardImageNotValid = "Giấy khai sinh không hợp lệ!";
  static const iDCardNotValid = "Số căn cước không được nhỏ hơn 12";
  static const skip = "Bỏ qua";
  static const request_reissue_health_insurance_card = "Đề nghị cấp lại thẻ BHYT";
  static const ho_chieu_cmnd_cccd = "Số CMND/Hộ chiếu/Thẻ căn cước";


  static const socialInsuranceHealthInformation = "Thông tin BHXH - BHYT";
  static const healthInsuranceCardInformation = "Thông tin thẻ BHYT";
  static const socialInsuranceBookInformation = "Thông tin sổ BHXH";
  static const commitmentHandOverSocialInsuranceook = "Cam kết bàn giao sổ BHXH";
  static const reasonRequestingIssuanceHealthInsuranceCard = "Lý do đề nghị cấp lại thẻ BHYT";
  static const dueChangeMedicalExaminationLocation = "Do thay đổi nơi khám chữa bệnh";
  static const dueChangesInformationCard = "Do thay đổi thông tin trên thẻ (Họ tên/ Ngày sinh/ Giới tính)";
  static const noteSocialInsurance = "Lưu ý: ";
  static const underCurrentRegulations = "Theo quy định hiện hành - "
      "Đề nghị cấp lại thẻ BHYT do thay đổi nơi KCB sẽ được cơ quan BHXH "
      "tiếp nhận vào tháng đầu tiên của quý tiếp theo !";
  static const confirmationHealthInsuranceCard = "Xác nhận thẻ BHYT";
  static const confirmationReissue = "Xác nhận thẻ BHYT cấp lại";
  static const register_insurance_success = "Đề nghị cấp lại thẻ BHYT thành công !";
  static const register_insurance_fail = "Đề nghị cấp lại thẻ BHYT thất bại !";
  static const searchProvinceCity = "Tìm kiếm tỉnh hoặc thành phố";
  static const chooseProvinceCity = "Chọn tỉnh hoặc thành phố";
  static const cancel_insurance = "Hủy";
  static const success_insurance = "Thành công";
  static const pleaseRegisterRequestReissueHealthInsuranceCard = "Đồng chí vui lòng đăng ký yêu cầu cấp lại thẻ BHYT ?";
  static const idCardInsurance = "Số CMND/Hộ chiếu/Thẻ căn cước";
  static const pleaseFillDateAndName = "Vui lòng nhập Họ tên đề nghị thay đổi hoặc Ngày sinh đề nghị thay đổi hoặc Giới tính đề nghị thay đổi !";
  static const provinceMedicalExaminationTreatment = "Tỉnh/TP KCB";
  static const chooseProvinceMedicalExaminationTreatment = "Chọn tỉnh/thành phố KCB";
  static const provinceMedicalExamination = "tỉnh hoặc thành phố";
  static const pleaseProvinceMedicalExaminationTreatment = "Hãy lựa chọn tỉnh hoặc thành phố";
  static const nameHealthCareUnit = "Tên đơn vị KCB";
  static const chooseNameHealthCareUnit = "Chọn tên đơn vị KCB";
  static const pleaseChooseNameHealthCareUnit = "Hãy lựa chọn tên đơn vị KCB";
  static const textNameHealthCareUnit = "tên đơn vị KCB";
  static const fullNameChanged = "Họ tên đề nghị thay đổi";
  static const dateChanged = "Ngày sinh đề nghị thay đổi";
  static const searchMedicalFacilityName = "Tìm kiếm tên đơn vị KCB";
  static const pleaseSelectMedicalExaminationProvinceFirst = "Vui lòng chọn Tỉnh/TP KCB trước";
  static const youHaveNotReceivedCodeYet = "Bạn chưa nhận được mã ?";
  static const resend = "Gửi lại";
  static const chooseDateChanged = "Chọn ngày sinh thay đổi";
  static const inputFullNameChanged = "Nhập họ tên thay đổi";

  static const idCardError = "CMND/CCCD không phải là 9 số hoặc 12 số";
  static const taxNumberError = "Mã số thuế không thuộc 10 - 13 ký tự";
  static const valTaxNumberError = "Mã số thuế đã được đăng ký. Vui lòng kiểm tra lại!";
  static const phoneNumberError = "SĐT không được nhỏ hơn 10 ký tự";
  static const download_success = "Tải xuống thành công";
  static const youDefinitelyDeleteImage = "Bạn chắc chắn muốn xóa hình ảnh này ?";
  static const youDefinitelyDeletePersonalIncomeTaxDocuments = "Bạn chắc chắn muốn xóa chứng từ thuế TNCN ?";
  static const youDefinitelyDeleteInvoiceSale = "Bạn chắc chắn muốn xóa hoá đơn bán hàng?";
  static const textConfirmEditIncomeTax =
      "Thông tin đăng ký hình thức quyết toán thuế trước đó sẽ bị hủy. Bạn chắc chắc thay đổi hình thức quyết toán thuế?";
  static const register_success_authority = "Đăng ký thành công. Hồ sơ của bạn đã được gửi cho đơn vị chờ phê duyệt";
  static const cancel_incomeTax = "Hủy";
  static const numberPhotosHasExceededLimit = "Số lượng ảnh đã quá giới hạn, hãy xóa bớt ảnh đi để thêm mới";

  static const systemCannotRecognizePhotoTook = "Hệ thống không nhận diện được ảnh bạn chụp";

  static const request_confirm_business_income = "Xin xác nhận công tác/thu nhập";
  static const work_unit = "Đơn vị công tác";
  static const certification_type = "Loại giấy xin xác nhận";
  static const business_type = "Xác nhận công tác";
  static const income_type = "Xác nhận thu nhập";
  static const permanent_residence = "Hộ khẩu thường trú";
  static const issue_place = "Nơi cấp";
  static const start_working_date = "Ngày bắt đầu làm việc";

  static const reason_business_1 = "Xin xác nhận công tác để làm thủ tục tạm hoãn nghĩa vụ quân sự tại địa phương";
  static const reason_business_2 = "Xin xác nhận công tác trong thời gian công tác tại Tổng Công ty CP Công trình Viettel để làm thủ tục pháp lý với doanh nghiệp nước ngoài";
  static const reason_business_3 = "Xin xác nhận công tác để làm thủ tục xin visa đi nước ngoài";
  static const reason_business_4 = "Xin xác nhận công tác để làm thủ tục đi du lịch nước ngoài";
  static const reason_business_5 = "Xác nhận công tác để đăng ký dự tuyển chương trình đào tạo thạc sĩ";

  static const reason_income_1 = "Xin xác nhận thu nhập để làm thủ tục vay vốn ngân hàng";
  static const reason_income_2 = "Xin xác nhận thu nhập trong thời gian công tác tại Tổng Công ty CP Công trình Viettel để làm thủ tục pháp lý với doanh nghiệp nước ngoài";
  static const reason_income_3 = "Xin xác nhận thu nhập để làm thủ tục xin visa đi nước ngoài";
  static const reason_income_4 = "Xin xác nhận thu nhập để làm thủ tục đi du lịch nước ngoài";

  static const method_receive_data_1 = "Nhận tại đơn vị nơi yêu cầu cung cấp";
  static const method_receive_data_2 = "Nhận bản ký điện tử";

  static const about_1_month = "1 tháng gần đây";
  static const about_2_month = "2 tháng gần đây";
  static const about_3_month = "3 tháng gần đây";
  static const about_4_month = "4 tháng gần đây";
  static const about_5_month = "5 tháng gần đây";
  static const about_6_month = "6 tháng gần đây";
  static const about_7_month = "7 tháng gần đây";
  static const about_8_month = "8 tháng gần đây";
  static const about_9_month = "9 tháng gần đây";
  static const about_10_month = "10 tháng gần đây";
  static const about_11_month = "11 tháng gần đây";
  static const about_12_month = "12 tháng gần đây";

  static const other_reason = "Lý do khác";
  static const reason_certification = "Lý do xin giấy xác nhận";
  static const input_reason = "Nhập lý do";
  static const method_receive = "Phương thức nhận";
  static const error_input_reason = "Bạn chưa nhập lý do";
  static const error_input_method_receive = "Bạn chưa nhập phương thức nhận";
  static const income_confirmation_time = "Thời gian xác nhận thu nhập";
  static const number_copies = "Số lượng bản photo dấu đỏ";
  static const receive_data_type = "Loại phương thức nhận dữ liệu";
  static const method_receive_data = "Phương thức nhận dữ liệu";
  static const income_confirm_time = "Thời gian xác nhận thu nhập";
  static const title_view_detail_certification = "Giấy xin xác nhận công tác/thu nhập";
  static const error_check_type_create_certification = "Kiểm tra trạng thái giấy xác nhận không thành công";
  static const confirmed_all_certification = "Bạn đã có yêu cầu xin giấy xác nhận công tác và yêu cầu xin giấy xác nhận thu nhập đang chờ phê duyệt";
  static const found_certification = "Không tìm thấy giấy xác nhận";
  static const error_same_id_card = "Trùng CCCD/CMND với nhân viên khác";
  static const error_call_api_check_id_card = "Lỗi kiểm tra trùng CCCD/CMND. Vui lòng thử lại";

  static const certification_status_1 = "Chưa trình P.TCLĐ phê duyệt";
  static const certification_status_2 = "Chờ TCLĐ đơn vị phê duyệt";
  static const certification_status_3 = "TCLĐ đơn vị từ chối";
  static const certification_status_4 = "Chờ P.TCLĐ phê duyệt";
  static const certification_status_5 = "P.TCLĐ từ chối";
  static const certification_status_6 = "Chưa trình ký VO";
  static const certification_status_7 = "Đang trình ký VO";
  static const certification_status_8 = "Từ chối ký VO";
  static const certification_status_9 = "Đã ban hành";
  static const certification_status_10 = "CBNV hủy đề xuất";
  static const certification_status_11 = "P.TCLĐ hủy đề xuất";
  static const certification_status_12 = "Đã đóng dấu";

  static const employee_code = "Mã nhân viên";
  static const full_name_household = "Họ và tên chủ hộ";
  static const householderCCCD = "Số CCCD chủ hộ";
  static const province_city = "Tỉnh/thành phố";
  static const district = "Quận/huyện";
  static const wards = "Xã/phường";
  static const street_village = "Số nhà, đường phố, thôn, xóm";
  static const householderBirthday = "Ngày sinh";
  static const full_name_member = "Họ và tên";
  static const birthday_member = "Ngày sinh";
  static const id_number_member = "Số CCCD";
  static const province_member = "Tỉnh/thành phố";
  static const district_member = "Quận/huyện";
  static const wards_member = "Xã/phường";
  static const note_member = "Ghi chú";
  static const relationship_member = "Mối quan hệ";
  static const sign_contract_success = "Ký hợp đồng thành công";
  static const registrationWasUnsuccessfulInsurance  = "Đồng chí đã có đề nghị cấp lại thẻ đang được xét duyệt !";
  static const noInformationReissue = "Không có thông tin thẻ BHYT cấp lại !";
  static const confirmationReissuedHealthInsurance = "Xác nhận bảo hiểm y tế cấp lại";
  static const upload_id_card_image = "Tải ảnh CCCD";
  static const update_photo_birthday = "Tải ảnh giấy khai sinh";

  static const staffInformation = "Thông tin nhân viên";

  static const ethnic = "Dân tộc";
  static const nationality = "Quốc tịch";
  static const noteInformation = "Ghi chú";
  static const houseStreetInformation = "Số nhà-đường phố";
  static const birthCertificatePhoto = "Hình ảnh giấy khai sinh";
  static const idCardInformation = "Thẻ CCCD/ ms định danh";
  static const idCardImgInformation = "Hình ảnh hai mặt căn cước công dân";
  static const provincePermanentInformation = "Tỉnh/thành phố hộ khẩu thường trú";
  static const districtPermanentInformation = "Quận/huyện hộ khẩu thường trú";
  static const wardPermanentInformation = "Xã/phường hộ khẩu thường trú";
  static const provinceAdressInformation = "Tỉnh/thành phố nhận hồ sơ";
  static const districtAdressInformation = "Quận/huyện nhận hồ sơ";
  static const wardAdressInformation = "Xã/phường nhận hồ sơ";
  static const provinceAdressKcb = "Tỉnh/ thành phố KCB";
  static const permanentResidenceRegistrationAddress = "Địa chỉ đăng ký hộ khẩu thường trú";
  static const emailInformation = "Email";
  static const positionInformation = "Chức vụ";
  static const workplaceInformation = "Nơi làm việc";
  static const attachMaximumPhotos = "Quá số lượng ảnh cho phép, đính kèm tối đa 2 ảnh";
  static const addressReceiveDocuments = "Địa chỉ nhận hồ sơ";
  static const searchForEthnicity = "Tìm kiếm dân tộc";
  static const ethnicSelection = "Lựa chọn dân tộc";
  static const searchDistrict = "Tìm kiếm quận/huyện";
  static const chooseDistrict = "Chọn quận/huyện";
  static const searchWards = "Tìm kiếm xã/phường";
  static const chooseWards = "Chọn xã/phường";
  static const searchNationality = "Tìm kiếm quốc tịch";
  static const chooseNationality = "Lựa chọn quốc tịch";
  static const chooseSelection= "Lựa chọn giới tính";
  static const searchSelection= "Tìm kiếm giới tính";

  static const imgFrontPathReissue = "Ảnh mặt trước CCCD";
  static const imgBackPathReissue = "Ảnh mặt sau CCCD";
  static const imgBirthPathReissue = "Ảnh giấy khai sinh";
  static const confirmFa = "Ảnh giấy khai sinh";
  static const updateInformationFail = "Cập nhật thông tin cá nhân thất bại";
  static const downloadCCCDPhotoBirthCertificate = "Tải ảnh CCCD/ Giấy khai sinh";
  static const genderConfirmChange = "Giới tính đề nghị thay đổi";
  static const addFrontPhotoReissue = "Ảnh CCCD\nmặt trước";
  static const addBackPhotoReissue = "Ảnh CCCD\nmặt sau";
  static const addBirthPhotoReissue = "Ảnh\ngiấy khai sinh";
  static const please_add_id_card_image = "Vui lòng thêm ảnh CCCD!";
  static const please_add_birth_image = "Vui lòng thêm ảnh giấy khai sinh!";
  static const pleaseFillAllImageBirthInformation = "Vui lòng thêm đủ ảnh CCCD mặt trước và mặt sau !";



  static const askForConfirmation = "Xin giấy xác nhận";
  static const collectionWork = "Công tác/thu nhập";
  static const proposedDate = "Ngày đề xuất";
  static const confirmationWork = "Xác nhận công tác";
  static const incomeConfirmation = "Xác nhận thu nhập";

  static const noCertificationListPage = 'Không có danh sách xác nhận công tác/thu nhập';
  static const textConfirmDeleteCertificationIncome =
      "Bạn chắc chắn muốn xóa yêu cầu xin xác nhận thu nhập";
  static const textConfirmDeleteCertificationWork =
      "Bạn chắc chắn muốn xóa yêu cầu xin xác nhận công tác";
  static const cancelCertificationSuccess = "Hủy giấy xác nhận thành công!";
  static const cancelCertificationFail = "Hủy giấy xác nhận thất bại!";
  static const chooseText = 'Xem văn bản';
  static const applicationIncomeConfirmation = 'Giấy xin xác nhận thu nhập';
  static const incomeCertificate = 'Giấy xác nhận thu nhập';
  static const applicationConfirmationWork = 'Giấy xin xác nhận công tác';
  static const workConfirmationCertificate = 'Giấy xác nhận công tác';
  static const haveRequestWorkConfirmationCertificateAwaitingApproval = 'Bạn đã có yêu cầu xin giấy xác nhận công tác đang chờ phê duyệt';
  static const haveRequestIncomeConfirmationCertificateAwaitingApproval = 'Bạn đã có yêu cầu xin giấy xác nhận thu nhập đang chờ phê duyệt';

  static const not_has_standard = "Chức danh này không còn tồn tại trong hệ thống";
  static const confirm_read_all_notification = "Bạn chắc chắn muốn đánh dấu tất cả thông báo là đã đọc";
  static const reward_employee = "Khen thưởng cấp TCT";

  static const sign_the_electronic_commitment = "Ký cam kết điện tử";
  static const certificate_group_needs_to_be_added = "Nhóm chứng chỉ cần bổ sung";
  static const certificate_class_needs_to_be_added = "Hạng chứng chỉ cần bổ sung";
  static const current_residence = "Nơi ở hiện tại";
  static const date_of_commitment = "Ngày cam kết bổ sung chứng chỉ";
  static const date_of_registration_at_the_training_unit = "Ngày tham gia đăng ký học tại đơn vị đào tạo";
  static const date_of_register_training = "Ngày tham gia đăng ký học";
  static const from_month_yeah = "Tháng/ năm từ";
  static const to_month_yeah = "Tháng/ năm đến";
  static const application_for_business_confirmation = 'Giấy đề nghị xác nhận công tác';
  static const application_for_income_confirmation = 'Giấy đề nghị xác nhận thu nhập';
  static const reason_for_cancellation = 'Lý do hủy bỏ';
  static const download = 'Tải xuống';
  static const income_period_from = 'Thời gian thu nhập từ';
  static const income_period_to = 'Thời gian thu nhập đến';

  static const training_time = "Thời gian đào tạo";
  static const sign_time = "Thời gian ký";
  static const period_date = "Kỳ chi trả";
  static const payment_month = "Tháng thanh toán";
  static const order_invoice = "File hoá đơn bán hàng";
  static const order_invoice_desc = "OFT có thu nhập > 100 triệu. Yêu cầu cung cấp hóa đơn bán hàng được kê khai và chi cục Thuế cấp tháng 3/2034";
  static const volume_work_detail = "Chi tiết khối lượng công việc";
  static const oft_volume_work_detail = "Yêu cầu OFT đọc kỹ và xác nhận khối lượng công việc";
  static const oft_volume_work_payment = "TCT Công trình Viettel chỉ chi trả khi OFT ký xác nhận khối lượng công việc . Mọi thắc mắc vui lòng liên hệ với đầu mối trực tiếp của tỉnh";
  static const signature_confirmation = "Ký xác nhận";
  static const kpi_employee = "KI cá nhân";
  static const warring_working_holiday = "Chức danh của đ/c không nằm trong quy định được đăng ký trực ngày lễ/Tết. TCT sẽ từ chối thanh toán lương trực lễ nếu không có văn bản phê duyệt của chỉ huy đơn vị/trưởng ngành dọc.";
  static const number_calendar = "Số cuốn lịch";
  static const address = "Địa chỉ";
  static const relationShip = "Mối quan hệ với người tặng";
  static const update = "Cập nhật";
}