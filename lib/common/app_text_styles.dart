import 'package:flutter/material.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/common/app_font_size.dart';

class AppTextStyle {
  static const black = TextStyle(color: Colors.black);

  static TextStyle styleElevateButton(context) {
    return TextStyle(
      fontSize: AppFontSize.s18,
      fontWeight: FontWeight.w700,
      color: Colors.white,
    );
  }

  static TextStyle styleTextButton(context) {
    return TextStyle(
      fontSize: AppFontSize.s18,
      fontWeight: FontWeight.w500,
      color: Colors.black45,
    );
  }

  static TextStyle styleHintText(context) {
    return TextStyle(
      fontSize: AppFontSize.s14,
      fontWeight: FontWeight.w400,
      color: Colors.black45,
    );
  }

  static TextStyle styleLabelText(context) {
    return TextStyle(
      fontSize: AppFontSize.s15,
      fontWeight: FontWeight.w400,
      color: Colors.black,
    );
  }

  static TextStyle styleSuffixLabelText(context) {
    return TextStyle(
      fontSize: AppFontSize.s15,
      fontWeight: FontWeight.w400,
      color: Colors.red,
    );
  }

  static TextStyle styleBoldLabel(context) {
    return TextStyle(
      fontSize: AppFontSize.s17,
      fontWeight: FontWeight.w700,
      color: Colors.black45,
    );
  }

  static final TextStyle whiteS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: Colors.white,
  );

  static final TextStyle whiteS14 = TextStyle(
    fontSize: AppFontSize.s14,
    color: Colors.white,
  );

  static final TextStyle whiteS15W500 = TextStyle(
    fontSize: AppFontSize.s15,
    fontWeight: FontWeight.w500,
    color: Colors.white,
  );

  static final TextStyle whiteS16W600 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w600,
    color: Colors.white,
  );

  static final TextStyle textButtonSignElectricS16W600 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w600,
    color: AppColors.textButtonSignElectric,
  );

  static final TextStyle whiteS16W500 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w500,
    color: Colors.white,
  );

  static final TextStyle whiteS18W600 = TextStyle(
    fontSize: AppFontSize.s18,
    fontWeight: FontWeight.w600,
    color: Colors.white,
  );

  static final TextStyle whiteS18W600Quicksand = TextStyle(
    fontSize: AppFontSize.s18,
    fontWeight: FontWeight.w600,
    fontFamily: "Quicksand",
    color: Colors.white,
  );

  static final TextStyle blackS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: Colors.black,
  );

  static final TextStyle greenS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: AppColors.green,
  );

  static final TextStyle blackS16W500 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w500,
    color: Colors.black,
  );

  static final TextStyle blackS16W600 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w600,
    color: Colors.black,
  );

  static final TextStyle blackS16W400 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w400,
    color: Colors.black,
  );

  static final TextStyle blackS14Normal = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
    color: Colors.black,
  );

  static final TextStyle blackS12Normal = TextStyle(
    fontSize: AppFontSize.s12,
    fontWeight: FontWeight.w400,
    color: Colors.black,
  );

  static final TextStyle blackS14NormalLineThrough = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
    color: Colors.black,
    decoration: TextDecoration.lineThrough
  );

  static final TextStyle outerSpaceS14Normal = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
    color: AppColors.outerSpaceColor,
  );

  static final TextStyle outerSpaceS13W400 = TextStyle(
    fontSize: AppFontSize.s13,
    fontWeight: FontWeight.w400,
    color: AppColors.outerSpaceColor,
  );

  static final TextStyle greyLoginOtpS14W400 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
    color: AppColors.greyLoginOtp,
  );

  static final TextStyle greyLoginOtpS14W400H13 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
    color: AppColors.greyLoginOtp,
    height: 1.3
  );

  static final TextStyle primaryColorS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: AppColors.primaryColor,
  );

  static final TextStyle primaryColorS16W500 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w500,
    color: AppColors.primaryColor,
  );

  static final TextStyle primaryColorS13 = TextStyle(
    fontSize: AppFontSize.s14,
    color: AppColors.primaryColor,
  );

  static final TextStyle primaryColorS13W500 = TextStyle(
    fontSize: AppFontSize.s13,
    fontWeight: FontWeight.w500,
    color: AppColors.primaryColor,
  );

  static final TextStyle primaryColorS15W500 = TextStyle(
    fontSize: AppFontSize.s15,
    fontWeight: FontWeight.w500,
    color: AppColors.primaryColor,
  );

  static final TextStyle primaryColorS16W600 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w600,
    color: AppColors.primaryColor,
  );

  static final TextStyle greyTextRoadmapColorS14W500 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w500,
    color: AppColors.greyTextRoadmap,
  );

  static final TextStyle greyTextRoadmapColorS14Normal = TextStyle(
    fontSize: AppFontSize.s14,
    color: AppColors.greyTextRoadmap,
  );

  static final TextStyle greyLoginOtpS13W500 = TextStyle(
    fontSize: AppFontSize.s13,
    color: AppColors.greyLoginOtp,
    fontWeight: FontWeight.w500,
  );

  static final TextStyle greyTextRoadmapS14W400 = TextStyle(
    fontSize: AppFontSize.s14,
    color: AppColors.greyTextRoadmap,
    fontWeight: FontWeight.w400,
  );

  static final TextStyle greyTextRoadmapColorS16W500 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w500,
    color: AppColors.greyTextRoadmap,
  );

  static final TextStyle greyTextIncomeColorS16W500 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w500,
    color: AppColors.blackTextIncome,
  );

  static final TextStyle hintTextFieldS14W500 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w500,
    color: AppColors.hintTextField,
  );



  static final TextStyle blackColorS16Normal = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w400,
    color: AppColors.blackColor,
  );

  static final TextStyle blackColorS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: AppColors.blackColor,
  );

  static final TextStyle blackColorS14W500 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w500,
    color: AppColors.blackColor,
  );

  static final TextStyle whiteColorS14W500 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w500,
    color: Colors.white,
  );

  static final TextStyle blueColorS16W500 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w500,
    color: AppColors.blueColor,
  );

  static TextStyle styleTitleDialogNotify =
  TextStyle(fontWeight: FontWeight.bold, fontSize: 30,          );

  static TextStyle styleContentDialogNotify = TextStyle(
    fontWeight: FontWeight.w500, color: Colors.black54, fontSize: 16,          );

  static TextStyle styleTitleLabor = TextStyle(
    color: AppColors.blueHeader, fontSize: 16, fontWeight: FontWeight.w500,          );

  static final TextStyle styleSuggestTextField = TextStyle(
    fontSize: AppFontSize.s11,
    fontWeight: FontWeight.w400,
    color: AppColors.primaryColor,
    fontStyle: FontStyle.italic,
  );

  static final TextStyle styleSuggestTextFieldShirt = TextStyle(
    fontSize: AppFontSize.s11,
    fontWeight: FontWeight.w400,
    color: AppColors.blackColor,
    fontStyle: FontStyle.italic,
  );

  static final TextStyle styleSuggestTextFieldPant = TextStyle(
    fontSize: AppFontSize.s11,
    fontWeight: FontWeight.w400,
    color: AppColors.blackColor,
  );

  static final TextStyle greyS16W600 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w600,
    color: AppColors.outerSpaceColor,
  );

  static final TextStyle blueHeaderColorS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: AppColors.blueHeader,
  );

  static final TextStyle blueHeaderColorS14W400 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
    color: AppColors.blueHeader,
  );


  static final black45S14W500 = TextStyle(
    color: Colors.black45,
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w500,
  );

  static final outerSpaceColor45S14W500 = TextStyle(
    color: AppColors.outerSpaceColor,
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w500,
  );

  static final greyTextTitles14W400 = TextStyle(
    color: AppColors.greyTextTitle,
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
  );

  static final black54S18Bold = TextStyle(
    color: Colors.black54,
    fontWeight: FontWeight.bold,
    fontSize: AppFontSize.s18,
  );

  static final black54S20Bold = TextStyle(
    color: Colors.black54,
    fontWeight: FontWeight.bold,
    fontSize: AppFontSize.s20,
  );

  static final blackColorS20W700 = TextStyle(
    color: AppColors.blackColor,
    fontWeight: FontWeight.w700,
    fontSize: AppFontSize.s20,
  );

  static final buttonCancelS16W600 = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: AppFontSize.s16,
    color: AppColors.buttonCancelColor,
  );

  static final TextStyle greyTitleS14NormalRoadmap = TextStyle(
    fontSize: AppFontSize.s14,
    color: AppColors.greyTextRoadmap,
  );

  static final TextStyle blackTextS14NormalRoadmap = TextStyle(
    fontSize: AppFontSize.s14,
    color: AppColors.blackTextRoadmap,
  );

  static final TextStyle blackTextS16NormalRoadmap = TextStyle(
    fontSize: AppFontSize.s16,
    color: AppColors.blackTextRoadmap,
  );

  static final blackTextS16W500Roadmap = TextStyle(
    fontWeight: FontWeight.w500,
    fontSize: AppFontSize.s16,
    color: AppColors.blackTextRoadmap,
  );

  static final blackTextS16W600Roadmap = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: AppFontSize.s16,
    color: AppColors.blackTextRoadmap,
  );

  static final TextStyle styleSuggestSearchResignation = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: Colors.grey,
    fontStyle: FontStyle.italic,
  );

  static final blackS18Bold = TextStyle(
    color: Colors.black,
    fontWeight: FontWeight.bold,
    fontSize: AppFontSize.s18,
  );

  static final blackS18W700 = TextStyle(
    color: Colors.black,
    fontWeight: FontWeight.w700,
    fontSize: AppFontSize.s18,
  );
  static final blackS18W600 = TextStyle(
    color: Colors.black,
    fontWeight: FontWeight.w600,
    fontSize: AppFontSize.s18,
  );


  static final borderColorS14W400Italic = TextStyle(
    color: AppColors.borderColor,
    fontWeight: FontWeight.w400,
    fontStyle: FontStyle.italic,
    fontSize: AppFontSize.s14,
  );

  static final blackS16Bold = TextStyle(
    color: Colors.black,
    fontWeight: FontWeight.bold,
    fontSize: AppFontSize.s16,
  );

  static final primaryS14W500 = TextStyle(
    color: AppColors.primaryColor,
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w500,
  );

  static final primaryS14W400 = TextStyle(
    color: AppColors.primaryColor,
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
  );

  static final primaryS14Normal = TextStyle(
    color: AppColors.primaryColor,
    fontSize: AppFontSize.s14,
  );

  static final blackS14W500 = TextStyle(
    color: Colors.black,
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w500,
  );

  static final blackS14W400 = TextStyle(
    color: Colors.black,
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
  );

  static final blackS15W500 = TextStyle(
    color: Colors.black,
    fontSize: AppFontSize.s15,
    fontWeight: FontWeight.w500,
  );

  static final blackS15W400 = TextStyle(
    color: Colors.black,
    fontSize: AppFontSize.s15,
    fontWeight: FontWeight.w400,
  );

  static final greyTextFieldLableS16W400 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w400,
    color: AppColors.greyTextFieldLable,
  );

  static final greyTextRoadmapS16W400 = TextStyle(
    fontSize: AppFontSize.s16,
    fontWeight: FontWeight.w400,
    color: AppColors.greyTextRoadmap,
  );

  static final TextStyle whiteS14W500 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w500,
    color: Colors.white,
  );

  static final neutral2_5S14W400 = TextStyle(
    color: AppColors.neutral2_5,
    fontWeight: FontWeight.w400,
    fontSize: AppFontSize.s14,
  );

  static final primaryS13W500 = TextStyle(
    color: AppColors.primaryColor,
    fontWeight: FontWeight.w500,
    fontSize: AppFontSize.s13,
  );

  static final TextStyle greyTitleS14NormalIncome = TextStyle(
    fontSize: AppFontSize.s14,
    color: AppColors.greyTextIncome,
  );

  static final TextStyle blackTextS14NormalIncome = TextStyle(
    fontSize: AppFontSize.s14,
    color: AppColors.blackTextIncome,
  );

  static final TextStyle blackTextS14W600Income = TextStyle(
    fontSize: AppFontSize.s14,
    color: AppColors.blackTextIncome,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle blackTextS16W600Income = TextStyle(
    fontSize: AppFontSize.s16,
    color: AppColors.blackTextIncome,
    fontWeight: FontWeight.w600,
  );

  static final TextStyle blackTextS16NormalIncome = TextStyle(
    fontSize: AppFontSize.s16,
    color: AppColors.blackTextIncome,
  );

  static final TextStyle greySpaceS14Normal = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
    color: AppColors.backBoxShadow.withAlpha(50),
  );


  static final TextStyle greenCertificationS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: AppColors.greenCertification,
  );

  static final TextStyle orangeCertificationS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: AppColors.orangeCertification,
  );

  static final TextStyle redCertificationS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: AppColors.redCertification,
  );

  static final TextStyle greyCertificationS14W600 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w600,
    color: AppColors.greyCertification,
  );

  static final TextStyle primaryColorS14W400 = TextStyle(
    fontSize: AppFontSize.s14,
    fontWeight: FontWeight.w400,
    color: AppColors.primaryColor,
  );

  static TextStyle textStyleCommitCertificate(int status) {
    switch (status) {
      case 1:
      case 4:
      case 5:
      case 7:
        return orangeCertificationS14W600;
      case 2:
        return greenS14W600;
      case 3:
      case 6:
      case 8:
        return primaryColorS14W600;
      default:
        return blackS14W600;
    }
  }

}
