import 'package:flutter/material.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/widget/dropdown/alert_modal.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:nb_utils/nb_utils.dart';

class PrivacyAppSetting {
  PrivacyAppSetting._();

  static openModalConfirm(
    BuildContext context, {
      String pricacy,
    Function onAccept,
    Function onCancel,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertModal(
          child: Column(
            children: [
              10.height,
              Text(
                "Thông báo",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
              10.height,
              Text(
                'Bạn chắc chắn muốn dừng truy cập $pricacy?',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.greyBlackColor,
                ),
              )
            ],
          ),
          onAccept: () {
            if (onAccept != null) {
              onAccept();
            }
          },
          onCancel: () {
            if (onCancel != null) {
              onCancel();
            }
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  static openModalAlert(
    BuildContext context, {
    Function onAccept,
    Function onCancel,
        String title,
        String content,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertModal(
          child: Column(
            children: [
              10.height,
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
              10.height,
              Text(
                content,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.greyBlackColor,
                ),
              ),
            ],
          ),
          acceptText: 'Đồng ý',
          onAccept: () {
            if (onAccept != null) {
              onAccept();
            }
          },
          onCancel: () {
            if(onCancel != null){
              onCancel();
            }
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  static showModalDenyLocationPermission(
      BuildContext context, {
        Function onAccept,
        Function onCancel,
        String title,
      }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertModal(
          child: Column(
            children: [
              10.height,
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Colors.black,
                ),
              )
            ],
          ),
          acceptText: 'Cài đặt',
          onAccept: () {
            if (onAccept != null) {
              onAccept();
            }
            openAppSettings();
          },
          onCancel: () {
            if (onCancel != null) {
              onCancel();
            }
            Navigator.of(context).pop();
          },
        );
      },
    );
  }
}
