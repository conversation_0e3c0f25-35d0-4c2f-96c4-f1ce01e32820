import 'package:hstd/common/app_strings.dart';
import 'package:hstd/common/app_vectors.dart';

enum TYPE_DATA {
  APP_PARAM,
  DATE_TIME,
  STRING,
  INTEGER,
  LOGIC,
  DATE,
  REAL,
  SPECIAL
}

enum REQUEST_AREA_TYPE {
  CITY,
  DISTRICT,
  STREET
}

extension TYPE_DATA_EXTENSION on TYPE_DATA {
  int get values {
    switch (this) {
      case TYPE_DATA.APP_PARAM:
        return 1;
      case TYPE_DATA.DATE_TIME:
        return 2;
      case TYPE_DATA.STRING:
        return 3;
      case TYPE_DATA.INTEGER:
        return 4;
      case TYPE_DATA.LOGIC:
        return 5;
      case TYPE_DATA.DATE:
        return 6;
      case TYPE_DATA.REAL:
        return 7;
      case TYPE_DATA.SPECIAL:
        return 8;
    }
  }
}

enum STATUS_CONTRACT {
  NOT_IN_RESIGN_SESSION,
  IN_EVALUATION,
  HR_UPDATED_INTERVIEW_RESULT,
  CREATED_BM_FILE,
  SENT_BM_09_VOFFICE,
  RECEIVED_BM_09_VOFFICE_AND_FAIL,
  RECEIVED_BM_09_VOFFICE_AND_SUCCESS,
  HR_TCT_ADD_EMPLOYEE_TO_VOFFICE2,
  HR_CREATED_BMTCT_FILE,
  HR_TCT_SENT_TO_VOFFICE2,
  RECEIVED_VOFFICE2_AND_FAIL,
  RECEIVED_VOFFICE2_AND_SUCCESS,
  TEMP_CONTRACT_CREATE,
  HR_UPDATED_RESIGN_CONTRACT,
  HR_CREATED_FILE_4_RESIGN_CONTRACT,
  EMPLOYEE_SIGN_RESIGN_CONTRACT,
  HR_SENT_TO_VOFFICE3,
  RECEIVED_VOFFICE3_AND_FAIL,
  RECEIVED_VOFFICE3_AND_SUCCESS,
  UPDATED_TEMP_CONTRACT_2_OFFICAL,
  SENT_REQUEST_OF_NEW_RESIGN_CONTRACT_TO_VHR
}

extension STATUS_CONTRACT_EXTENSION on STATUS_CONTRACT {
  int get values {
    switch (this) {
      case STATUS_CONTRACT.NOT_IN_RESIGN_SESSION:
        return -1;
      case STATUS_CONTRACT.IN_EVALUATION:
        return 0;
      case STATUS_CONTRACT.HR_UPDATED_INTERVIEW_RESULT:
        return 1;
      case STATUS_CONTRACT.CREATED_BM_FILE:
        return 2;
      case STATUS_CONTRACT.SENT_BM_09_VOFFICE:
        return 3;
      case STATUS_CONTRACT.RECEIVED_BM_09_VOFFICE_AND_FAIL:
        return 4;
      case STATUS_CONTRACT.RECEIVED_BM_09_VOFFICE_AND_SUCCESS:
        return 5;
      case STATUS_CONTRACT.HR_TCT_ADD_EMPLOYEE_TO_VOFFICE2:
        return 6;
      case STATUS_CONTRACT.HR_CREATED_BMTCT_FILE:
        return 7;
      case STATUS_CONTRACT.HR_TCT_SENT_TO_VOFFICE2:
        return 8;
      case STATUS_CONTRACT.RECEIVED_VOFFICE2_AND_FAIL:
        return 9;
      case STATUS_CONTRACT.RECEIVED_VOFFICE2_AND_SUCCESS:
        return 10;
      case STATUS_CONTRACT.TEMP_CONTRACT_CREATE:
        return 11;
      case STATUS_CONTRACT.HR_UPDATED_RESIGN_CONTRACT:
        return 12;
      case STATUS_CONTRACT.HR_CREATED_FILE_4_RESIGN_CONTRACT:
        return 13;
      case STATUS_CONTRACT.EMPLOYEE_SIGN_RESIGN_CONTRACT:
        return 14;
      case STATUS_CONTRACT.HR_SENT_TO_VOFFICE3:
        return 15;
      case STATUS_CONTRACT.RECEIVED_VOFFICE3_AND_FAIL:
        return 16;
      case STATUS_CONTRACT.RECEIVED_VOFFICE3_AND_SUCCESS:
        return 17;
      case STATUS_CONTRACT.UPDATED_TEMP_CONTRACT_2_OFFICAL:
        return 18;
      case STATUS_CONTRACT.SENT_REQUEST_OF_NEW_RESIGN_CONTRACT_TO_VHR:
        return 19;
      default:
        return -2;
    }
  }
}

enum DURATION_CONTRACT {
  ONE_MONTH,
  TWO_MONTH,
  THREE_MONTH,
  SIX_MONTH,
  ONE_YEAR,
  TWO_YEAR,
  INFINITY
}

extension DURATION_CONTRACT_EXTENSION on DURATION_CONTRACT {
  int get values {
    switch (this) {
      case DURATION_CONTRACT.INFINITY:
        return 0;
      case DURATION_CONTRACT.ONE_MONTH:
        return 1;
      case DURATION_CONTRACT.TWO_MONTH:
        return 2;
      case DURATION_CONTRACT.THREE_MONTH:
        return 3;
      case DURATION_CONTRACT.SIX_MONTH:
        return 6;
      case DURATION_CONTRACT.ONE_YEAR:
        return 12;
      case DURATION_CONTRACT.TWO_YEAR:
        return 24;
      default:
        return -1;
    }
  }
}

enum CONTRACT_TYPE { FREELANCER, LABOR, PROBATION, SERVICE, COMMIT }

extension CONTRACT_TYPE_EXTENSION on CONTRACT_TYPE {
  int get values {
    switch (this) {
      case CONTRACT_TYPE.FREELANCER:
        return 3;
      case CONTRACT_TYPE.LABOR:
        return 4;
      case CONTRACT_TYPE.PROBATION:
        return 5;
      case CONTRACT_TYPE.SERVICE:
        return 6;
      case CONTRACT_TYPE.COMMIT:
        return 7;
    }
  }
}

enum TYPE_OTP { PROBATION, LABOR, TERMINATE, ANNEX_CONTRACT, COMMIT, SOCIAL_INSURANCE,CONTRACT_OUTSIDE,CERTIFICATION, CERTIFICATE_COMMIT, TRAINING_SESSION_BOOK, SMART_OTP, TRAINING_ELECTRICAL_SAFETY_BOOK}

extension TYPE_OTP_EXTENSION on TYPE_OTP {
  int get values {
    switch (this) {
      case TYPE_OTP.CERTIFICATION:
        return 0;
      case TYPE_OTP.PROBATION:
        return 1;
      case TYPE_OTP.LABOR:
        return 2;
      case TYPE_OTP.TERMINATE:
        return 3;
      case TYPE_OTP.ANNEX_CONTRACT:
        return 4;
      case TYPE_OTP.COMMIT:
        return 5;
      case TYPE_OTP.SOCIAL_INSURANCE:
        return 6;
      case TYPE_OTP.CONTRACT_OUTSIDE:
        return 8;
      case TYPE_OTP.CERTIFICATE_COMMIT:
        return 5;
      case TYPE_OTP.TRAINING_SESSION_BOOK:
        return 9;
      case TYPE_OTP.SMART_OTP:
        return 10;
      case TYPE_OTP.TRAINING_ELECTRICAL_SAFETY_BOOK:
        return 11;
      default:
        return 2;
    }
  }
}

enum CONTRACT_NEW_STATUS {
  NOT_HAVE_VHR_CODE,
  PENDING_VHR_CODE,
  HAVE_VHR_CODE,
  UPDATE_CONTRACT,
  CREATE_FILE_CONTRACT,
  SEND_CONTRACT_TO_EMPLOYEE,
  EMPLOYEE_SIGNED,
  PENDING_SIGN_VOFFICE,
  SIGNED_VOFFICE
}

extension CONTRACT_NEW_STATUS_EXTENSION on CONTRACT_NEW_STATUS {
  int get values {
    switch (this) {
      case CONTRACT_NEW_STATUS.NOT_HAVE_VHR_CODE:
        return 0;
      case CONTRACT_NEW_STATUS.PENDING_VHR_CODE:
        return 1;
      case CONTRACT_NEW_STATUS.HAVE_VHR_CODE:
        return 2;
      case CONTRACT_NEW_STATUS.UPDATE_CONTRACT:
        return 3;
      case CONTRACT_NEW_STATUS.CREATE_FILE_CONTRACT:
        return 4;
      case CONTRACT_NEW_STATUS.SEND_CONTRACT_TO_EMPLOYEE:
        return 5;
      case CONTRACT_NEW_STATUS.EMPLOYEE_SIGNED:
        return 6;
      case CONTRACT_NEW_STATUS.PENDING_SIGN_VOFFICE:
        return 7;
      case CONTRACT_NEW_STATUS.SIGNED_VOFFICE:
        return 8;
      default:
        return 0;
    }
  }
}

enum TERMINATE_CONTRACT_STATUS { NOT_CONTRACT, HAS_CONTRACT, HAS_TERMINATE }

extension TERMINATE_CONTRACT_STATUS_EXTENSION on TERMINATE_CONTRACT_STATUS {
  int get values {
    switch (this) {
      case TERMINATE_CONTRACT_STATUS.NOT_CONTRACT:
        return 0;
      case TERMINATE_CONTRACT_STATUS.HAS_CONTRACT:
        return 1;
      case TERMINATE_CONTRACT_STATUS.HAS_TERMINATE:
        return 2;
      default:
        return 0;
    }
  }
}

enum TERMINATE_STATUS {
  DRAFT,
  PENDING,
  MANAGER_APPROVAL,
  MANAGER_REJECT,
  VOFFICE_PENDING,
  VOFFICE_APPROVAL,
  VOFFICE_REJECT,
  SEV_PENDING,
  SEV_REJECT,
  SEV_APPROVAL
}

extension TERMINATE_STATUS_EXTENSION on TERMINATE_STATUS {
  int get values {
    switch (this) {
      case TERMINATE_STATUS.DRAFT:
        return 1;
      case TERMINATE_STATUS.PENDING:
        return 2;

      case TERMINATE_STATUS.MANAGER_APPROVAL:
        return 3;

      case TERMINATE_STATUS.MANAGER_REJECT:
        return 4;

      case TERMINATE_STATUS.VOFFICE_PENDING:
        return 5;

      case TERMINATE_STATUS.VOFFICE_APPROVAL:
        return 6;

      case TERMINATE_STATUS.VOFFICE_REJECT:
        return 7;
      case TERMINATE_STATUS.SEV_PENDING:
        return 8;
      case TERMINATE_STATUS.SEV_REJECT:
        return 9;
      case TERMINATE_STATUS.SEV_APPROVAL:
        return 10;
    }
  }
}

enum FlushBarType { success, warning, error }
enum LoadStatus { initial, loading, success, failure, loadMore }

enum SelectDateType { fromDate, toDate, dateSelected }

enum SalaryType { month, giff, bonus, overTime, other, welfare }

extension SalaryTypeExtension on SalaryType {
  String get display {
    switch (this) {
      case SalaryType.month:
        return "Lương tháng";
      case SalaryType.giff:
        return "Quà lễ tết/nghỉ mát";
      case SalaryType.bonus:
        return "Quý/năm/bổ sung";
      case SalaryType.overTime:
        return "Làm thêm/chuyên cần";
      case SalaryType.other:
        return "Lương khác";
        case SalaryType.welfare:
        return "Phúc lợi & khác";
      default:
        return "";
    }
  }

  String get icon {
    switch (this) {
      case SalaryType.month:
        return AppVectors.icMonthSalary;
      case SalaryType.giff:
        return AppVectors.icGiffSalary;
      case SalaryType.bonus:
        return AppVectors.icBonusSalary;
      case SalaryType.overTime:
        return AppVectors.icOverTimeSalary;
      case SalaryType.other:
        return AppVectors.icOtherSalary;
      case SalaryType.welfare:
        return AppVectors.icMonthSalary;
      default:
        return AppVectors.icMonthSalary;
    }
  }

  String get title {
    switch (this) {
      case SalaryType.month:
        return "Lương tháng";
      case SalaryType.giff:
        return "Lương quà lễ tết/nghỉ mát";
      case SalaryType.bonus:
        return "Lương quý/năm/bổ sung";
      case SalaryType.overTime:
        return "Lương chuyên cần/lương làm thêm";
      case SalaryType.other:
        return "Lương khác";
      case SalaryType.welfare:
        return "Phúc lợi/hỏi thăm/khen thưởng";
      default:
        return "";
    }
  }

  int get typeInt {
    switch (this) {
      case SalaryType.month:
        return 1;
      case SalaryType.giff:
        return 3;
      case SalaryType.bonus:
        return 2;
      case SalaryType.overTime:
        return 4;
      case SalaryType.other:
        return 5;
      case SalaryType.welfare:
        return 6;
      default:
        return 1;
    }
  }
}

enum WarningType { All, KiD, Fall, Discipline }

extension WarningTypeExtension on WarningType {
  int get keyToServer {
    switch (this) {
      case WarningType.KiD:
        return 1;
      case WarningType.Fall:
        return 2;
      case WarningType.Discipline:
        return 3;
      case WarningType.All:
        return null;
      default:
        return null;
    }
  }

  String get display {
    switch (this) {
      case WarningType.KiD:
        return "KI D";
      case WarningType.Fall:
        return "Không hoàn thành nhiệm vụ";
      case WarningType.Discipline:
        return "Kỷ luật";
      case WarningType.All:
        return "Tất cả";
      default:
        return '';
    }
  }

  static WarningType getType(int value) {
    switch (value) {
      case 1:
        return WarningType.KiD;
      case 2:
        return WarningType.Fall;
      case 3:
        return WarningType.Discipline;
      default:
        return WarningType.All;
    }
  }
}

enum WarningStatus { all, confirmed, unconfirmed }

extension WarningStatusExtension on WarningStatus {
  int get keyToServer {
    switch (this) {
      case WarningStatus.unconfirmed:
        return 0;
      case WarningStatus.confirmed:
        return 1;
      case WarningStatus.all:
        return null;
      default:
        return null;
    }
  }

  String get display {
    switch (this) {
      case WarningStatus.confirmed:
        return "Đã xác nhận";
      case WarningStatus.unconfirmed:
        return "Chưa xác nhận";
      case WarningStatus.all:
        return "Tất cả";
      default:
        return '';
    }
  }

  static WarningStatus getType(int value) {
    switch (value) {
      case 0:
        return WarningStatus.unconfirmed;
      case 1:
        return WarningStatus.confirmed;
      default:
        return WarningStatus.all;
    }
  }
}

enum ResignationStatus {
  waitManagementConfirm,
  managerAgreedQuit,
  managerRefusedQuit,
  unitSubmitDesignation,
  unitRefusedQuit,
  waitTCTResolve,
  presentResignationDecision,
  CEORefusedQuit,
  hadDecisionQuit,
  hasRetired
}

enum EnumExtraTime {
  directCommandFullDay,
  directCommandMorning,
  directCommandAfternoon,
  directBusinessFullDay,
  directBusinessMorning,
  directBusinessAfternoon,
}

extension ParseEnumExtraTime on EnumExtraTime {
  int get keyToServer {
    switch (this) {
      case EnumExtraTime.directCommandFullDay:
        return 1;
      case EnumExtraTime.directCommandMorning:
        return 3;
      case EnumExtraTime.directCommandAfternoon:
        return 2;
      case EnumExtraTime.directBusinessFullDay:
        return 4;
      case EnumExtraTime.directBusinessMorning:
        return 6;
      case EnumExtraTime.directBusinessAfternoon:
        return 5;
      default:
        return null;
    }
  }

  String get display {
    switch (this) {
      case EnumExtraTime.directCommandFullDay:
        return "Trực chỉ huy cả ngày (08:00 – 17:30)";
      case EnumExtraTime.directCommandMorning:
        return "Trực chỉ huy sáng (08:00 – 12:00)";
      case EnumExtraTime.directCommandAfternoon:
        return "Trực chỉ huy chiều (13:30 – 17:30)";
      case EnumExtraTime.directBusinessFullDay:
        return "Trực nghiệp vụ cả ngày (08:00 – 17:30)";
      case EnumExtraTime.directBusinessMorning:
        return "Trực nghiệp vụ sáng (08:00 – 12:00)";
      case EnumExtraTime.directBusinessAfternoon:
        return "Trực nghiệp vụ chiều (13:30 – 17:30)";
      default:
        return '';
    }
  }

  static EnumExtraTime getType(int value) {
    switch (value) {
      case 1:
        return EnumExtraTime.directCommandFullDay;
      case 2:
        return EnumExtraTime.directCommandAfternoon;
      case 3:
        return EnumExtraTime.directCommandMorning;
      case 4:
        return EnumExtraTime.directBusinessFullDay;
      case 5:
        return EnumExtraTime.directBusinessAfternoon;
      case 6:
        return EnumExtraTime.directCommandMorning;
      default:
        return EnumExtraTime.directCommandFullDay;
    }
  }
}

enum EnumExtraTimeNotLead {
  directBusinessFullDay,
  directBusinessMorning,
  directBusinessAfternoon,
}

extension ParseEnumExtraTimeNotLead on EnumExtraTimeNotLead {
  int get keyToServer {
    switch (this) {
      case EnumExtraTimeNotLead.directBusinessFullDay:
        return 4;
      case EnumExtraTimeNotLead.directBusinessMorning:
        return 6;
      case EnumExtraTimeNotLead.directBusinessAfternoon:
        return 5;
      default:
        return null;
    }
  }

  String get display {
    switch (this) {
      case EnumExtraTimeNotLead.directBusinessFullDay:
        return "Trực nghiệp vụ cả ngày (08:00 – 17:30)";
      case EnumExtraTimeNotLead.directBusinessMorning:
        return "Trực nghiệp vụ sáng (08:00 – 12:00)";
      case EnumExtraTimeNotLead.directBusinessAfternoon:
        return "Trực nghiệp vụ chiều (13:30 – 17:30)";
      default:
        return '';
    }
  }

  static EnumExtraTimeNotLead getType(int value) {
    switch (value) {
      case 4:
        return EnumExtraTimeNotLead.directBusinessFullDay;
      case 5:
        return EnumExtraTimeNotLead.directBusinessAfternoon;
      case 6:
        return EnumExtraTimeNotLead.directBusinessMorning;
      default:
        return EnumExtraTimeNotLead.directBusinessFullDay;
    }
  }
}

enum TypeSign {
  CONTRACT_OUTSIDE,
  INCOME_TAX,
  PROBATION,
  LABOR,
  ANNEX_CONTRACT,
  COMMIT,
  SOCIAL_INSURANCE,
  CERTIFICATION,
  CERTIFICATE_COMMIT,
  TRAINING_SESSION_BOOK,
  SMART_OTP,
  TRAINING_ELECTRICAL_SAFETY_BOOK,
  WORKLOAD_VERIFICATION,
}

extension ResignationStatusExtension on ResignationStatus {
  int get keyToServer {
    switch (this) {
      case ResignationStatus.waitManagementConfirm:
        return 2;
      case ResignationStatus.managerAgreedQuit:
        return 3;
      case ResignationStatus.managerRefusedQuit:
        return 4;
      case ResignationStatus.unitSubmitDesignation:
        return 5;
      case ResignationStatus.unitRefusedQuit:
        return 6;
      case ResignationStatus.waitTCTResolve:
        return 7;
      case ResignationStatus.presentResignationDecision:
        return 8;
      case ResignationStatus.CEORefusedQuit:
        return 9;
      case ResignationStatus.hadDecisionQuit:
        return 10;
      case ResignationStatus.hasRetired:
        return 11;
      default:
        return null;
    }
  }

  String get display {
    switch (this) {
      case ResignationStatus.waitManagementConfirm:
        return AppStrings.waitManagementConfirm;
      case ResignationStatus.managerAgreedQuit:
        return AppStrings.managerAgreedQuit;
      case ResignationStatus.managerRefusedQuit:
        return AppStrings.managerRefusedQuit;
      case ResignationStatus.unitSubmitDesignation:
        return AppStrings.unitSubmitDesignation;
      case ResignationStatus.unitRefusedQuit:
        return AppStrings.unitRefusedQuit;
      case ResignationStatus.waitTCTResolve:
        return AppStrings.waitTCTResolve;
      case ResignationStatus.presentResignationDecision:
        return AppStrings.presentResignationDecision;
      case ResignationStatus.CEORefusedQuit:
        return AppStrings.CEORefusedQuit;
      case ResignationStatus.hadDecisionQuit:
        return AppStrings.hadDecisionQuit;
      case ResignationStatus.hasRetired:
        return AppStrings.hasRetired;
      default:
        return '';
    }
  }

  static ResignationStatus getType(int value) {
    switch (value) {
      case 2:
        return ResignationStatus.waitManagementConfirm;
      case 3:
        return ResignationStatus.managerAgreedQuit;
      case 4:
        return ResignationStatus.managerRefusedQuit;
      case 5:
        return ResignationStatus.unitSubmitDesignation;
      case 6:
        return ResignationStatus.unitRefusedQuit;
      case 7:
        return ResignationStatus.waitTCTResolve;
      case 8:
        return ResignationStatus.presentResignationDecision;
      case 9:
        return ResignationStatus.CEORefusedQuit;
      case 10:
        return ResignationStatus.hadDecisionQuit;
      case 11:
        return ResignationStatus.hasRetired;
      default:
        return ResignationStatus.waitManagementConfirm;
    }
  }
}

enum JobTitle {
  employees,
  manage,
}

extension JobTitleExtension on JobTitle {
  int get keyToServer {
    switch (this) {
      case JobTitle.employees:
        return 0;
      case JobTitle.manage:
        return 1;
      default:
        return null;
    }
  }

  String get display {
    switch (this) {
      case JobTitle.employees:
        return AppStrings.employees;
      case JobTitle.manage:
        return AppStrings.manage;
      default:
        return '';
    }
  }

  static JobTitle getType(int value) {
    switch (value) {
      case 0:
        return JobTitle.employees;
      case 1:
        return JobTitle.manage;
      default:
        return JobTitle.manage;
    }
  }
}

enum ConfirmStatus { agree, disagree }

extension ConfirmStatusExtension on ConfirmStatus {
  int get keyToServer {
    switch (this) {
      case ConfirmStatus.agree:
        return 0;
      case ConfirmStatus.disagree:
        return 1;
      default:
        return null;
    }
  }

  String get display {
    switch (this) {
      case ConfirmStatus.agree:
        return AppStrings.agree;
      case ConfirmStatus.disagree:
        return AppStrings.disagree;
      default:
        return '';
    }
  }

  static ConfirmStatus getType(int value) {
    switch (value) {
      case 0:
        return ConfirmStatus.agree;
      case 1:
        return ConfirmStatus.disagree;
      default:
        return null;
    }
  }
}

enum StepSelectTitle {
  step_begin,
  step_select_block,
  step_select_unit,
  step_select_department,
  step_select_title,
}

enum TypeListContract {
  all,
  signed,
  not_sign,
}

enum Certification {
  business,
  income,
}

extension CertificationExtension on Certification {
  int get getType {
    switch (this) {
      case Certification.business:
        return 1;
      case Certification.income:
        return 2;
      default:
        return null;
    }
  }
}

enum ReasonBusiness {
  reason_1,
  reason_2,
  reason_3,
  reason_4,
  reason_5,
  other_reason,
}

extension ReasonBusinessExtension on ReasonBusiness {
  String get getName {
    switch (this) {
      case ReasonBusiness.reason_1:
        return AppStrings.reason_business_1;
      case ReasonBusiness.reason_2:
        return AppStrings.reason_business_2;
      case ReasonBusiness.reason_3:
        return AppStrings.reason_business_3;
      case ReasonBusiness.reason_4:
        return AppStrings.reason_business_4;
      case ReasonBusiness.reason_5:
        return AppStrings.reason_business_5;
      case ReasonBusiness.other_reason:
        return AppStrings.other_reason;
      default:
        return "";
    }
  }

  int get getValue {
    switch (this) {
      case ReasonBusiness.reason_1:
        return 1;
      case ReasonBusiness.reason_2:
        return 2;
      case ReasonBusiness.reason_3:
        return 3;
      case ReasonBusiness.reason_4:
        return 4;
      case ReasonBusiness.reason_5:
        return 5;
      case ReasonBusiness.other_reason:
        return 10;
      default:
        return -1;
    }
  }

}

enum ReasonIncome {
  reason_1,
  reason_2,
  reason_3,
  reason_4,
  other_reason,
}

extension ReasonIncomeExtension on ReasonIncome {
  String get getName {
    switch (this) {
      case ReasonIncome.reason_1:
        return AppStrings.reason_income_1;
      case ReasonIncome.reason_2:
        return AppStrings.reason_income_2;
      case ReasonIncome.reason_3:
        return AppStrings.reason_income_3;
      case ReasonIncome.reason_4:
        return AppStrings.reason_income_4;
      case ReasonIncome.other_reason:
        return AppStrings.other_reason;
      default:
        return "";
    }
  }

  int get getValue {
    switch (this) {
      case ReasonIncome.reason_1:
        return 6;
      case ReasonIncome.reason_2:
        return 7;
      case ReasonIncome.reason_3:
        return 8;
      case ReasonIncome.reason_4:
        return 9;
      case ReasonIncome.other_reason:
        return 10;
      default:
        return -1;
    }
  }

}

enum MethodReceiveData {
  method_1,
  method_2,
}

extension MethodReceiveDataExtension on MethodReceiveData {
  String get getName {
    switch (this) {
      case MethodReceiveData.method_1:
        return AppStrings.method_receive_data_1;
      case MethodReceiveData.method_2:
        return AppStrings.method_receive_data_2;
      default:
        return "";
    }
  }

  int get getValue {
    switch (this) {
      case MethodReceiveData.method_1:
        return 1;
      case MethodReceiveData.method_2:
        return 2;
      default:
        return -1;
    }
  }

}
enum IncomeTime {
  one,
  two,
  three,
  four,
  five,
  six,
  seven,
  eight,
  nine,
  ten,
  eleven,
  twelve,
}

extension IncomeTimeExtension on IncomeTime {
  String get getName {
    switch (this) {
      case IncomeTime.one:
        return AppStrings.about_1_month;
        case IncomeTime.two:
        return AppStrings.about_2_month;
        case IncomeTime.three:
        return AppStrings.about_3_month;
        case IncomeTime.four:
        return AppStrings.about_4_month;
        case IncomeTime.five:
        return AppStrings.about_5_month;
        case IncomeTime.six:
        return AppStrings.about_6_month;
        case IncomeTime.seven:
        return AppStrings.about_7_month;
        case IncomeTime.eight:
        return AppStrings.about_8_month;
        case IncomeTime.nine:
        return AppStrings.about_9_month;
        case IncomeTime.ten:
        return AppStrings.about_10_month;
        case IncomeTime.eleven:
        return AppStrings.about_11_month;
        case IncomeTime.twelve:
        return AppStrings.about_12_month;
      default:
        return "";
    }
  }

  int get getValue {
    switch (this) {
      case IncomeTime.one:
        return 1;
      case IncomeTime.two:
        return 2;
      case IncomeTime.three:
        return 3;
      case IncomeTime.four:
        return 4;
      case IncomeTime.five:
        return 5;
      case IncomeTime.six:
        return 6;
      case IncomeTime.seven:
        return 7;
      case IncomeTime.eight:
        return 8;
      case IncomeTime.nine:
        return 9;
      case IncomeTime.ten:
        return 10;
      case IncomeTime.eleven:
        return 11;
      case IncomeTime.twelve:
        return 12;
      default:
        return -1;
    }
  }
}

enum TypeCreateCertification {
  confirmed_business,
  confirmed_income,
  confirmed_all,
  unconfirmed,
}

extension TypeCreateCertificationExtension on TypeCreateCertification {
  int get getValue {
    switch (this) {
      case TypeCreateCertification.confirmed_business:
        return 1;
      case TypeCreateCertification.confirmed_income:
        return 2;
      case TypeCreateCertification.confirmed_all:
        return 3;
      case TypeCreateCertification.unconfirmed:
        return 4;
      default:
        return -1;
    }
  }

}

enum ReasonsNotRegisteringEnum {
  // maternityLeave,
  terminalIllness,
  illnessAccident
}

extension ReasonsNotRegisteringEnumExtension on ReasonsNotRegisteringEnum {
  int get keyToServer {
    switch (this) {
      // case ReasonsNotRegisteringEnum.maternityLeave:
      //   return 0;
      case ReasonsNotRegisteringEnum.terminalIllness:
        return 1;
      case ReasonsNotRegisteringEnum.illnessAccident:
        return 2;
      default:
        return null;
    }
  }

  String get display {
    switch (this) {
      // case ReasonsNotRegisteringEnum.maternityLeave:
      //   return "CBNV sinh con";
      case ReasonsNotRegisteringEnum.terminalIllness:
        return "CBNV mắc bệnh hiểm nghèo";
      case ReasonsNotRegisteringEnum.illnessAccident:
        return "CBNV ốm đau/tai nạn đột xuất";
      default:
        return '';
    }
  }

  static ReasonsNotRegisteringEnum getType(int value) {
    switch (value) {
      // case 0:
      //   return ReasonsNotRegisteringEnum.maternityLeave;
      case 1:
        return ReasonsNotRegisteringEnum.terminalIllness;
      default:
        return ReasonsNotRegisteringEnum.illnessAccident;
    }
  }
}

enum USER_ROLE {
  HSTD_OUTSOURCE,
  HSTD_WEEKLY_LEAVE_MANAGER ,
  HSTD_WEEKLY_LEAVE_TDV,
}