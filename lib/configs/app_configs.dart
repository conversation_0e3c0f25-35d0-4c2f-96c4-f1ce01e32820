import 'package:hstd/configs/app_env_config.dart';

class AppConfigs {
  static Environment env = Environment.prod;

  ///API Env
  static String get baseUrl => env.baseUrl;

  static String get baseUrlWO => env.baseUrlWO;

  static String get baseUrlSign => env.baseUrlSign;

  static String get baseUrlPayment => env.baseUrlPayment;

  static String get baseUrlSrqService => env.baseUrlSrqService;

  static String get baseUrlHrmService => env.baseUrlHrmService;

  static String get baseUrlIcnService => env.baseUrlIcnService;

  static String get envName => env.envName;

  static String get baseSmartOtpKey => env.baseSmartOtpKey;

  static String get filePathSmartOtPolicy => env.filePathSmartOtPolicy;

  ///Paging
  static const pageSize = 20;

  /// max scroll extend
  static const maxScrollExtend = 300;
}
