enum Environment {
  dev,
  stg,
  prod,
}

extension EnvironmentExt on Environment {
  String get envName {
    switch (this) {
      case Environment.dev:
        return 'LOCAL';
      case Environment.stg:
        return 'STAGING';
      case Environment.prod:
        return 'PROD';
    }
  }

  String get baseUrl {
    switch (this) {
      case Environment.dev:
        return "http://10.207.112.70:8864/hsdt-service";
        // return "http://192.168.1.252:8864/hsdt-service";
      // return "https://apis-public.congtrinhviettel.com.vn/hsdt-service/noti";
      case Environment.stg:
        return "http://10.248.242.62:8864/hsdt-service";
      case Environment.prod:
        return "https://gw-public.congtrinhviettel.com.vn/hsdt/hsdt-service";
    }
  }

  String get baseUrlWO {
    switch (this) {
      case Environment.dev:
        return "http://10.248.242.62:8206/qlwo-service";
      case Environment.stg:
        return "http://10.248.242.62:8206/qlwo-service";
      case Environment.prod:
        return "https://apiwo.congtrinhviettel.com.vn/qlwo-service";
    }
  }
  String get baseUrlSign {
    switch (this) {
      case Environment.dev:
        return "http://10.207.112.41:8864/hsdt-service";
      case Environment.stg:
        return "http://10.207.112.41:8864/hsdt-service";
      case Environment.prod:
        return "https://gw-public.congtrinhviettel.com.vn/hsdt/hsdt-service";
    }
  }

  String get baseUrlPayment{
    switch (this) {
      case Environment.dev:
        return "http://10.248.242.246:8000/oft-service";
        // return "http://14.225.7.172:8669/oft-service";
      case Environment.stg:
        return "http://10.248.242.246:8000/oft-service";
      case Environment.prod:
        return "https://gw-public.congtrinhviettel.com.vn/hsdt/oft-service";
    }
  }
  String get baseSmartOtpKey {
    switch (this) {
      case Environment.dev:
        return "gImq3FHh2OL7e9Vc9ARtA7RbaNa2Ld";
      case Environment.stg:
        return "gImq3FHh2OL7e9Vc9ARtA7RbaNa2Ld";
      case Environment.prod:
        return "gImq3FHh2OL7e9Vc9ARtA7RbaNa2Ld";
    }
  }
  String get filePathSmartOtPolicy {
    switch (this) {
      case Environment.dev:
        return "2024-07-04-145046EEE9EF765F40C014379B0679253FC935.pdf";
      case Environment.stg:
        return "2024-07-04-145046EEE9EF765F40C014379B0679253FC935.pdf";
      case Environment.prod:
        return "2024-08-09-1759157809F020454939659D2D6B623668CB64.pdf";
    }
  }

  String get baseUrlSrqService {
    switch (this) {
      case Environment.dev:
        return "http://10.248.242.246:8000/srq-service";
      case Environment.stg:
        return "http://10.248.242.246:8000/srq-service";
      case Environment.prod:
        return "https://gw-public.congtrinhviettel.com.vn/hsdt/srq-service";
    }
  }

  String get baseUrlHrmService {
    switch (this) {
      case Environment.dev:
        return "http://10.248.242.246:8000/hrm-service";
      case Environment.stg:
        return "http://10.248.242.246:8000/hrm-service";
      case Environment.prod:
        return "https://gw-public.congtrinhviettel.com.vn/hsdt/hrm-service";
    }
  }

    String get baseUrlIcnService {
    switch (this) {
      case Environment.dev:
        return "http://10.248.242.246:8000/icn-service";
      case Environment.stg:
        return "http://10.248.242.246:8000/icn-service";
      case Environment.prod:
        return "https://gw-public.congtrinhviettel.com.vn/hsdt/icn-service";
    }
  }
}
