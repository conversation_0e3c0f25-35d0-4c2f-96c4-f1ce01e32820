import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:typed_data';

import 'package:file_picker/file_picker.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/screen/home_screen.dart';
import 'package:hstd/utils/exception.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:path/path.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../preferences/data_center.dart';
import '../screen/login/view/login_form.dart';

class ApiRequest {
  // Helper method to log curl equivalent of requests
  static String _getCurlCommand(String method, String url, Map<String, String> headers, [dynamic body]) {
    String curl = 'curl -X $method';

    // Add headers
    headers.forEach((key, value) {
      if (value != null) {
        curl += " -H '$key: $value'";
      }
    });

    // Add body if present
    if (body != null) {
      curl += " -d '${body.toString()}'";
    }

    // Add URL
    curl += " '$url'";

    return curl;
  }

  // Helper method to log response
  static void _logResponse(http.Response response) {
    developer.log('Response Status: ${response.statusCode}');
    developer.log('Response Headers: ${response.headers}');

    // Try to format JSON response if possible
    try {
      final dynamic jsonResponse = json.decode(response.body);
      developer.log('Response Body: ${const JsonEncoder.withIndent('  ').convert(jsonResponse)}');
    } catch (e) {
      // If not JSON, log as is (truncate if too large)
      final String responseBody = response.body.length > 1000
          ? '${response.body.substring(0, 1000)}... (truncated)'
          : response.body;
      developer.log('Response Body: $responseBody');
    }
  }

  static Future<http.Response> post(
      url, {
        body,
        Encoding encoding,
      }) async {
    final token = await getToken();
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": token != null ? "Bearer $token" : null
    };

    // Log curl equivalent
    developer.log(_getCurlCommand('POST', url, headers, body));

    try {
      http.Response response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: body,
      );

      // Log response
      _logResponse(response);

      if (response.statusCode == 401) {
        HomeScreen.showToast(
          AppColors.primaryColor,
          "Phiên đăng nhập của bạn đã hết hạn!",
        );
        throw AuthorizationException("Tên đăng nhập và mật khẩu không hợp lệ!");
      }
      if (response.statusCode == 403) throw AuthorizationException("Bạn không có quyền!");
      return response;
    } catch (e) {
      developer.log('Error in POST request: $e');
      print(e);
      DataCenter.shared().setLoginFaceId(loginFaceIdStatus: false);
      phoneController.clear();
      rethrow; // Rethrow to allow caller to handle the exception
    }
  }

  static Future<http.Response> get(
      url, {
        body,
      }) async {
    final token = await getToken();
    Map<String, String> headers = {
      "Content-Type": "application/json; charset=utf-8",
      "Authorization": "Bearer $token",
      "Accept": "application/json",
    };

    // Log curl equivalent
    developer.log(_getCurlCommand('GET', url, headers));
    developer.log("url: ${Uri.parse(url)}");

    try {
      http.Response response = await http.get(Uri.parse(url), headers: headers);

      // Log response
      _logResponse(response);

      if (response.statusCode == 401) {
        HomeScreen.showToast(
          AppColors.primaryColor,
          "Phiên đăng nhập của bạn đã hết hạn!",
        );
        throw AuthorizationException("Đăng nhập thất bại!");
      }
      if (response.statusCode == 403)
        throw AuthorizationException("Bạn không có quyền!");
      return response;
    } catch (e) {
      developer.log('Error in GET request: $e');
      rethrow;
    }
  }

  static Future<http.Response> put(
      url, {
        body,
        Encoding encoding,
      }) async {
    final token = await getToken();
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $token"
    };

    // Log curl equivalent
    developer.log(_getCurlCommand('PUT', url, headers, body));

    try {
      http.Response response = await http.put(
        Uri.parse(url),
        headers: headers,
        body: body,
        encoding: encoding,
      );

      // Log response
      _logResponse(response);

      return response;
    } catch (e) {
      developer.log('Error in PUT request: $e');
      rethrow;
    }
  }

  static Future<http.Response> delete(url) async {
    final token = await getToken();
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $token"
    };

    // Log curl equivalent
    developer.log(_getCurlCommand('DELETE', url, headers));

    try {
      http.Response response = await http.delete(Uri.parse(url), headers: headers);

      // Log response
      _logResponse(response);

      return response;
    } catch (e) {
      developer.log('Error in DELETE request: $e');
      rethrow;
    }
  }

  static Future<String> getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.get(AppConstant.KEY_TOKEN);
  }

  static Future<http.StreamedResponse> postFile(
      List<FileUploadRequest> files,
      String url,
      ) async {
    final token = await getToken();
    Map<String, String> headers = {"Authorization": "Bearer $token"};

    developer.log('POST FILE request to: $url');
    developer.log('Headers: $headers');
    developer.log('Uploading ${files.length} files');

    var request = http.MultipartRequest('POST', Uri.parse(url))
      ..headers.addAll(headers);
    files.forEach(
          (element) {
        http.MultipartFile file = http.MultipartFile.fromBytes(
          'files',
          element.fileUnit,
          filename: element.fileName,
        );
        request.files.add(file);
        developer.log('Added file: ${element.fileName}');
      },
    );

    try {
      var response = await request.send();

      // Log response
      developer.log('Response Status: ${response.statusCode}');
      developer.log('Response Headers: ${response.headers}');

      // Convert stream to string to log body
      final responseBody = await response.stream.bytesToString();
      try {
        final jsonResponse = json.decode(responseBody);
        developer.log('Response Body: ${const JsonEncoder.withIndent('  ').convert(jsonResponse)}');
      } catch (e) {
        developer.log('Response Body: $responseBody');
      }

      // Create a new stream from the string for the caller
      final streamedResponse = http.StreamedResponse(
        Stream.value(utf8.encode(responseBody)),
        response.statusCode,
        headers: response.headers,
        contentLength: responseBody.length,
        reasonPhrase: response.reasonPhrase,
      );

      return streamedResponse;
    } catch (e) {
      developer.log('Error in POST FILE request: $e');
      rethrow;
    }
  }

  static Future<http.StreamedResponse> postFileAttach(
      List<PlatformFile> files,
      String url,
      ) async {
    final token = await getToken();
    Map<String, String> headers = {'Content-type': 'multipart/form-data;'};
    headers['Authorization'] = 'Bearer $token';

    developer.log('POST FILE ATTACH request to: $url');
    developer.log('Headers: $headers');
    developer.log('Uploading ${files.length} files');

    var request = new http.MultipartRequest("POST", Uri.parse(url));
    for (var file in files) {
      request.files.add(
        await http.MultipartFile.fromPath(
          'files',
          file.path,
          filename: basename(file.path),
        ),
      );
      developer.log('Added file: ${basename(file.path)}');
      request.headers.addAll(headers);
    }

    try {
      var response = await request.send();

      // Log response
      developer.log('Response Status: ${response.statusCode}');
      developer.log('Response Headers: ${response.headers}');

      // Convert stream to string to log body
      final responseBody = await response.stream.bytesToString();
      try {
        final jsonResponse = json.decode(responseBody);
        developer.log('Response Body: ${const JsonEncoder.withIndent('  ').convert(jsonResponse)}');
      } catch (e) {
        developer.log('Response Body: $responseBody');
      }

      // Create a new stream from the string for the caller
      final streamedResponse = http.StreamedResponse(
        Stream.value(utf8.encode(responseBody)),
        response.statusCode,
        headers: response.headers,
        contentLength: responseBody.length,
        reasonPhrase: response.reasonPhrase,
      );

      return streamedResponse;
    } catch (e) {
      developer.log('Error in POST FILE ATTACH request: $e');
      rethrow;
    }
  }

  static Future<http.StreamedResponse> postOTP(
      url,
      String fileBill,
      Uint8List imageSign,
      int paymentId,
      String signaturePath,
      {
        headers,
        Encoding encoding,
      }) async {
    developer.log('POST OTP request to: $url');
    developer.log('Headers: $headers');
    developer.log('Payment ID: $paymentId');
    developer.log('File Bill: ${fileBill != null ? "Provided" : "Not provided"}');
    developer.log('Image Sign: ${imageSign != null ? "${imageSign.length} bytes" : "Not provided"}');

    var request = http.MultipartRequest('POST', Uri.parse(url))
      ..headers.addAll(headers);
    request.fields['paymentId'] = paymentId.toString();
    if (signaturePath != null) {
      request.fields['signaturePath'] = signaturePath.toString();
    }
    if (fileBill != null && fileBill.isNotEmpty) {
      var _imgFileBill = await http.MultipartFile.fromPath('fileBill', fileBill,
          contentType: MediaType('application', 'octet-stream'));
      request.files.add(_imgFileBill);
      developer.log('Added file bill: $fileBill');
    }
    if (imageSign != null) {
      var _imageSign = http.MultipartFile.fromBytes('imageSign', imageSign,
          contentType: MediaType('application', 'octet-stream'),
          filename: 'imageSign');
      request.files.add(_imageSign);
      developer.log('Added image sign');
    }

    try {
      var response = await request.send();

      // Log response
      developer.log('Response Status: ${response.statusCode}');
      developer.log('Response Headers: ${response.headers}');

      // Convert stream to string to log body
      final responseBody = await response.stream.bytesToString();
      try {
        final jsonResponse = json.decode(responseBody);
        developer.log('Response Body: ${const JsonEncoder.withIndent('  ').convert(jsonResponse)}');
      } catch (e) {
        developer.log('Response Body: $responseBody');
      }

      // Create a new stream from the string for the caller
      final streamedResponse = http.StreamedResponse(
        Stream.value(utf8.encode(responseBody)),
        response.statusCode,
        headers: response.headers,
        contentLength: responseBody.length,
        reasonPhrase: response.reasonPhrase,
      );

      return streamedResponse;
    } catch (e) {
      developer.log('Error in POST OTP request: $e');
      rethrow;
    }
  }
}