import 'dart:convert';
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:hstd/common/app_colors.dart';
import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/screen/home_screen.dart';
import 'package:hstd/utils/exception.dart';
import 'package:http/http.dart' as http;
import 'package:nb_utils/nb_utils.dart';
import 'package:path/path.dart';
import 'package:http_parser/http_parser.dart';

import '../preferences/data_center.dart';
import '../screen/login/view/login_form.dart';

class ApiRequest {
  static Future<http.Response> post(
    url, {
    body,
    Encoding encoding,
  }) async {
    final token = await getToken();
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": token != null ? "Bearer $token" : null
    };
    try {
      http.Response response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: body,
      );

      if (response.statusCode == 401) {
        HomeScreen.showToast(
          AppColors.primaryColor,
          "Phiên đăng nhập của bạn đã hết hạn!",
        );
        throw AuthorizationException("Tên đăng nhập và mật khẩu không hợp lệ!");
      }
      if (response.statusCode == 403) throw AuthorizationException("Bạn không có quyền!");
      return response;
    } catch (e) {
      print(e);
      DataCenter.shared().setLoginFaceId(loginFaceIdStatus: false);
      phoneController.clear();
    }
  }

  static Future<http.Response> get(
    url, {
    body,
  }) async {
    final token = await getToken();
    Map<String, String> headers = {
      "Content-Type": "application/json; charset=utf-8",
      "Authorization": "Bearer $token",
      "Accept": "application/json",
    };
    log("url: ${Uri.parse(url)}");

    http.Response response = await http.get(Uri.parse(url), headers: headers,);
    if (response.statusCode == 401) {
      HomeScreen.showToast(
        AppColors.primaryColor,
        "Phiên đăng nhập của bạn đã hết hạn!",
      );
      throw AuthorizationException("Đăng nhập thất bại!");
    }
    if (response.statusCode == 403)
      throw AuthorizationException("Bạn không có quyền!");
    return response;
  }

  static Future<http.Response> put(
    url, {
    body,
    Encoding encoding,
  }) async {
    final token = await getToken();
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $token"
    };
    return http.put(
      Uri.parse(url),
      headers: headers,
      body: body,
      encoding: encoding,
    );
  }

  static Future<http.Response> delete(url) async {
    final token = await getToken();
    Map<String, String> headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $token"
    };
    return http.delete(Uri.parse(url), headers: headers);
  }

  static Future<String> getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.get(AppConstant.KEY_TOKEN);
  }

  static Future<http.StreamedResponse> postFile(
    List<FileUploadRequest> files,
    String url,
  ) async {
    final token = await getToken();
    Map<String, String> headers = {"Authorization": "Bearer $token"};
    var request = http.MultipartRequest('POST', Uri.parse(url))
      ..headers.addAll(headers);
    files.forEach(
      (element) {
        http.MultipartFile file = http.MultipartFile.fromBytes(
          'files',
          element.fileUnit,
          filename: element.fileName,
        );
        request.files.add(file);
      },
    );
    return request.send();
  }

  static Future<http.StreamedResponse> postFileAttach(
    List<PlatformFile> files,
    String url,
  ) async {
    final token = await getToken();
    Map<String, String> headers = {'Content-type': 'imultipart/form-data;'};
    headers['Authorization'] = 'Bearer $token';
    var request = new http.MultipartRequest("POST", Uri.parse(url));
    for (var file in files) {
      request.files.add(
        await http.MultipartFile.fromPath(
          'files',
          file.path,
          filename: basename(file.path),
        ),
      );
      request.headers.addAll(headers);
    }
    return request.send();
  }

  static Future<http.StreamedResponse> postOTP(
    url,
    String fileBill,
    Uint8List imageSign,
    int paymentId, {
    headers,
    Encoding encoding,
  }) async {
    var request = http.MultipartRequest('POST', Uri.parse(url))
      ..headers.addAll(headers);
    request.fields['paymentId'] = paymentId.toString();
    if (fileBill != null && fileBill.isNotEmpty) {
      var _imgFileBill = await http.MultipartFile.fromPath('fileBill', fileBill,
          contentType: MediaType('application', 'octet-stream'));
      request.files.add(_imgFileBill);
    }
    var _imageSign = http.MultipartFile.fromBytes('imageSign', imageSign,
        contentType: MediaType('application', 'octet-stream'),
        filename: 'imageSign');
    request.files.add(_imageSign);
    var response = await request.send();
    return response;
  }
}
