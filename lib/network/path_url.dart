import 'package:hstd/configs/app_configs.dart';

String SMART_OTP_KEY({String systemId, String userId, String deviceId, String entityId, String entityType}) =>
    '$systemId$userId$deviceId$entityId$entityType${AppConfigs.baseSmartOtpKey}';
final URL_LOGIN = '${AppConfigs.baseUrl}/auth/app-login';
final URL_LOGIN_BY_OTP = '${AppConfigs.baseUrl}/auth/os-otp/submit-login';
final URL_PROFILE = '${AppConfigs.baseUrl}/profile';
final URL_GET_PROFILE = '${AppConfigs.baseUrl}/profile/get-profile';
final URL_APP_VERSION = '${AppConfigs.baseUrl}/v1/app-version';
final URL_UPDATE_FIREBASE_TOKEN = '${AppConfigs.baseUrl}/firebaseNotificationService/update-token';

final URL_DOWNLOAD = '${AppConfigs.baseUrl}/download';
final URL_DOWNLOAD_FILE = '${AppConfigs.baseUrl}/download-file';
final URL_DOWNLOAD_FILE_VOFFICE = '${AppConfigs.baseUrl}/download-file-vo';
final URL_DOWNLOAD_FILE_MULTI = '${AppConfigs.baseUrl}/download-multifile';
final URL_UPLOAD_FILE = '${AppConfigs.baseUrl}/upload-file';

final URL_CREATE_OTP = '${AppConfigs.baseUrl}/otp/create';
final URL_SUBMIT_OTP = '${AppConfigs.baseUrl}/otp/submit';
final URL_LOGIN_OTP = '${AppConfigs.baseUrl}/auth/os-otp/create';

final URL_LABOR_SIGN =
    '${AppConfigs.baseUrl}/labor-contract/add-employee-signature-to'
    '-contract';
final URL_COMMIT_SIGN =
    '${AppConfigs.baseUrl}/employee-commit/add-employee-signature';
final URL_PROBATION_SIGN =
    '${AppConfigs.baseUrl}/probationary-contract/add-employee'
    '-signature-to'
    '-contract';
final URL_EMPLOYEE = '${AppConfigs.baseUrl}/employee';
final URL_EMPLOYEE_SEARCH = '$URL_EMPLOYEE/search';

final URL_ETHNIC_SEARCH = '${AppConfigs.baseUrl}/ethnic/search';
final URL_NATIONALITY_SEARCH =
    '${AppConfigs.baseUrl}/province-area/get-area-by-level';
final URL_MEDICAL_SEARCH = '${AppConfigs.baseUrl}/medical-facility/search';
final URL_CONFIRM =
    '${AppConfigs.baseUrl}/employee-increase/user-confirm-insurance-info';

final URL_CONFIRM_REISSUE =
    '${AppConfigs.baseUrl}/employee-request-insurance-number/user-confirm-insurance-info';

final URL_ORGANIZATION = '${AppConfigs.baseUrl}/organization';
final URL_ORGANIZATION_UNIT = '$URL_ORGANIZATION/unit/search';
final URL_ORGANIZATION_DEPARTMENT = '$URL_ORGANIZATION/department/search';

final URL_TERMINATE_CONTRACT = '${AppConfigs.baseUrl}/terminate-contract';
final URL_SEND_TERMINATE =
    '${AppConfigs.baseUrl}/terminate-contract/send-resignation';

final URL_CHANGEPASS = "${AppConfigs.baseUrl}/auth/change-password";
final URL_DISTRICT = "${AppConfigs.baseUrl}/provinces/1/districts";
final URL_PROJECT = "${AppConfigs.baseUrl}/projects";
final URL_BUILDING = "${AppConfigs.baseUrl}/buildings";
final URL_FLOOR = "${AppConfigs.baseUrl}/floors";
final URL_CUSTOMER = "${AppConfigs.baseUrl}/customers/search-fields";
final URL_PARAMS = "${AppConfigs.baseUrl}/sys-params";
final URL_CLUSTERS = "${AppConfigs.baseUrl}/clusters/search-fields";
final URL_GPON = "${AppConfigs.baseUrl}/gpons/search-fields";
final URL_HISTORY = "${AppConfigs.baseUrl}/histories";
final URL_RENEW = "${AppConfigs.baseUrl}/renews";
final URL_HISTORY_CALL = "${AppConfigs.baseUrl}/history-call";
final URL_HISTORY_SEARCH = "${AppConfigs.baseUrl}/histories/search-fields";

final URL_EMPLOYEE_INCREASE = "${AppConfigs.baseUrl}/employee-increase";
final URL_GET_HEALTH_INSURANCE_USER_INFO = "${AppConfigs.baseUrl}/employee-origin/find-by-sso";
final URL_EMPLOYEE_INCREASE_CONFIRM_REISSUE =
    "${AppConfigs.baseUrl}/employee-request-insurance-number/getBHYT";
final URL_EMPLOYEE_INCREASE_BY_CODE =
    "${AppConfigs.baseUrl}/employee-increase/find-by-employee-code";
final URL_FAMILY_RELATIONSHIP = "${AppConfigs.baseUrl}/family-relationship";
final URL_UPDATE_EMPLOYEE_INCREASE =
    "${AppConfigs.baseUrl}/employee-increase/update-family";
final URL_GET_INFO_ID_CARD =
    "${AppConfigs.baseUrl}/employee-increase/validate-id-card";
final URL_GET_INFO_BIRTH_CERTIFICATE =
    "${AppConfigs.baseUrl}/employee-increase/validate-gks";
final URL_UPLOAD_IMAGE = "${AppConfigs.baseUrl}/upload-file";

final URL_GET_ANNEXS_CONTRACT =
    '${AppConfigs.baseUrl}/v1/annex-contracts/get-by-user';
final URL_GET_ANNEX_CONTRACT =
    '${AppConfigs.baseUrl}/v1/annex-contracts/get-by-employee-login';
final URL_SIGN_ANNEX_CONTRACT = '${AppConfigs.baseUrl}/v1/annex-contracts/sign';
final URL_GET_QUESTION = '${AppConfigs.baseUrl}/v1/terminate-surveys/criteria';

String URL_GET_QUESTION_EMPLOYEE(String id) =>
    '${AppConfigs.baseUrl}/v1/employee-survey-criteria/type/$id';

String URL_UPDATE_EMPLOYEE_SURVEY(String id) =>
    '${AppConfigs.baseUrl}/v1/employee-surveys/$id/update';
final URL_GET_LIST_SURVEY =
    '${AppConfigs.baseUrl}/v1/employee-surveys/find-by-user';
final URL_RICE_TICKET =
    '${AppConfigs.baseUrlWO}/service/orderLunchRsPublicService/mobile/getImagePhieuDatCom';
final URL_TOTAL_RICE_TICKET =
    '${AppConfigs.baseUrlWO}/service/orderLunchRsPublicService/mobile/listOrderLunchEmployee';
final URL_GET_SAFETY_CERTIFICATES =
    '${AppConfigs.baseUrl}/v1/labor-safety-certificates/search';
final URL_GET_LIST_OSH =
    '${AppConfigs.baseUrl}/v1/labor-safety-certificates/book/unsigned';
final URL_GET_SAFETY_ELECTRICAL = '${AppConfigs.baseUrl}/v1/labor-electrical-safety/search';
final URL_GET_SAFETY_ELECTRICAL_UNSIGNED = '${AppConfigs.baseUrl}/v1/labor-electrical-safety/book/unsigned';
final URL_CHANGE_PASSWORD = '${AppConfigs.baseUrl}/auth/change-password';

String URL_GET_LIST_TAX_VOUCHER(String year) =>
    '${AppConfigs.baseUrl}/v1/tax-payment/getTaxPaymentByUser?year=$year';
final URL_GET_BANK_ACCOUNT =
    '${AppConfigs.baseUrl}/v1/salary-bank-accounts/get-by-user';

String URL_GET_SALARY_DETAIL(int templateCode, int month, int year) =>
    '${AppConfigs.baseUrl}/v2/salary-import-details/get-salary-by-user?templateCode=$templateCode&month=$month&year=$year';

String URL_GET_SALARY(int level, int parentTemplateCode, int month, int year,
        {int salaryType}) =>
    '${AppConfigs.baseUrl}/v1/salary-import-details/agg-by-user?level=$level&parentTemplateCode=$parentTemplateCode&month=$month&year=$year&salaryType=$salaryType';

String URL_GET_OTHER_SALARY({int templateCode, int month, int year}) =>
    '${AppConfigs.baseUrl}/v1/salary-import-details/get-other-salary-by-user?templateCode=$templateCode&month=$month&year=$year';

String URL_GET_BONUS_SALARY({String fromDate, String toDate, int salaryType}) =>
    '${AppConfigs.baseUrl}/v1/salary-import-details/get-bonus-salary?fromDate=$fromDate&toDate=$toDate&salaryType=$salaryType';

String URL_GET_WELFARE_SALARY({String fromDate, String toDate, int salaryType}) =>
    '${AppConfigs.baseUrl}/v1/salary-import-details/get-welfare-salary?fromDate=$fromDate&toDate=$toDate&salaryType=$salaryType';

String URL_GET_SALARY_DASHBOARD({String fromDate, String toDate}) =>
    '${AppConfigs.baseUrl}/v1/salary-import-details/report-by-user?fromDate=$fromDate&toDate=$toDate';

String URL_EXPORT_MONTHLY_SALARY({int templateCode, int month, int year}) =>
    '${AppConfigs.baseUrl}/v1/salary-import-details/export-monthly-salary?templateCode=$templateCode&month=$month&year=$year';

String URL_GET_WARNING_TASK =
    '${AppConfigs.baseUrl}/v1/uncomplete-works/search-mobile';

String URL_MARK_READ_WARNING({int id}) =>
    '${AppConfigs.baseUrl}/v1/uncomplete-works/$id/mark-as-read';

String URL_CONFIRM_WARNING({int id}) =>
    '${AppConfigs.baseUrl}/v1/uncomplete-works/$id/confirm';

String urlSalaryUserManual = "${AppConfigs.baseUrl}/download-file?path=HDSD.PDF";

//ký cam kết
 String URL_GET_COMMIT_LIST = '${AppConfigs.baseUrlSign}/sign-document/search';
 String URL_GET_COMMIT_DETAIL = '${AppConfigs.baseUrlSign}/sign-document/get-detail';
 String URL_SIGN_APPROVE = '${AppConfigs.baseUrlSign}/sign-document/approve';
 String URL_SIGN_REJECT = '${AppConfigs.baseUrlSign}/sign-document/reject';

String URL_GET_LIST_COMMIT = '${AppConfigs.baseUrl}/employee-commit/find-by-user';

String URL_GET_LIST_LABOR_CONTRACT = '${AppConfigs.baseUrl}/contractOs/searchSigned';

String URL_GET_COMMIT_BY_ID({int id}) => '${AppConfigs.baseUrl}/employee-commit/find-by-id/$id';

String URL_GET_FILE_COMMIT({int id}) => '${AppConfigs.baseUrl}/employee-commit/get-file/$id';

String URL_GET_FILE_COMMIT_ADD_DOCS({int id}) => '${AppConfigs.baseUrl}/employee-commit/generate-info-add-doc-commit-app/$id';

String URL_GET_FILE_LABOR_CONTRACT({int id}) => '${AppConfigs.baseUrl}/contractOs/get-file/$id';

String URL_ADD_EMPLOYEE_SIGNATURE = '${AppConfigs.baseUrl}/employee-commit/add-employee-signature';

String URL_SIGN_CONTRACT_OUTSIDE = '${AppConfigs.baseUrl}/contractOs/employee-sign';

String URL_GET_REGISTER_LABOR_PROTECTION_DETAIL({int id}) =>
    '${AppConfigs.baseUrl}/v1/employee-period-bhld/$id/getListDetail';

String URL_UPDATE_REGISTER_LABOR_PROTECTION_DETAIL =
    '${AppConfigs.baseUrl}/v1/employee-period-bhld';

String URL_GET_REGISTER_LABOR_PROTECTION_LIST =
    '${AppConfigs.baseUrl}/v1/employee-period-bhld/getListByEmployeeCodeForRegister';

String URL_GET_GRANTED_LIST =
    '${AppConfigs.baseUrl}/v1/employee-period-bhld/getListByEmployeeCodeForGranted';

String URL_CONFIRM_GRANTED({int id}) =>
    '${AppConfigs.baseUrl}/v1/employee-period-bhld/$id/accept';

String URL_GET_TOTAL_REGISTER({int registerId}) =>
    '${AppConfigs.baseUrl}/v1/employee-period-bhld/$registerId/countNotificationForRegister';

String URL_GET_NOTIFY_CONTRACT() =>
    '${AppConfigs.baseUrl}/v1/resign-employee-reject/notification';

String URL_CONFIRM_NOTIFY() =>
    '${AppConfigs.baseUrl}/v1/resign-employee-reject/approve';

String URL_NOT_APPROVE_NOTIFY() =>
    '${AppConfigs.baseUrl}/v1/resign-employee-reject/notification/not-approve';

//Bhxh
String URL_GET_INFORMATION_COMMITMENT_HAND_OVER({int id}) => '${AppConfigs.baseUrl}/social-insurance/getInformation-on-commitment-to-hand-over-detail/$id';

String URL_GENERATE_FILE_COMMITMENT_TO_HAND_OVER = '${AppConfigs.baseUrl}/social-insurance/generate-file-commitment-to-handover';

String URL_CANCLE_SIGN_COMMIT = '${AppConfigs.baseUrl}/social-insurance/cancel-signing-commitment';

String URL_GET_LIST_COMMIT_SOCIAL_INSURANCE = '${AppConfigs.baseUrl}/social-insurance/getInformation-on-commitment-to-hand-over';

String URL_GET_LIST_RESIGNATION_LETTERS = '${AppConfigs.baseUrl}/terminate-contract/list-of-resignation-letters';

String URL_GET_TERMINATE_CONTRACT_BY_ID({int id}) => '${AppConfigs.baseUrl}/terminate-contract/$id';

String URL_UPDATE_TERMINATE_CONTRACT_BY_ID({int id}) => '${AppConfigs.baseUrl}/terminate-contract/update-mobile/$id';

String URL_CREATE_TERMINATE_CONTRACT_BY_ID = '${AppConfigs.baseUrl}/terminate-contract/create-mobile';

String URL_GET_LIST_EMPLOYEE = '${AppConfigs.baseUrl}/v1/employee-vcc/search';

String URL_CONFIRM_RESIGNATION_APPROVAL = '${AppConfigs.baseUrl}/terminate-contract/confirm-resignation-approval';

String URL_GET_PROFILE_VCC = '${AppConfigs.baseUrl}/v1/employee-vcc/get-user-login-info';

String URL_GET_COUNT_DOCTUMENT = '${AppConfigs.baseUrl}/profile/count-document';

String URL_GET_USER_INFO_AD_ROADMAP = '${AppConfigs.baseUrl}/position-standard/app/get-user-info';

String URL_GET_USER_INFO_AD_ROADMAP_V2 = '${AppConfigs.baseUrl}/position-standard/app/get-user-info-v2';

String URL_GET_BACKGROUND = '${AppConfigs.baseUrl}/position-standard/app/download-background';

String URL_GET_STANDARD = '${AppConfigs.baseUrl}/position-standard/app/get-standard';

String URL_GET_POSITION_STANDARD({int id}) => '${AppConfigs.baseUrl}/position-standard/$id';

String URL_GET_LIST_OPTION = '${AppConfigs.baseUrl}/position-standard/option';

String URL_GET_USER_STANDARD_NOW = '${AppConfigs.baseUrl}/position-standard/app/get-user-standard-now';

String URL_GET_PROCESS_CAREER({int nowId, int targetId}) => '${AppConfigs.baseUrl}/process-career/app/get-process-career/$nowId/$targetId';

String URL_GET_REGISTERED_ROADMAP = '${AppConfigs.baseUrl}/process-career/app/registered';

String URL_CHECK_REGISTERED_ROADMAP = '${AppConfigs.baseUrl}/process-career/app/check-exits';

String URL_GET_LIST_POSITION_STANDARD = '${AppConfigs.baseUrl}/position-standard/search';

String URL_VALIDATE_REGISTER = '${AppConfigs.baseUrl}/process-career/app/validate-register';

String URL_REGISTER_ROADMAP = '${AppConfigs.baseUrl}/process-career/app/register';

String URL_REGISTER_HISTORY = '${AppConfigs.baseUrl}/process-career/app/history-register';

String URL_GET_REGISTER_HISTORY_ROADMAP({int id}) => '${AppConfigs.baseUrl}/process-career/app/$id';

String URL_GET_PROCESS_CAREER_RANK = '${AppConfigs.baseUrl}/process-career/app/register/top10';

String URL_SUGGEST_REPLACEMENT_CARD = '${AppConfigs.baseUrl}/employee-request-insurance-number/suggest-replacement-card';

String URL_GET_HEALTH_INSURANCE_REGISTERED = '${AppConfigs.baseUrl}/employee-request-insurance-number/get-not-accept-employee-request';



//Xac nhan nghi viec
// String URL_GET_LIST_CONFIRM_RESIGNATION = '${AppConfigs.baseUrl}/terminate-contract/list-of-resignation-letters';
String URL_GET_LIST_CONFIRM_RESIGNATION = '${AppConfigs.baseUrl}/terminate-contract/list-resignation-approval';
String URL_GET_INFORMATION_CONFIRM_RESIGNATION({int id}) => '${AppConfigs.baseUrl}/terminate-contract/$id';

//thue TNCN
String URL_GET_INCOME_TAX_LIST =
    '${AppConfigs.baseUrl}/v1/taxpayment-register/search-by-mobile';
String URL_UPDATE_REGISTER_INCOME_TAX =
    '${AppConfigs.baseUrl}/v1/taxpayment-register/register-from-app';
String URL_UPDATE_ID_CARD_GET_INFORMATION =
    '${AppConfigs.baseUrl}/v1/taxpayment-register/validate-id-card';
String URL_GET_INCOME_TAX_DETAIL({int id}) =>
    '${AppConfigs.baseUrl}/v1/taxpayment-register/$id';
String URL_GET_COUNT_TAX_PAYMENT =
    '${AppConfigs.baseUrl}/v1/taxpayment-register/count-for-app';

String URL_GET_INCOME_TAX_CONTRACT = '${AppConfigs.baseUrl}/v1/taxpayment-register/get-authory-file';

String URL_SIGN_TAX_PAYMENT = '${AppConfigs.baseUrl}/v1/taxpayment-register/sign-authory';

String URL_VALIDATE_TAX_NUMBER =
    '${AppConfigs.baseUrl}/v1/taxpayment-register/check-exist-tax-number';

String URL_CHECK_EXIST_NUMBER = '${AppConfigs.baseUrl}/v1/taxpayment-register/check-exist-id-number';

String URL_VALIDATE_CREATE_INSURANCE =
    '${AppConfigs.baseUrl}/employee-request-insurance-number/check-create-employee-request';

//xac nhan cong tac thu nhap
String URL_GET_CERTIFICATION_USER_INFO = '${AppConfigs.baseUrl}/confirm-bussiness-income/employeeInfo';

String URL_GET_TYPE_CREATE_CERTIFICATION = '${AppConfigs.baseUrl}/confirm-bussiness-income/check-type';

String URL_GET_CHECK_ID_CARD_CERTIFICATION = '${AppConfigs.baseUrl}/confirm-bussiness-income/check-cccd';

String URL_GENERATE_FILE_REQUEST_CERTIFICATION = '${AppConfigs.baseUrl}/confirm-bussiness-income/generate-file';

String URL_GENERATE_FILE_CERTIFICATION = '${AppConfigs.baseUrl}/confirm-bussiness-income/generate-file2';

String URL_SIGN_CERTIFICATION = '${AppConfigs.baseUrl}/confirm-bussiness-income/sign-authory';

String URL_GET_LIST_CERTIFICATION =
    '${AppConfigs.baseUrl}/confirm-bussiness-income/getAll';

String URL_CANCEL_CERTIFICATION =
    '${AppConfigs.baseUrl}/confirm-bussiness-income/cancelled';

String URL_GET_PDF_CERTIFICATION =
    '${AppConfigs.baseUrl}/confirm-bussiness-income/detailConfirmBI';

String URL_GET_COMMIT_INFO_BY_ID({int id}) => '${AppConfigs.baseUrl}/employee-commit/generate-info-id/$id';

String URL_GENERATE_FILE_COMMIT = '${AppConfigs.baseUrl}/employee-commit/get-additional-certificate-file';

String URL_GENERATE_FILE_ADD_DOCS_COMMIT = '${AppConfigs.baseUrl}/employee-commit/get-additional-doc-commit-file';

String URL_SIGN_COMMIT = '${AppConfigs.baseUrl}/employee-commit/add-signature-additional-certificate-file';

String URL_SIGN_OSH = '${AppConfigs.baseUrl}/v1/labor-safety-certificates/book/sign-book-training-session';

String URL_SIGN_ELECTRICAL_SAFETY = '${AppConfigs.baseUrl}/v1/labor-electrical-safety/book/sign-book-training-session';

String URL_SIGN_COMMIT_ADD_DOCS = '${AppConfigs.baseUrl}/employee-commit/add-signature-additional-doc-commit-file';

String URL_GET_TITLE_PERIOD_BY_USER = '${AppConfigs.baseUrl}/v1/title-period/getTitlePeriodByUser';

///Vi tri thiet bi
String URL_GET_DEVICE_LIST = '${AppConfigs.baseUrl}/extra-work/device/list';
String URL_CREATE_DEVICE = '${AppConfigs.baseUrl}/extra-work/device/create';
String URL_UPDATE_PRIMARY({int id}) =>
    '${AppConfigs.baseUrl}/extra-work/device/update-primary/$id';

String URL_DOWNLOAD_FILE_BY_ID({int id, String checkSum}) =>
    '${AppConfigs.baseUrlPayment}/v1/download/$id?checkSum=${checkSum}';
//Payment Periods
String URL_PAYMENT_OUTSOURCE({String startDate, String endDate}) => '${AppConfigs.baseUrlPayment}/v1/payments/outsource?startDate=$startDate&endDate=$endDate';
String URL_PAYMENT_OUTSOURCE_DETAIL({int paymentId, String type}) => '${AppConfigs.baseUrlPayment}/v1/payments/outsource/detail/$paymentId/$type';
String URL_PAYMENT_OUTSOURCE_CONFIRM = '${AppConfigs.baseUrlPayment}/v1/payments/outsource/confirm';

String URL_INSPECT_DEVICE({int id}) =>
    '${AppConfigs.baseUrl}/extra-work/device/inspect/$id';

String URL_COUNT_DATA_NOT_READ = '${AppConfigs.baseUrl}/firebaseNotificationService/countDataNotRead';
String URL_GET_NOTIFICATIONS = '${AppConfigs.baseUrl}/firebaseNotificationService/search';
String URL_READ_NOTIFICATIONS = '${AppConfigs.baseUrl}/firebaseNotificationService/update';
String URL_READ_ALL = '${AppConfigs.baseUrl}/firebaseNotificationService/updateAll';
String URL_GET_KPI = '${AppConfigs.baseUrl}/kiMobileService/getKpi';

String URL_REGISTER_SMART_OTP = '${AppConfigs.baseUrl}/smart-otp/register';
String URL_UNREGISTER_SMART_OTP = '${AppConfigs.baseUrl}/smart-otp/unregister';
String URL_VALIDATE_SMART_OTP = '${AppConfigs.baseUrl}/smart-otp/validate';
String URL_GET_LIST_REGISTER_WORK = '${AppConfigs.baseUrl}/extra-work-register/search-app';
String URL_DELETE_REGISTER_WORK({int id}) => '${AppConfigs.baseUrl}/extra-work-register/$id';
String URL_GET_CURRENT_PERIOD = '${AppConfigs.baseUrl}/extra-work-period/get-current-period-app';
String URL_GET_TYPE_REGISTER = '${AppConfigs.baseUrl}/extra-work-period/get-type-register';
String URL_REGISTER_WORK_HOLIDAY = '${AppConfigs.baseUrl}/extra-work-register/save-app';
String URL_GET_REGISTER_BY_ID({int id}) => '${AppConfigs.baseUrl}/extra-work-register/find-by-id/$id';
String URL_GET_LIST_WORK = '${AppConfigs.baseUrl}/extra-work/search';
String URL_GET_RESULT_WORK_BY_ID({int id}) => '${AppConfigs.baseUrl}/extra-work/$id';
String URL_UPDATE_WORK_HOLIDAY = '${AppConfigs.baseUrl}/extra-work/update-work-app';
String URL_GET_BANNER_HOME= '${AppConfigs.baseUrl}/sys-config/detail-by-key/BANNER_GIFT';
String URL_GET_REGISTER_GIFT_TUTORIAL= '${AppConfigs.baseUrl}/sys-config/detail-by-key/HD_DK_QUA_TET';
String URL_GIFT_REG_LIST= '${AppConfigs.baseUrlSrqService}/v1/gift-regs/personal';
String URL_GIFT_INFO({String year})=> '${AppConfigs.baseUrlSrqService}/v1/gift-regs/personal/gift-info?year=${year}';
String URL_GIFT_INFO_SUBMIT({int giftRegId})=> '${AppConfigs.baseUrlSrqService}/v1/gift-regs/personal/${giftRegId}';
String URL_PARENT_INFO= '${AppConfigs.baseUrlHrmService}/v1/family-relationships/personal';
String URL_GET_ALL_VACATION = '${AppConfigs.baseUrl}/travel-register/search';
String URL_GET_TRAVEL_PERIOD_BY_ID({int id}) => '${AppConfigs.baseUrl}/travel-period/$id';
String URL_SAVE_REGISTER_LOCATION_TRAVEL = '${AppConfigs.baseUrl}/travel-register/save-register-location';
String URL_GET_TRAVEL_REGISTER_BY_ID({int id}) => '${AppConfigs.baseUrl}/travel-register/$id';
String URL_SAVE_REGISTER_VACATION = '${AppConfigs.baseUrl}/travel-register/save-register-time';
String URL_GET_SENT_MAIL_FILE = '${AppConfigs.baseUrl}/travel-register/get-send-mail-file';
String URL_SENT_MAIL = '${AppConfigs.baseUrl}/travel-register/send-email';