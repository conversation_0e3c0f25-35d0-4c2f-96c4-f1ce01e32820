import 'dart:convert';
import 'package:hstd/configs/enum.dart';
import 'package:hstd/models/advancement_roadmap/option_dto.dart';
import 'package:hstd/models/advancement_roadmap/position_standard_dto.dart';
import 'package:hstd/models/advancement_roadmap/process_career_dto.dart';
import 'package:hstd/models/advancement_roadmap/register_roadmap_dto.dart';
import 'package:hstd/models/advancement_roadmap/register_roadmap_history_dto.dart';
import 'package:hstd/models/advancement_roadmap/register_roadmap_request.dart';
import 'package:hstd/models/advancement_roadmap/search_standard_request.dart';
import 'package:hstd/models/advancement_roadmap/standard_dto.dart';
import 'package:hstd/models/advancement_roadmap/user_info_dto.dart';
import 'package:hstd/models/advancement_roadmap/value_option.dart';
import 'package:hstd/models/common_dto.dart';
import 'package:hstd/models/organization.dart';
import 'package:hstd/models/response/array_response.dart';
import 'package:hstd/models/update_resutl.dart';
import 'package:hstd/models/user_info_v2_dto.dart';
import 'package:hstd/network/api_request.dart';
import 'package:hstd/network/path_url.dart';
import 'package:hstd/repositories/base_service.dart';
import 'package:http/http.dart' as http;

abstract class AdvancementRoadmapService {
  Future<AdRoadUserInfoDto> getUserInfo();

  Future<AdRoadUserInfoV2Dto> getUserInfoV2();

  Future<StandardDto> getStandard();

  Future<ArrayResponse<ProcessCareerDto>> getListStandard({int nowId, int targetId});

  Future<PositionStandardDto> getPositionStandardById({int id});

  Future<List<OptionDto>> getListOption();

  Future<PositionStandardDto> getPositionStandardNow();

  Future<ArrayResponse<Organization>> getListUnit({
    int page,
    int size,
    int id,
    String code,
    String name,
  });

  Future<ArrayResponse<Organization>> getListDepartment({
    int page,
    int size,
    int orgParentId,
    String code,
    String name,
  });

  Future<ArrayResponse<PositionStandardDto>> getListTitle({
    SearchStandardRequest request,
  });

  Future<ArrayResponse<Organization>> getUnits(DataSearchRoadmapRequest request);

  Future<ArrayResponse<Organization>> getDepartments(DataSearchRoadmapRequest request);

  Future<String> validateRegister();

  Future<UpdateResult> registerRoadmap(RegisterRoadmapRequest request);

  Future<ArrayResponse<RegisterRoadmapHistoryDto>> getRegisterHistory();

  Future<ProcessCareerDto> getRegisterHistoryRoadmap({int id});

  Future<ArrayResponse<RegisterRoadmapHistoryDto>> getProcessCareerRank();

  Future<ArrayResponse<RegisterRoadmapDto>> getRegisteredRoadmap();

  Future<int> checkRegisteredRoadmap();

}

class AdvancementRoadmapServiceImp extends BaseService
    implements AdvancementRoadmapService {
  @override
  Future<AdRoadUserInfoDto> getUserInfo() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_USER_INFO_AD_ROADMAP,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (jsonData['status'] == 0) {
        return null;
      }

      final result = AdRoadUserInfoDto.fromJson(jsonData['result']);

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<AdRoadUserInfoV2Dto> getUserInfoV2() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_USER_INFO_AD_ROADMAP_V2,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (jsonData['status'] == 0) {
        return null;
      }

      final result = AdRoadUserInfoV2Dto.fromJson(jsonData['result']);

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<StandardDto> getStandard() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_STANDARD,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (jsonData['status'] == 0) {
        return null;
      }

      final result = StandardDto.fromJson(jsonData['result']);

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ArrayResponse<ProcessCareerDto>> getListStandard({int nowId, int targetId}) async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_PROCESS_CAREER(nowId: nowId, targetId: targetId),
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 0) {
        return null;
      }

      final result = ArrayResponse.fromJson(
        jsonData,
            (json) => ProcessCareerDto.fromJson(json),
      );

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ArrayResponse<RegisterRoadmapDto>> getRegisteredRoadmap() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_REGISTERED_ROADMAP,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (jsonData['status'] == 0) {
        return null;
      }

      final result = ArrayResponse.fromJson(
        jsonData,
            (json) => RegisterRoadmapDto.fromJson(json),
      );

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<int> checkRegisteredRoadmap() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_CHECK_REGISTERED_ROADMAP,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (jsonData['status'] == 0) {
        return null;
      }

      print(jsonData.toString());

      final result = jsonData['result'];

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ArrayResponse<RegisterRoadmapHistoryDto>> getProcessCareerRank() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_PROCESS_CAREER_RANK,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 0) {
        return null;
      }

      final result = ArrayResponse.fromJson(
        jsonData,
            (json) => RegisterRoadmapHistoryDto.fromJson(json),
      );

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ProcessCareerDto> getRegisterHistoryRoadmap({int id}) async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_REGISTER_HISTORY_ROADMAP(id: id),
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (response.statusCode == 0) {
        return null;
      }

      final result = ProcessCareerDto.fromJson(jsonData['result']);

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<PositionStandardDto> getPositionStandardById({int id}) async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_POSITION_STANDARD(id: id),
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (jsonData['status'] == 0) {
        return null;
      }

      final result = PositionStandardDto.fromJson(jsonData['result']);

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<List<OptionDto>> getListOption() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_LIST_OPTION,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = (jsonData as List)
          ?.map((item) => OptionDto.fromJson(item as Map<String, dynamic>))
          ?.toList();

      for(int i=0; i<result.length;i++) {
        if (result[i].value!=null) {
          var jsonValue = json.decode(result[i].value);
          result[i].listValue = (jsonValue as List)
              ?.map((item) => ValueOption.fromJson(item as Map<String, dynamic>))
              ?.toList();
        }
      }

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<PositionStandardDto> getPositionStandardNow() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_USER_STANDARD_NOW,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (jsonData['status'] == 0) {
        return null;
      }

      final result = PositionStandardDto.fromJson(jsonData['result']);

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ArrayResponse<Organization>> getListUnit({
    int page,
    int size,
    int id,
    String code,
    String name,
  }) async {
    DataSearchRoadmapRequest request =
    new DataSearchRoadmapRequest(page: page ?? 0, size: size ?? 10, pagedFlag: true, notCheckUnitRole: true);
    List<CriteriaSearch> criteriaSearch = [];
    if (code != null && code.length > 0) {
      criteriaSearch.add(new CriteriaSearch(
          field: 'code',
          operation: ":",
          type: TYPE_DATA.STRING.values,
          value: code,
          andFlag: true));
    }

    if (name != null && name.length > 0) {
      criteriaSearch.add(new CriteriaSearch(
          field: 'name',
          operation: ":",
          type: TYPE_DATA.STRING.values,
          value: name,
          andFlag: false));
    } else {
      criteriaSearch.add(new CriteriaSearch(
          field: 'name',
          operation: "NOT LIKE",
          type: TYPE_DATA.STRING.values,
          value: "Trung tâm Kỹ thuật Viettel",
          andFlag: true));
    }
    criteriaSearch.add(new CriteriaSearch(
        field: 'status',
        operation: ":",
        type: TYPE_DATA.INTEGER.values,
        value: 1,
        andFlag: true));
    criteriaSearch.add(new CriteriaSearch(
        field: 'orgLevel',
        operation: ":",
        type: TYPE_DATA.INTEGER.values,
        value: 5,
        andFlag: true));
    if (id != null) {
      criteriaSearch.add(new CriteriaSearch(
          field: 'organizationId',
          operation: "=",
          type: TYPE_DATA.STRING.values,
          value: id,
          andFlag: true));
    }

    request.criteriaList = criteriaSearch;
    List<SortSearch> sortList = [];
    sortList.add(new SortSearch(
        direction: 'asc',
        property: "name"));
    request.sortList = sortList;
    return this.getUnits(request);
  }

  @override
  Future<ArrayResponse<Organization>> getUnits(DataSearchRoadmapRequest request) async {
    final requestEncode = json.encode(request.toJson());

    print(requestEncode.toString());

    http.Response response =
    await ApiRequest.post(URL_ORGANIZATION_UNIT, body: requestEncode);

    print("${response.request.url.toString()}");

    var responseJson = json.decode(utf8.decode(response.bodyBytes));

    if (response.statusCode == 0) {
      return null;
    }

    final result = ArrayResponse.fromJson(
      responseJson,
          (json) => Organization.fromJson(json),
    );

    print(responseJson.toString());

    return result;
  }

  @override
  Future<ArrayResponse<Organization>> getListDepartment({
    int page,
    int size,
    int orgParentId,
    String code,
    String name,
  }) async {
    DataSearchRoadmapRequest request =
    new DataSearchRoadmapRequest(page: page ?? 0, size: size ?? 100000, pagedFlag: true, notCheckUnitRole: true);
    List<CriteriaSearch> criteriaSearch = [];
    criteriaSearch.add(new CriteriaSearch(
        field: 'status',
        operation: ":",
        type: TYPE_DATA.INTEGER.values,
        value: 1,
        andFlag: true));
    if (orgParentId != null) {
      criteriaSearch.add(new CriteriaSearch(
          field: 'orgParentId',
          operation: "=",
          type: TYPE_DATA.INTEGER.values,
          value: orgParentId,
          andFlag: true));
    }

    if (code != null && code.length > 0) {
      criteriaSearch.add(new CriteriaSearch(
          field: 'code',
          operation: ":",
          type: TYPE_DATA.STRING.values,
          value: code,
          andFlag: true));
    }

    if (name != null && name.length > 0) {
      criteriaSearch.add(new CriteriaSearch(
          field: 'name',
          operation: ":",
          type: TYPE_DATA.STRING.values,
          value: name,
          andFlag: false));
    }
    request.criteriaList = criteriaSearch;
    List<SortSearch> sortList = [];
    sortList.add(new SortSearch(
        direction: 'asc',
        property: "name"));
    request.sortList = sortList;
    return this.getDepartments(request);
  }

  @override
  Future<ArrayResponse<Organization>> getDepartments(DataSearchRoadmapRequest request) async {
    final requestEncode = json.encode(request.toJson());

    print(requestEncode.toString());

    http.Response response =
    await ApiRequest.post(URL_ORGANIZATION_DEPARTMENT, body: requestEncode);

    print("${response.request.url.toString()}");

    var responseJson = json.decode(utf8.decode(response.bodyBytes));

    if (response.statusCode == 0) {
      return null;
    }

    final result = ArrayResponse.fromJson(
      responseJson,
          (json) => Organization.fromJson(json),
    );

    print(responseJson.toString());

    return result;
  }

  @override
  Future<ArrayResponse<PositionStandardDto>> getListTitle({
    SearchStandardRequest request,
  }) async {
    assert(request != null);
    final requestEncode = json.encode(request.toJson());
    print(requestEncode);

    try {
      http.Response response = await ApiRequest.post(
        URL_GET_LIST_POSITION_STANDARD,
        body: requestEncode,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ArrayResponse.fromJson(
        jsonData,
            (json) => PositionStandardDto.fromJson(json),
      );

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<String> validateRegister() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_VALIDATE_REGISTER,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (jsonData['status'] == 0) {
        return null;
      }

      final result = jsonData['result'] ?? '';

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<UpdateResult> registerRoadmap(RegisterRoadmapRequest request) async {
    try {
      assert(request != null);
      final requestEncode = json.encode(request.toJson());
      print(requestEncode);

      http.Response response = await ApiRequest.post(
        URL_REGISTER_ROADMAP,
        body: requestEncode,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      if (jsonData['status'] == 0) {
        return null;
      }

      final result = UpdateResult.fromJson(jsonData);

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ArrayResponse<RegisterRoadmapHistoryDto>> getRegisterHistory() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_REGISTER_HISTORY,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ArrayResponse.fromJson(
        jsonData,
            (json) => RegisterRoadmapHistoryDto.fromJson(json),
      );

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

}
