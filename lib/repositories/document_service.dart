import 'dart:convert';

import 'package:hstd/models/certification/detail_pdf_dto.dart';
import 'package:hstd/models/document/labor_contract.dart';
import 'package:hstd/models/document/request_document.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/models/income_tax/file_income_tax_dto.dart';
import 'package:hstd/models/response/array_response.dart';
import 'package:hstd/network/api_request.dart';
import 'package:hstd/network/path_url.dart';
import 'package:hstd/repositories/base_service.dart';
import 'package:http/http.dart' as http;

abstract class DocumentService {
  Future<ArrayResponse<LaborContractDto>> getListLaborContract({
    RequestDocument request,
  });

  Future<FileData> downloadFile(int id);

  Future<LaborContractDto> getLaborContractSign();

  Future<FileIncomeTaxDto> getIncomeTaxSign(int taxPaymentRegisterId);

  Future<DetailPdfDto> getCertificationPdf(int getCertificationPdf);

  Future<FileData> getDefaultSignature(String signType);

}

class DocumentServiceImp extends BaseService implements DocumentService {
  @override
  Future<ArrayResponse<LaborContractDto>> getListLaborContract({
    RequestDocument request,
  }) async {
    assert(request != null);
    final requestEncode = json.encode(request.toJson());
    print(requestEncode);

    try {
      http.Response response = await ApiRequest.post(
        URL_GET_LIST_LABOR_CONTRACT,
        body: requestEncode,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ArrayResponse.fromJsonType2(
        jsonData,
            (json) => LaborContractDto.fromJson(json),
      );

      print(jsonData.toString());

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<LaborContractDto> getLaborContractSign() async {

    RequestDocument request = new RequestDocument(
      size: 1,
      page: 0,
      contractStatus: 1,
    );

    final requestEncode = json.encode(request.toJson());
    print(requestEncode);

    try {
      http.Response response = await ApiRequest.post(
        URL_GET_LIST_LABOR_CONTRACT,
        body: requestEncode,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ArrayResponse.fromJsonType2(
        jsonData,
            (json) => LaborContractDto.fromJson(json),
      );

      print(jsonData.toString());

      if (result.data!=null) {
        if (result.data.length>0) {
          return result.data.first;
        }
      }
      return null;

    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<FileData> downloadFile(int id) async {
    http.Response response =
    await ApiRequest.post(URL_GET_FILE_LABOR_CONTRACT(id: id));
    var jsonData = json.decode(utf8.decode(response.bodyBytes));
    if (jsonData['status'] == 0) {
      String message = jsonData['message'];
      addMessageError(message);
      return null;
    } else {
      return FileData.fromJson(jsonData['result']);
    }
  }

  @override
  Future<FileIncomeTaxDto> getIncomeTaxSign(int taxPaymentRegisterId) async {
    RequestFileIncomeTaxDto request = new RequestFileIncomeTaxDto(
      taxPaymentRegisterId: taxPaymentRegisterId
    );

    final requestEncode = json.encode(request.toJson());
    print(requestEncode);

    try {
      http.Response response = await ApiRequest.post(
        URL_GET_INCOME_TAX_CONTRACT,
        body: requestEncode,
      );

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = FileIncomeTaxDto.fromJson(jsonData['result']);
      print(jsonData.toString());
      if (result != null) {
        return result;
      }
      return null;

    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<DetailPdfDto> getCertificationPdf(int getCertificationPdf) async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_PDF_CERTIFICATION + '/$getCertificationPdf',
      );
      print("${response.request.url.toString()}");
      var jsonData = json.decode(utf8.decode(response.bodyBytes));
      final result = DetailPdfDto.fromJson(jsonData['result']);
      print(jsonData.toString());
      if (result != null) {
        return result;
      }
      return null;

    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<FileData> getDefaultSignature(String signType) async {
    try {
      http.Response response = await ApiRequest.post(
        urlFileSignatureDefault,
        body: jsonEncode({ "signType": signType }),
      );

      print("${response.request?.url}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));
      print(jsonData.toString());

      if (jsonData['result'] != null) {
        final result = FileData.fromJson(jsonData['result']);
        return result;
      }

      return null;

    } catch (ex) {
      print(ex);
      rethrow; // giữ nguyên stack trace
    }
  }
}
