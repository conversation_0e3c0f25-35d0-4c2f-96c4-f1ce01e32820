import 'dart:async';
import 'dart:convert';

import 'package:hstd/models/commit/generate_commit_request.dart';
import 'package:hstd/models/file_data_dto.dart';
import 'package:hstd/models/register_vacation/list_register_vacation_response_model.dart';
import 'package:hstd/models/register_vacation/save_location_request.dart';
import 'package:hstd/models/response/array_response.dart';
import 'package:hstd/models/response/object_response.dart';
import 'package:hstd/network/api_request.dart';
import 'package:hstd/network/path_url.dart';
import 'package:hstd/repositories/base_service.dart';
import 'package:http/http.dart' as http;

abstract class RegisterVacationService {
  Future<ArrayResponse<VacationModel>> getAllRegisterVacation({
    String employeeCode,
  });

  Future<ObjectResponse<dynamic>> getDetailTravelPeriod({
    int id,
  });

  Future<ObjectResponse<dynamic>> saveLocationTravelPeriod({
    SaveLocationRequest request,
  });

  Future<ObjectResponse<dynamic>> getDetailTravelRegister({
    int id,
  });

  Future<ObjectResponse<dynamic>> saveRegisterTime({
    VacationModel request,
  });

  Future<FileData> getSentMailFile({
    int id,
  });

  Future<ObjectResponse<dynamic>> sentMail({
    int id,
  });
}

class RegisterVacationServiceImp extends BaseService
    implements RegisterVacationService {
  @override
  Future<ArrayResponse<VacationModel>> getAllRegisterVacation({
    String employeeCode,
  }) async {
    try {
      http.Response response = await ApiRequest.post(
        URL_GET_ALL_VACATION,
        body: json.encode({"employeeCode": employeeCode}),
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ArrayResponse.fromJson(
        jsonData,
        (json) => VacationModel.fromJson(json),
      );

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ObjectResponse<dynamic>> getDetailTravelPeriod({
    int id,
  }) async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_TRAVEL_PERIOD_BY_ID(id: id),
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ObjectResponse.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ObjectResponse<dynamic>> saveLocationTravelPeriod({
    SaveLocationRequest request,
  }) async {
    assert(request != null);
    final requestEncode = json.encode(request.toJson());
    try {
      http.Response response = await ApiRequest.post(
          URL_SAVE_REGISTER_LOCATION_TRAVEL,
          body: requestEncode);

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ObjectResponse.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ObjectResponse<dynamic>> getDetailTravelRegister({
    int id,
  }) async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_TRAVEL_REGISTER_BY_ID(id: id),
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ObjectResponse.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ObjectResponse<dynamic>> saveRegisterTime({
    VacationModel request,
  }) async {
    assert(request != null);
    final requestEncode = json.encode(request.toJson());
    try {
      http.Response response = await ApiRequest.post(URL_SAVE_REGISTER_VACATION,
          body: requestEncode);

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ObjectResponse.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<FileData> getSentMailFile({
    int id,
  }) async {
    final requestEncode = json.encode({"id": id});
    print(requestEncode);

    try {
      http.Response response = await ApiRequest.post(
        URL_GET_SENT_MAIL_FILE,
        body: requestEncode,
      );
      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = FileData.fromJson(jsonData['result']);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ObjectResponse<dynamic>> sentMail({
    int id,
  }) async {
    final requestEncode = json.encode({"id": id});
    print(requestEncode);

    try {
      http.Response response = await ApiRequest.post(
        URL_SENT_MAIL,
        body: requestEncode,
      );
      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ObjectResponse.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }
}
