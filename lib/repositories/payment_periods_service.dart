import 'dart:convert';
import 'dart:typed_data';

import 'package:hstd/configs/app_constant.dart';
import 'package:hstd/models/download_response.dart';
import 'package:hstd/models/otp_dto.dart';
import 'package:hstd/models/payment_periods/payment_periods_confirm_dto.dart';
import 'package:hstd/models/payment_periods/payment_periods_dto.dart';
import 'package:hstd/models/response/array_response.dart';
import 'package:hstd/network/api_request.dart';
import 'package:hstd/network/path_url.dart';
import 'package:hstd/repositories/header_request_confirm_payment.dart';
import 'package:http/http.dart' as http;
import 'package:nb_utils/nb_utils.dart';

abstract class PaymentPeriodsService {
  Future<ArrayResponse<PaymentPeriodsDto>> getPaymentOutsource(String startDate,
      String endDate);

  Future<DownloadResponse> downloadFile(int id, String type);

  Future<DownloadResponse> downloadFileById(int id, String checkSum);

  Future<OtpResponse> confirmPayment(PaymentPeriodsConfirmDto request,
      String fileBill, Uint8List imageSign);

  Future<Otp> createOtp(OtpRequest request);
}

class PaymentPeriodsServiceImpl implements PaymentPeriodsService {
  @override
  Future<ArrayResponse<PaymentPeriodsDto>> getPaymentOutsource(String startDate,
      String endDate) async {
    http.Response response = await ApiRequest.get(
        URL_PAYMENT_OUTSOURCE(startDate: startDate, endDate: endDate));
    var jsonData = json.decode(utf8.decode(response.bodyBytes));

    final result = ArrayResponse.fromJson6(
      jsonData,
          (json) => PaymentPeriodsDto.fromJson(json),
    );
    return result;
  }

  @override
  Future<DownloadResponse> downloadFile(int id, String type) async {
    http.Response response = await ApiRequest.get(
        URL_PAYMENT_OUTSOURCE_DETAIL(paymentId: id, type: type));
    try {
      var jsonString = json.decode(utf8.decode(response.bodyBytes));
      if (jsonString["status"] == 0) {
        return DownloadResponse(result: false, message: jsonString["message"]);
      }
    } catch (e) {
      return DownloadResponse(
          result: true, message: "", image: response.bodyBytes);
    }
  }

  @override
  Future<DownloadResponse> downloadFileById(int id, String checkSum) async {
    http.Response response = await ApiRequest.get(
        URL_DOWNLOAD_FILE_BY_ID(id: id, checkSum: checkSum));
    try {
      var jsonString = json.decode(utf8.decode(response.bodyBytes));
      if (jsonString["status"] == 0) {
        return DownloadResponse(result: false, message: jsonString["message"]);
      }
    } catch (e) {
      return DownloadResponse(
          result: true, message: "", image: response.bodyBytes);
    }
  }

  static Future<String> getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.get(AppConstant.KEY_TOKEN);
  }

  @override
  Future<OtpResponse> confirmPayment(PaymentPeriodsConfirmDto request,
      String fileBill, Uint8List imageSign) async {
    final token = await getToken();
    final headerRequest = "{\"otpId\": ${request
        .otpId}, \"otpCode\": \"${request
        .otpCode}\", \"isSmartOtp\": \"${request
        .isSmartOtp}\", \"deviceId\": \"${request.deviceId}\"}";

    Map<String, String> headers = {
      "Client-Otp-Key": headerRequest,
      "Content-Type": "multipart/form-data",
      "Accept": "*/*",
      "Authorization": token != null ? "Bearer $token" : null,
    };
    http.StreamedResponse streamedResponse = await ApiRequest.postOTP(
      URL_PAYMENT_OUTSOURCE_CONFIRM,
      fileBill,
      imageSign,
      request.paymentId,
      headers: headers,
    );
    if (streamedResponse.statusCode == 200) {
      String body = await streamedResponse.stream.bytesToString();
      var jsonDecode = json.decode(body);

      return OtpResponse(result: true, message: jsonDecode['message']);
    } else {
      final responseBody = await streamedResponse.stream.bytesToString();
      try {
        final errorJson = jsonDecode(responseBody);
        final errorMessage = errorJson['message'] as String;
        print('Error: $errorMessage');
        return OtpResponse(result: false, message: errorMessage);
      } catch (e) {
        print('Error parsing error response: $e');
      }
      return null;
    }
  }

  @override
  Future<Otp> createOtp(OtpRequest request) async {
    final requestEncode = json.encode(request.toJson1());
    http.Response response =
    await ApiRequest.post(URL_CREATE_OTP, body: requestEncode);
    var jsonData = json.decode(response.body);
    return Otp.fromJson(jsonData['result']);
  }
}
