import 'dart:async';
import 'dart:convert';

import 'package:hstd/models/devices_dto.dart';
import 'package:hstd/models/salary_dto.dart';
import 'package:hstd/network/api_request.dart';
import 'package:hstd/network/path_url.dart';
import 'package:hstd/repositories/base_service.dart';
import 'package:http/http.dart' as http;

import '../models/notificaton/notification_request.dart';
import '../models/notificaton/notification_response_dto.dart';
import '../models/reward_employee_dto.dart';
import '../screen/employee_profile/ki_information/model/ki_response_model.dart';
import '../screen/employee_profile/ki_information/model/kpi_request.dart';

abstract class KpiService {
  Future<KiResponseModel> getKpi(KpiRequest request);
}

class KpiServiceImp extends BaseService implements KpiService{

  @override
  Future<KiResponseModel> getKpi(KpiRequest request) async {
    try {
      assert(request != null);
      final requestEncode = json.encode(request.toJson());
      print(requestEncode);
      http.Response response = await ApiRequest.post(URL_GET_KPI, body: requestEncode);

      print("${response.request.url.toString()}");

      var jsonData = json.decode(utf8.decode(response.bodyBytes));
      return KiResponseModel.fromJson(jsonData);
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }
}
