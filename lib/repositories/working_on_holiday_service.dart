import 'dart:convert';

import 'package:hstd/models/response/array_response.dart';
import 'package:hstd/models/response/object_response.dart';
import 'package:hstd/models/working_on_holiday/current_period_response_model.dart';
import 'package:hstd/network/api_request.dart';
import 'package:hstd/network/path_url.dart';
import 'package:hstd/repositories/base_service.dart';
import 'package:http/http.dart' as http;

import '../models/working_on_holiday/working_holiday_request.dart';
import '../models/working_on_holiday/working_on_holiday_dto.dart';

abstract class WorkingOnHolidayService {
  Future<ArrayResponse<WorkingOnHolidayDto>> getWorkingOnHolidayRegister({
    WorkingOnHolidayRequest request,
  });

  Future<ArrayResponse<WorkingOnHolidayDto>> getWorkingOnHoliday({
    WorkingOnHolidayRequest request,
  });

  Future<ObjectResponse> deleteWorkingOnHoliday({
    int id,
  });

  Future<CurrentPeriodResponseModel> getCurrentPeriod();
  Future<ObjectResponse> getTypeRegister();

  Future<ObjectResponse> registerWorkingOnHoliday({
    WorkingOnHolidayRequest request,
  });

  Future<ObjectResponse> updateWorkingOnHoliday({
    WorkingOnHolidayRequest request,
  });

  Future<WorkingOnHolidayDto> getWorkById({int id});

  Future<WorkingOnHolidayDto> getResultWorkById({int id});
}

class WorkingOnHolidayServiceImp extends BaseService
    implements WorkingOnHolidayService {
  @override
  Future<ArrayResponse<WorkingOnHolidayDto>> getWorkingOnHolidayRegister({
    WorkingOnHolidayRequest request,
  }) async {
    assert(request != null);
    final requestEncode = json.encode(request.toJson());
    print(requestEncode);

    try {
      http.Response response = await ApiRequest.post(
        URL_GET_LIST_REGISTER_WORK,
        body: requestEncode,
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ArrayResponse.fromJson(
        jsonData,
        (json) => WorkingOnHolidayDto.fromJson(json),
      );

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ArrayResponse<WorkingOnHolidayDto>> getWorkingOnHoliday({
    WorkingOnHolidayRequest request,
  }) async {
    assert(request != null);
    final requestEncode = json.encode(request.toJson());
    print(requestEncode);

    try {
      http.Response response = await ApiRequest.post(
        URL_GET_LIST_WORK,
        body: requestEncode,
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ArrayResponse.fromJson(
        jsonData,
            (json) => WorkingOnHolidayDto.fromJson(json),
      );

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ObjectResponse> deleteWorkingOnHoliday({
    int id,
  }) async {
    try {
      http.Response response = await ApiRequest.delete(
        URL_DELETE_REGISTER_WORK(id: id),
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ObjectResponse.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<CurrentPeriodResponseModel> getCurrentPeriod() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_CURRENT_PERIOD,
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = CurrentPeriodResponseModel.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ObjectResponse> getTypeRegister() async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_TYPE_REGISTER,
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ObjectResponse.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ObjectResponse> registerWorkingOnHoliday({WorkingOnHolidayRequest request}) async {
    try {
      assert(request != null);
      final requestEncode = json.encode(request.toJson());
      http.Response response = await ApiRequest.post(
        URL_REGISTER_WORK_HOLIDAY,body: requestEncode
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ObjectResponse.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<WorkingOnHolidayDto> getWorkById({int id}) async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_REGISTER_BY_ID(id: id),
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = WorkingOnHolidayDto.fromJson(ObjectResponse.fromJson(jsonData).data);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<WorkingOnHolidayDto> getResultWorkById({int id}) async {
    try {
      http.Response response = await ApiRequest.get(
        URL_GET_RESULT_WORK_BY_ID(id: id),
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = WorkingOnHolidayDto.fromJson(ObjectResponse.fromJson(jsonData).data);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }

  @override
  Future<ObjectResponse> updateWorkingOnHoliday({WorkingOnHolidayRequest request}) async {
    try {
      assert(request != null);
      final requestEncode = json.encode(request.toJson());
      http.Response response = await ApiRequest.post(
          URL_UPDATE_WORK_HOLIDAY,body: requestEncode
      );

      var jsonData = json.decode(utf8.decode(response.bodyBytes));

      final result = ObjectResponse.fromJson(jsonData);

      return result;
    } catch (ex) {
      print(ex);
      throw (ex);
    }
  }
}
