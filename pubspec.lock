# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      url: "https://pub.dartlang.org"
    source: hosted
    version: "22.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.18"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.2"
  another_flushbar:
    dependency: "direct main"
    description:
      name: another_flushbar
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.20"
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.4.2"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.1"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.8.2"
  auto_size_text:
    dependency: transitive
    description:
      name: auto_size_text
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0"
  base58check:
    dependency: transitive
    description:
      name: base58check
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  bech32:
    dependency: transitive
    description:
      name: bech32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.1"
  bloc:
    dependency: transitive
    description:
      name: bloc
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.2.1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  build:
    dependency: transitive
    description:
      name: build
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.6.3"
  build_config:
    dependency: transitive
    description:
      name: build_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.6"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.10"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.4"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.11.5"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.10"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.8.0"
  camera:
    dependency: "direct main"
    description:
      name: camera
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.10.0+3"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.10.2+3"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.10+2"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.4"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.1+1"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.1"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.5"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.7.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.15.0"
  connectivity:
    dependency: transitive
    description:
      name: connectivity
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.6"
  connectivity_for_web:
    dependency: transitive
    description:
      name: connectivity_for_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.0+1"
  connectivity_macos:
    dependency: transitive
    description:
      name: connectivity_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.1+2"
  connectivity_platform_interface:
    dependency: transitive
    description:
      name: connectivity_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.3+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.14"
  datetime_picker_formfield:
    dependency: "direct main"
    description:
      name: datetime_picker_formfield
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  dbus:
    dependency: transitive
    description:
      name: dbus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.3"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  device_info_plus_linux:
    dependency: transitive
    description:
      name: device_info_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  device_info_plus_macos:
    dependency: transitive
    description:
      name: device_info_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.3"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.6.1"
  device_info_plus_web:
    dependency: transitive
    description:
      name: device_info_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  device_info_plus_windows:
    dependency: transitive
    description:
      name: device_info_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  dio:
    dependency: "direct main"
    description:
      name: dio
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.6"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0+1"
  equatable:
    dependency: "direct main"
    description:
      name: equatable
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  expandable:
    dependency: "direct main"
    description:
      name: expandable
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.4"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.4"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.6.1"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      url: "https://pub.dartlang.org"
    source: hosted
    version: "10.1.6"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.23"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.1+14"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.8.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.5.3"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.2"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "14.3.0"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.16"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.17"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  fl_chart:
    dependency: "direct main"
    description:
      name: fl_chart
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.55.2"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_beautiful_popup:
    dependency: "direct main"
    description:
      name: flutter_beautiful_popup
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.0"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.3.3"
  flutter_cupertino_datetime_picker:
    dependency: "direct main"
    description:
      name: flutter_cupertino_datetime_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  flutter_datetime_picker:
    dependency: "direct main"
    description:
      name: flutter_datetime_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.1"
  flutter_expanded_tile:
    dependency: "direct main"
    description:
      name: flutter_expanded_tile
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.7"
  flutter_form_builder:
    dependency: "direct main"
    description:
      name: flutter_form_builder
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.2.0"
  flutter_hooks:
    dependency: "direct main"
    description:
      name: flutter_hooks
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      url: "https://pub.dartlang.org"
    source: hosted
    version: "10.0.0"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_mobx:
    dependency: "direct main"
    description:
      name: flutter_mobx
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0+2"
  flutter_multi_formatter:
    dependency: "direct main"
    description:
      name: flutter_multi_formatter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.11.16"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.22.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_udid:
    dependency: "direct main"
    description:
      name: flutter_udid
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.2.5"
  formz:
    dependency: "direct main"
    description:
      name: formz
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.1"
  get_it:
    dependency: transitive
    description:
      name: get_it
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.6.0"
  glob:
    dependency: transitive
    description:
      name: glob
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  google_maps_webservice:
    dependency: "direct main"
    description:
      name: google_maps_webservice
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.0.19"
  graphs:
    dependency: transitive
    description:
      name: graphs
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0"
  http:
    dependency: "direct main"
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.5"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.6+1"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.5+5"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.10"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.6+6"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.6.2"
  injectable:
    dependency: "direct main"
    description:
      name: injectable
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.4"
  injectable_generator:
    dependency: "direct dev"
    description:
      name: injectable_generator
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  io:
    dependency: transitive
    description:
      name: io
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.5"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.3"
  json_annotation:
    dependency: "direct main"
    description:
      name: json_annotation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.5.2"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  local_auth_android:
    dependency: transitive
    description:
      name: local_auth_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.17"
  local_auth_ios:
    dependency: transitive
    description:
      name: local_auth_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.12"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.6"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  logging:
    dependency: transitive
    description:
      name: logging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.11"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.3"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.0"
  mime:
    dependency: transitive
    description:
      name: mime
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  mobx:
    dependency: "direct main"
    description:
      name: mobx
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  nb_utils:
    dependency: "direct main"
    description:
      name: nb_utils
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  nested:
    dependency: transitive
    description:
      name: nested
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  open_file:
    dependency: "direct main"
    description:
      name: open_file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.1"
  package_config:
    dependency: transitive
    description:
      name: package_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.2"
  package_info_plus_linux:
    dependency: transitive
    description:
      name: package_info_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  package_info_plus_macos:
    dependency: transitive
    description:
      name: package_info_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  package_info_plus_web:
    dependency: transitive
    description:
      name: package_info_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.6"
  package_info_plus_windows:
    dependency: transitive
    description:
      name: package_info_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  path:
    dependency: "direct main"
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.1+1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.11"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.22"
  path_provider_ios:
    dependency: transitive
    description:
      name: path_provider_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.11"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.7"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  pattern_formatter:
    dependency: "direct main"
    description:
      name: pattern_formatter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.11.1"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      url: "https://pub.dartlang.org"
    source: hosted
    version: "10.2.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "10.3.6"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      url: "https://pub.dartlang.org"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.4.0"
  pin_code_fields:
    dependency: "direct main"
    description:
      name: pin_code_fields
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.4.0"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.7.3"
  pool:
    dependency: transitive
    description:
      name: pool
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.1"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: "direct main"
    description:
      name: provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.5"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.8"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  rflutter_alert:
    dependency: "direct main"
    description:
      name: rflutter_alert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  rxdart:
    dependency: "direct main"
    description:
      name: rxdart
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.27.7"
  scrollable_positioned_list:
    dependency: "direct main"
    description:
      name: scrollable_positioned_list
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.1"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.17"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.15"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  shelf:
    dependency: transitive
    description:
      name: shelf
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.2"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.4+1"
  signature:
    dependency: "direct main"
    description:
      name: signature
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.1"
  simple_gesture_detector:
    dependency: transitive
    description:
      name: simple_gesture_detector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.10+3"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  store_redirect:
    dependency: "direct main"
    description:
      name: store_redirect
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "19.4.56"
  syncfusion_flutter_datepicker:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_datepicker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "19.4.56"
  syncfusion_flutter_pdf:
    dependency: transitive
    description:
      name: syncfusion_flutter_pdf
      url: "https://pub.dartlang.org"
    source: hosted
    version: "19.4.56-beta"
  syncfusion_flutter_pdfviewer:
    dependency: "direct main"
    description:
      name: syncfusion_flutter_pdfviewer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "19.4.56-beta"
  syncfusion_flutter_pdfviewer_macos:
    dependency: transitive
    description:
      name: syncfusion_flutter_pdfviewer_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "19.4.56-beta"
  syncfusion_flutter_pdfviewer_platform_interface:
    dependency: transitive
    description:
      name: syncfusion_flutter_pdfviewer_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "19.4.56-beta"
  syncfusion_flutter_pdfviewer_web:
    dependency: transitive
    description:
      name: syncfusion_flutter_pdfviewer_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "19.4.56-beta"
  table_calendar:
    dependency: "direct main"
    description:
      name: table_calendar
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.8"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.8"
  timeago:
    dependency: "direct main"
    description:
      name: timeago
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.0"
  timezone:
    dependency: transitive
    description:
      name: timezone
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.8.0"
  timing:
    dependency: transitive
    description:
      name: timing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.1+3"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.8"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.23"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.18"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.14"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  uuid:
    dependency: transitive
    description:
      name: uuid
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  watcher:
    dependency: transitive
    description:
      name: watcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.5.2"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+3"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.3.1"
  yaml:
    dependency: transitive
    description:
      name: yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
sdks:
  dart: ">=2.16.0 <3.0.0"
  flutter: ">=2.10.5"
