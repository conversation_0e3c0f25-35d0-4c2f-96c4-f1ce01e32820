<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>VCC <PERSON>ồ sơ điện tử</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>NSCameraUsageDescription</key>
    <string>VCC <PERSON>ồ sơ điện tử yêu cầu quyền truy cập vào máy ảnh</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>VC<PERSON> <PERSON><PERSON> sơ điện tử yêu cầu quyền truy cập và<PERSON> thư viện</string>
    <key>NSAppleMusicUsageDescription</key>
    <string>This permission is not needed by the app, but it is required by an underlying API. If you see this dialog, contact us.</string>
    <key>NSCalendarsUsageDescription</key>
    <string>VCC Hồ sơ điện tử need calendars access for create events feature &amp;amp;amp; better user experience</string>
   	<key>NSContactsUsageDescription</key>
   	<string>VCC Hồ sơ điện tử need contacts access for finding product &amp;amp;amp; chatting between users</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
   	<string>Ứng dụng cần quyền truy cập vào vị trí khi mở và trong nền. Để lấy vị trí đại lý hoặc công trình.</string>
   	<key>NSLocationAlwaysUsageDescription</key>
   	<string>Ứng dụng cần quyền truy cập vị trí để lấy vị trí đại lý hoặc công trình.</string>
   	<key>NSLocationUsageDescription</key>
   	<string>Ứng dụng cần quyền truy cập vị trí để lấy vị trí đại lý hoặc công trình.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
   	<string>Ứng dụng cần quyền truy cập vị trí để lấy vị trí đại lý hoặc công trình.</string>
   	<key>NSMicrophoneUsageDescription</key>
   	<string>Can I use the mic please?</string>
   	<key>NSMotionUsageDescription</key>
  	<string>VCC Hồ sơ điện tử motion use for better user experience</string>
   	<key>NSSpeechRecognitionUsageDescription</key>
   	<string>VCC Hồ sơ điện tử speech use for better user experience</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Tuyển dụng</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
    <true/>
    <key>UIFileSharingEnabled</key>
    <true/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>NSFaceIDUsageDescription</key>
    <string>Hồ sơ điện tử yêu cầu xác thực sinh trắc học để đăng nhập</string>
</dict>
</plist>
