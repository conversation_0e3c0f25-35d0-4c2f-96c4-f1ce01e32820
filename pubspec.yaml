name: hstd
description: <PERSON><PERSON> <PERSON><PERSON> đ<PERSON> tử

publish_to: 'none'

version: 1.0.77+66

environment:
  sdk: ">=2.10.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

  flutter_hooks: ^0.17.0
  flutter_bloc: ^7.3.0
  flutter_form_builder: ^6.1.0+1
  pattern_formatter: ^2.0.0
  flutter_multi_formatter: ^2.3.8
  flutter_beautiful_popup: ^1.7.0
  rflutter_alert: ^2.0.4
  http: ^0.13.3
  equatable: ^2.0.3
  rxdart: ^0.27.2
  shared_preferences: ^2.0.8
  formz: ^0.4.0
  cupertino_icons: ^1.0.3
  flutter_svg: ^0.22.0
  provider: ^6.0.1
  fluttertoast:
  url_launcher: ^6.0.10
  flutter_cupertino_datetime_picker: ^2.0.1
  signature: ^4.1.1
  syncfusion_flutter_pdfviewer: ^19.3.44-beta
  intl: ^0.17.0
  path_provider:
  datetime_picker_formfield: ^2.0.0
  pin_code_fields: ^7.4.0
  flutter_expanded_tile: ^0.3.7
  flutter_datetime_picker: ^1.5.1
  timeago: ^3.0.2
  expandable: ^4.1.4
  permission_handler: 10.2.0
  another_flushbar: 1.10.20
  open_file: ^3.1.0
  package_info_plus: ^1.0.0
  store_redirect: ^2.0.0
  dotted_border: ^2.0.0
  file_picker: ^4.6.1
#  geolocator: ^10.1.0
  google_maps_webservice: 0.0.19
  flutter_local_notifications: ^10.0.0
  local_auth:
  flutter_udid:
  device_info_plus:
  firebase_analytics:
  firebase_messaging:
  firebase_core:
  scrollable_positioned_list:
#  audio_service: ^0.18.9
#  just_audio: ^0.9.32
#  background_location: ^0.11.8

  #Defines the annotations used by json_serializable to create code for JSON serialization and deserialization.
  #See the example to understand how to configure your package
  json_annotation: ^3.1.1

  # A Flutter plugin for iOS and Android for picking images from the image library, and taking new pictures with the camera.
  image_picker: ^0.8.5+3

  # Supercharge the state-management in your Dart apps with Transparent Functional Reactive Programming (TFRP)
  mobx: ^2.0.7

  # A Flutter plugin for iOS, Android and Web allowing access to the device cameras.
  camera: ^0.10.0+1

  # Provides the Observer widget that listens to observables and automatically rebuilds on changes.
  flutter_mobx: ^2.0.6+4

  # A comprehensive, cross-platform path manipulation library for Dart.
  path: ^1.8.0
    # Readme
    #Changelog
    #Example
    #Installing
    #Versions
    #Scores
    #Language: English | 中文简体
    #
    #dio
  #Pub support
  #A powerful Http client for Dart, which supports Interceptors, Global configuration, FormData, Request Cancellation, File downloading, Timeout etc.
  dio: ^4.0.6

  # A carousel slider widget.
  carousel_slider: ^4.1.1
  injectable: ^1.5.4

  nb_utils: 4.0.0
  table_calendar: ^3.0.8
  fl_chart:

dev_dependencies:
  flutter_test:
    sdk: flutter


  #The build_runner package provides a concrete way of generating files using Dart code, outside of tools like pub.
  #Unlike pub serve/build, files are always generated directly on disk, and rebuilds are incremental - inspired by tools such as Bazel.
  build_runner: ^1.10.1

  #Provides Dart Build System builders for handling JSON.
  json_serializable: ^3.5.1

  #A generator for injectable library.
  injectable_generator: ^1.0.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/login_bg.png
    - assets/logo.svg
    - assets/logo_old.svg
    - assets/viettel_construction.png
    - assets/
    - assets/vectors/
    - assets/images/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SFProText
      fonts:
#        - asset: assets/fonts/sf-pro-text-bold.ttf
#        - asset: assets/fonts/sf-pro-text-heavy.ttf
        - asset: fonts/sf-pro-text-medium.ttf
#        - asset: assets/fonts/sf-pro-text-regular.ttf
#        - asset: assets/fonts/sf-pro-text-semibold.ttf

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages